# Extra dependencies not included in the main Chromium source archive
# For now, the following are from the top level DEPS file which are needed for building to work
# Check downloads-x86-64.ini and downloads-arm64.ini for the architecture specific dependencies

# Uses configparser.BasicInterpolation interpolation

# Google Toolbox for Mac, which is macOS-specific code needed for building
[google-toolbox-for-mac]
version = 3.0.0
url = https://github.com/google/google-toolbox-for-mac/archive/v%(version)s.tar.gz
download_filename = google-toolbox-for-mac-v%(version)s.tar.gz
strip_leading_dirs = google-toolbox-for-mac-%(version)s
sha512 = 18e1e8d91869f82c1b4582c60e191a6f946dd9958f1e1279d86259d45589fbceec636f75f939e96b6a85a2fa457d4df2e6b143b44d21feab21700309addca575
output_path = third_party/google_toolbox_for_mac/src
