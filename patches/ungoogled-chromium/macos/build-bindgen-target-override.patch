--- a/tools/rust/build_bindgen.py
+++ b/tools/rust/build_bindgen.py
@@ -173,11 +173,18 @@ def main():
         '--skip-checkout',
         action='store_true',
         help='skip downloading the git repo. Useful for trying local changes')
+    # The extra argument that assign a Rust target instead of the host target.
+    parser.add_argument(
+        '--rust-target',
+        type=str,
+        default=RustTargetTriple(),
+        help='The Rust target triple to build bindgen for')
     args, rest = parser.parse_known_args()
 
     if not args.skip_checkout:
         CheckoutGitRepo("bindgen", BINDGEN_GIT_REPO, BINDGEN_GIT_VERSION,
                         BINDGEN_SRC_DIR)
+    rust_target = args.rust_target
 
     build_dir = BINDGEN_HOST_BUILD_DIR
     if os.path.exists(build_dir):
@@ -189,7 +196,7 @@ def main():
         'build',
         f'--manifest-path={BINDGEN_SRC_DIR}/Cargo.toml',
         f'--target-dir={build_dir}',
-        f'--target={RustTargetTriple()}',
+        f'--target={rust_target}',
         f'--no-default-features',
         f'--features=logging' + static_feature,
         '--release',
@@ -203,7 +210,7 @@ def main():
 
     llvm_dir = os.path.join(THIRD_PARTY_DIR, 'llvm-build', 'Release+Asserts')
     shutil.copy(
-        os.path.join(build_dir, RustTargetTriple(), 'release',
+        os.path.join(build_dir, rust_target, 'release',
                      f'bindgen{EXE}'), os.path.join(install_dir, 'bin'))
     if sys.platform == 'win32':
         shutil.copy(os.path.join(llvm_dir, 'bin', f'libclang.dll'),
--- a/build/rust/rust_bindgen_generator.gni
+++ b/build/rust/rust_bindgen_generator.gni
@@ -177,7 +177,7 @@ template("rust_bindgen_generator") {
       ]
     }
 
-    if (is_linux) {
+    if (true) {
       # Linux clang, and clang libs, use a shared libstdc++, which we must
       # point to.
       args += [
