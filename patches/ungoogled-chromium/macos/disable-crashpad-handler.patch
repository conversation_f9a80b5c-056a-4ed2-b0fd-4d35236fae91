# Disables crashpad_handler process

--- a/third_party/crashpad/crashpad/client/crashpad_client_mac.cc
+++ b/third_party/crashpad/crashpad/client/crashpad_client_mac.cc
@@ -138,61 +138,7 @@ class HandlerStarter final : public Noti
       const std::map<std::string, std::string>& annotations,
       const std::vector<std::string>& arguments,
       bool restartable) {
-    base::apple::ScopedMachReceiveRight receive_right(
-        NewMachPort(MACH_PORT_RIGHT_RECEIVE));
-    if (!receive_right.is_valid()) {
-      return base::apple::ScopedMachSendRight();
-    }
-
-    mach_port_t port;
-    mach_msg_type_name_t right_type;
-    kern_return_t kr = mach_port_extract_right(mach_task_self(),
-                                               receive_right.get(),
-                                               MACH_MSG_TYPE_MAKE_SEND,
-                                               &port,
-                                               &right_type);
-    if (kr != KERN_SUCCESS) {
-      MACH_LOG(ERROR, kr) << "mach_port_extract_right";
-      return base::apple::ScopedMachSendRight();
-    }
-    base::apple::ScopedMachSendRight send_right(port);
-    DCHECK_EQ(port, receive_right.get());
-    DCHECK_EQ(right_type,
-              implicit_cast<mach_msg_type_name_t>(MACH_MSG_TYPE_PORT_SEND));
-
-    std::unique_ptr<HandlerStarter> handler_restarter;
-    if (restartable) {
-      handler_restarter.reset(new HandlerStarter());
-      if (!handler_restarter->notify_port_.is_valid()) {
-        // This is an error that NewMachPort() would have logged. Proceed anyway
-        // without the ability to restart.
-        handler_restarter.reset();
-      }
-    }
-
-    if (!CommonStart(handler,
-                     database,
-                     metrics_dir,
-                     url,
-                     annotations,
-                     arguments,
-                     std::move(receive_right),
-                     handler_restarter.get(),
-                     false)) {
-      return base::apple::ScopedMachSendRight();
-    }
-
-    if (handler_restarter &&
-        handler_restarter->StartRestartThread(
-            handler, database, metrics_dir, url, annotations, arguments)) {
-      // The thread owns the object now.
-      std::ignore = handler_restarter.release();
-    }
-
-    // If StartRestartThread() failed, proceed without the ability to restart.
-    // handler_restarter will be released when this function returns.
-
-    return send_right;
+    return base::apple::ScopedMachSendRight();
   }
 
   // NotifyServer::DefaultInterface:
@@ -463,24 +409,7 @@ bool CrashpadClient::StartHandler(
   // Attachments are not implemented on MacOS yet.
   DCHECK(attachments.empty());
 
-  // The “restartable” behavior can only be selected on OS X 10.10 and later. In
-  // previous OS versions, if the initial client were to crash while attempting
-  // to restart the handler, it would become an unkillable process.
-  base::apple::ScopedMachSendRight exception_port(HandlerStarter::InitialStart(
-      handler,
-      database,
-      metrics_dir,
-      url,
-      annotations,
-      arguments,
-      restartable && (__MAC_OS_X_VERSION_MIN_REQUIRED >= __MAC_10_10 ||
-                      MacOSVersionNumber() >= 10'10'00)));
-  if (!exception_port.is_valid()) {
-    return false;
-  }
-
-  SetHandlerMachPort(std::move(exception_port));
-  return true;
+  return false;
 }
 
 bool CrashpadClient::SetHandlerMachService(const std::string& service_name) {
