--- a/tools/rust/build_bindgen.py
+++ b/tools/rust/build_bindgen.py
@@ -29,8 +29,7 @@ from update import (RmTree)
 # The git hash to use.  See https://github.com/rust-lang/rust-bindgen/tags.
 # The current hash below corresponds to version 0.72.0
 BINDGEN_GIT_VERSION = 'd0e7d6b5b763e93dd38f9ece05230979ede95a0a'
-BINDGEN_GIT_REPO = ('https://chromium.googlesource.com/external/' +
-                    'github.com/rust-lang/rust-bindgen')
+BINDGEN_GIT_REPO = ('https://github.com/rust-lang/rust-bindgen')
 
 BINDGEN_SRC_DIR = os.path.join(THIRD_PARTY_DIR, 'rust-toolchain-intermediate',
                                'bindgen-src')
@@ -103,15 +102,8 @@ def RunCargo(cargo_args):
               f'the build_rust.py script builds rustc that is needed here.')
         sys.exit(1)
 
-    clang_bins_dir = os.path.join(RUST_HOST_LLVM_INSTALL_DIR, 'bin')
-    llvm_dir = RUST_HOST_LLVM_INSTALL_DIR
-
-    if not os.path.exists(os.path.join(llvm_dir, 'bin', f'llvm-config{EXE}')):
-        print(f'Missing llvm-config in {llvm_dir}. This '
-              f'script expects to be run after build_rust.py is run as '
-              f'the build_rust.py script produces the LLVM libraries that '
-              f'are needed here.')
-        sys.exit(1)
+    clang_bins_dir = os.path.join(THIRD_PARTY_DIR, 'llvm-build', 'Release+Asserts', 'bin')
+    llvm_dir = os.path.join(THIRD_PARTY_DIR, 'llvm-build', 'Release+Asserts')
 
     env = collections.defaultdict(str, os.environ)
     # Cargo normally stores files in $HOME. Override this.
@@ -209,7 +201,7 @@ def main():
     install_dir = os.path.join(RUST_TOOLCHAIN_OUT_DIR)
     print(f'Installing bindgen to {install_dir} ...')
 
-    llvm_dir = RUST_HOST_LLVM_INSTALL_DIR
+    llvm_dir = os.path.join(THIRD_PARTY_DIR, 'llvm-build', 'Release+Asserts')
     shutil.copy(
         os.path.join(build_dir, RustTargetTriple(), 'release',
                      f'bindgen{EXE}'), os.path.join(install_dir, 'bin'))
