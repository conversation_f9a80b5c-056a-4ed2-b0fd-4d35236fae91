--- a/third_party/blink/renderer/bindings/scripts/bind_gen/style_format.py
+++ b/third_party/blink/renderer/bindings/scripts/bind_gen/style_format.py
@@ -49,21 +49,16 @@ def init(root_src_dir, enable_style_form
     new_buildtools_platform_dir = os.path.join(
         root_src_dir, "buildtools", platform + new_path_platform_suffix)
 
-    # TODO(b/328065301): Remove old paths once clang hooks are migrated
-    # //buildtools/<platform>/clang-format
-    possible_paths = [
-        os.path.join(buildtools_platform_dir,
-                     "clang-format{}".format(exe_suffix)),
-        # //buildtools/<platform>/format/clang-format
-        os.path.join(new_buildtools_platform_dir, "format",
-                     "clang-format{}".format(exe_suffix)),
-        # //buildtools/<platform>-format/clang-format
-        os.path.join(f"{new_buildtools_platform_dir}-format",
-                     "clang-format{}".format(exe_suffix)),
-    ]
-    for path in possible_paths:
-        if os.path.isfile(path):
-            _clang_format_command_path = path
+    _clang_format_command_path = ""
+    try:
+        # try to get system clang-format
+        _clang_format_command_path = subprocess.check_output(
+            ["which", "clang-format"]).strip()
+    except subprocess.CalledProcessError:
+        # otherwise, use the bundled clang-format
+        _clang_format_command_path = os.path.join(
+            root_src_dir, "third_party/llvm-build/Release+Asserts/bin",
+            "clang-format{}".format(exe_suffix))
 
     # //buildtools/<platform>/gn
     _gn_command_path = os.path.join(buildtools_platform_dir,
