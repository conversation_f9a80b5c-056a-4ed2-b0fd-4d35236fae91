--- a/third_party/libpng/BUILD.gn
+++ b/third_party/libpng/BUILD.gn
@@ -109,6 +109,12 @@ source_set("libpng_sources") {
     cflags += [ "/wd4146" ]
   }
 
+  if (is_apple) {
+    # TODO(crbug.com/41492875): this can be removed once libpng is updated to include
+    # https://github.com/pnggroup/libpng/commit/893b8113f04d408cc6177c6de19c9889a48faa24
+    cflags += [ "-fno-define-target-os-macros" ]
+  }
+
   if (is_win && is_component_build) {
     defines += [ "PNG_BUILD_DLL" ]
   }
