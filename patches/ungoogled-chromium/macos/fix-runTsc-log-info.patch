--- a/third_party/devtools-frontend/src/scripts/build/typescript/ts_library.py
+++ b/third_party/devtools-frontend/src/scripts/build/typescript/ts_library.py
@@ -52,7 +52,7 @@ logging.basicConfig(
 
 def runTsc(tsconfig_location):
     cmd = [NODE_LOCATION, TSC_LOCATION, '-p', tsconfig_location]
-    logging.info("runTsc: %s", ' '.join(cmd))
+    logging.info("runTsc: %s", ' '.join(str(cmd)))
     process = subprocess.Popen(cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
