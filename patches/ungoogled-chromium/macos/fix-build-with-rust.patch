--- a/build/config/clang/BUILD.gn
+++ b/build/config/clang/BUILD.gn
@@ -292,8 +292,6 @@ clang_lib("compiler_builtins") {
     } else {
       assert(false, "unsupported target_platform=$target_platform")
     }
-  } else if (current_cpu != "ppc64" && current_cpu != "s390x") {
-    libname = "builtins"
   }
 }
 
--- a/build/config/rust.gni
+++ b/build/config/rust.gni
@@ -52,7 +52,7 @@ declare_args() {
   # To use a custom toolchain instead, specify an absolute path to the root of
   # a Rust sysroot, which will have a 'bin' directory and others. Commonly
   # <home dir>/.rustup/toolchains/nightly-<something>-<something>
-  rust_sysroot_absolute = ""
+  rust_sysroot_absolute = "//third_party/rust-toolchain"
 
   # Directory under which to find `bin/bindgen` (a `bin` directory containing
   # the bindgen exectuable).
@@ -62,7 +62,7 @@ declare_args() {
   # set this to the output of `rustc -V`. Changing this string will cause all
   # Rust targets to be rebuilt, which allows you to update your toolchain and
   # not break incremental builds.
-  rustc_version = ""
+  rustc_version = "rustc 1.89.0-nightly (be19eda0d 2025-06-22)"
 
   # Whether artifacts produced by the Rust compiler can participate in ThinLTO.
   #
