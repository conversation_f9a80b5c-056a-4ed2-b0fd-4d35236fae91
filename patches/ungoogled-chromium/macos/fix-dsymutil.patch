--- a/tools/mac/dump-static-initializers.py
+++ b/tools/mac/dump-static-initializers.py
@@ -41,7 +41,7 @@ def ParseDsymutil(binary):
   static initializers.
   """
 
-  child = subprocess.Popen(['tools/clang/dsymutil/bin/dsymutil', '-s', binary],
+  child = subprocess.Popen(['/usr/bin/dsymutil', '-s', binary],
      stdout=subprocess.PIPE)
   for line in child.stdout:
     file_match = dsymutil_file_re.search(line)
--- a/build/toolchain/apple/toolchain.gni
+++ b/build/toolchain/apple/toolchain.gni
@@ -232,8 +232,9 @@ template("single_apple_toolchain") {
     if (_enable_dsyms) {
       dsym_switch = " -Wcrl,dsym,{{root_out_dir}} "
       dsym_switch += "-Wcrl,dsymutilpath," +
-                     rebase_path("//tools/clang/dsymutil/bin/dsymutil",
-                                 root_build_dir) + " "
+                     "/usr/bin/dsymutil "
+#                     rebase_path("//tools/clang/dsymutil/bin/dsymutil",
+#                                 root_build_dir) + " "
 
       dsym_output_dir =
           "{{root_out_dir}}/{{target_output_name}}{{output_extension}}.dSYM"
