# Default Settings

Vanilla Chromium settings are not often changed by ungoogled-chromium, however there are some exceptions.  
Below is a list of the documented changes to the default Chromium settings.

Setting | New State | Location
-- | -- | --
Allow sites to check if you have payment methods saved | Disabled | chrome://settings/payments
Ask where to save each file before downloading | Enabled | chrome://settings/downloads
Auto sign-in | Disabled | chrome://settings/passwords
Block third-party cookies | Enabled | chrome://settings/cookies
Continue running background apps when Chromium is closed | Disabled | chrome://settings/system
Hyperlink auditing (\<a ping>) | Disabled | NA
Link Doctor | Disabled | NA
Offer to save passwords | Disabled | chrome://settings/passwords
Payment autofill | Disabled | chrome://settings/payments
Preload pages | Disabled | chrome://settings/cookies
Search suggestions | Disabled | chrome://settings/syncSetup
Show bookmark bar | Enabled | chrome://settings/appearance
WebRTC IP handling policy | Disable Non-Proxied UDP | `--webrtc-ip-handling-policy`
