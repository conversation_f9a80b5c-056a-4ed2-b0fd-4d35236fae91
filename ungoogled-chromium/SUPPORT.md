# Support

**Before you submit feedback, please ensure you have tried the following**: 

* Read the [FAQ](https://ungoogled-software.github.io/ungoogled-chromium-wiki/faq)
* If you are using a build for an officially-supported platform ([see the list here](docs/platforms.md)), then please refer to the [Platform-specific Support](#platform-specific-support) section first.
* Check if your feedback already exists in the [Issue Tracker](https://github.com/ungoogled-software/ungoogled-chromium/issues) (make sure to search closed issues and use search filters, as applicable)
* If this is a problem, ensure it does *not* occur with regular Chromium or Google Chrome. If it does, then this is *not* a problem with ungoogled-chromium. Instead, please submit your feedback to the [Chromium bug tracker](https://bugs.chromium.org/p/chromium/issues/list) or Google.
* Read the documentation under [docs/](docs/)

There are a few channels for support:

* An issue tracker. Issue trackers are the main hubs for discussions and development activity, and thus the primary means of support. They includes problems, suggestions, and questions. If you are using a build for an officially-supported platform ([see the list here](docs/platforms.md)), then please refer to the [Platform-specific Support](#platform-specific-support) section to find its issue tracker. Otherwise, please use [the main issue tracker](https://github.com/ungoogled-software/ungoogled-chromium/issues). 
* A chat room. There are two options available:
    * [Gitter](https://gitter.im/ungoogled-software/Lobby). It can use your GitHub account as an identity.
    * Matrix.org under name `ungoogled-software/lobby`. It has a bidirectional connection with Gitter.

## Platform-specific Support

For officially-supported platforms, **please visit their issue trackers before using the issue tracker in this repository**:

* Android: [Issue tracker](https://github.com/ungoogled-software/ungoogled-chromium-android/issues)
* Arch Linux and derivatives (e.g. Manjaro): [Issue tracker](https://github.com/ungoogled-software/ungoogled-chromium-archlinux/issues)
* Debian & Ubuntu: [Issue tracker](https://github.com/ungoogled-software/ungoogled-chromium-debian/issues)
* Fedora & CentOS: [Issue tracker](https://github.com/ungoogled-software/ungoogled-chromium-fedora/issues)
* Flatpak: [Issue tracker](https://github.com/ungoogled-software/ungoogled-chromium-flatpak/issues)
* Gentoo ([@PF4Public](https://github.com/PF4Public)'s overlay): [Issue tracker](https://github.com/PF4Public/gentoo-overlay/issues?q=is%3Aissue++ungoogled-chromium+)
* macOS: [Issue tracker](https://github.com/ungoogled-software/ungoogled-chromium-macos/issues)
* Portable Linux: [Issue tracker](https://github.com/ungoogled-software/ungoogled-chromium-portablelinux/issues)
* Windows: [Issue tracker](https://github.com/ungoogled-software/ungoogled-chromium-windows/issues)
