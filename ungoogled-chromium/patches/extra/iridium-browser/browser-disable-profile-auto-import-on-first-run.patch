From 7134d5fd762237ad2d80093b68ccbd1582476640 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 25 Jun 2015 15:51:59 +0200
Subject: [PATCH 56/66] browser: disable profile auto-import on first run

--- a/chrome/browser/chrome_browser_main.cc
+++ b/chrome/browser/chrome_browser_main.cc
@@ -1588,11 +1588,6 @@ int ChromeBrowserMainParts::PreMainMessa
   // and preferences have been registered since some of the import code depends
   // on preferences.
   if (first_run::IsChromeFirstRun()) {
-    // `profile` may be nullptr even on first run, for example when the
-    // "BrowserSignin" policy is set to "Force". If so, skip the auto import.
-    if (profile) {
-      first_run::AutoImport(profile, master_prefs_->import_bookmarks_path);
-    }
 
     // Note: This can pop-up the first run consent dialog on Linux & Mac.
     first_run::DoPostImportTasks(master_prefs_->make_chrome_default_for_user);
