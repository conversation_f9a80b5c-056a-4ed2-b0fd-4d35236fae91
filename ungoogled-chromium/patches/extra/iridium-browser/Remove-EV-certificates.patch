From d32e222a2706cb59f9855b9cf4330f88d1af5435 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 2 Apr 2015 12:44:23 +0200
Subject: [PATCH 41/66] Remove EV certificates

The team chose to let EV certificates appear just like normal
certificates. The web of trust is considered a failure in itself, so
do not give users a false sense of extra security with EV certs.
Instead, let them appear just like regular ones.

--- a/net/cert/ev_root_ca_metadata.cc
+++ b/net/cert/ev_root_ca_metadata.cc
@@ -36,7 +36,17 @@ struct EVMetadata {
   const std::string_view policy_oids[kMaxOIDsPerCA];
 };
 
-#include "net/data/ssl/chrome_root_store/chrome-ev-roots-inc.cc"
+static const EVMetadata kEvRootCaMetadata[] = {
+    // need some dummy thing to make compiler happy, because
+    // arraysize() is implemented as a convoluted template rather than
+    // the traditional sizeof(x)/sizeof(*x)
+    { { { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
+          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff } },
+      {
+        "0",
+      }
+    }
+};
 
 #endif  // defined(PLATFORM_USES_CHROMIUM_EV_METADATA)
 }  // namespace
