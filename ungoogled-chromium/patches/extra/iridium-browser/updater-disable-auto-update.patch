From f97af1715c10c5926169ff317ca7c91f1d073af9 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 13 Feb 2015 00:59:04 +0100
Subject: [PATCH 39/66] updater: disable auto-update

As per http://www.chromium.org/administrators/turning-off-auto-updates ,
the auto update function is decidedly disabled on Linux, i.e.
the following patch is for Windows and MacOS.

For Windows, all we need is to build without -DGOOGLE_CHROME_BUILD (cf.
chrome/installer/util/google_update_settings.cc), which may already be
the case anyway, since we are based off Chromium, not Chrome.
---
 chrome/browser/app_controller_mac.mm | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/chrome/browser/app_controller_mac.mm
+++ b/chrome/browser/app_controller_mac.mm
@@ -1076,7 +1076,7 @@ class AppControllerNativeThemeObserver :
   CFStringRef checkInterval = CFSTR("checkInterval");
   CFPropertyListRef plist = CFPreferencesCopyAppValue(checkInterval, app);
   if (!plist) {
-    const float fiveHoursInSeconds = 5.0 * 60.0 * 60.0;
+    const float fiveHoursInSeconds = 0.0;
     CFPreferencesSetAppValue(
         checkInterval, base::apple::NSToCFPtrCast(@(fiveHoursInSeconds)), app);
     CFPreferencesAppSynchronize(app);
