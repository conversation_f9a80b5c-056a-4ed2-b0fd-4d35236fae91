From d3dcad96b3c2091026c3a81054bb3ce56538a702 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 29 Jan 2015 10:46:40 +0100
Subject: [PATCH 16/66] mime_util: force text/x-suse-ymp to be downloaded

YMP files (YaST One Click Install) are plaintext XML, but also not
very interesting in themselves. Force them to be stored.
---
 components/mime_util/mime_util.cc | 1 +
 1 file changed, 1 insertion(+)

--- a/third_party/blink/common/mime_util/mime_util.cc
+++ b/third_party/blink/common/mime_util/mime_util.cc
@@ -88,6 +88,7 @@ constexpr auto kUnsupportedTextTypes =
         "text/x-csv",
         "text/x-vcf",
         "text/rtf",
+        "text/x-suse-ymp",
         "text/comma-separated-values",
         "text/csv",
         "text/tab-separated-values",
