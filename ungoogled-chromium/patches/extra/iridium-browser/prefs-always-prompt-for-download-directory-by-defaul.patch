From 93010fd16c1c9f01a06eab18055bcab54b028cc8 Mon Sep 17 00:00:00 2001
From: <PERSON> <jeng<PERSON><EMAIL>>
Date: Fri, 13 Feb 2015 01:04:21 +0100
Subject: [PATCH 28/66] prefs: always prompt for download directory by default

If the user opens a link, one of two things might happen. The URI
resolves to a "web page" and will be displayed, then everything is
good. Or it has a MIME type like application/* or so that causes the
browser to consider it a file download instead.

In that case, one of two things might happen. The browser may display
a dialog box asking explicitly for an action, then everything is
good. Or it does not and instead readily stores the file on disk.

(Modern browsers also download the file in the background when waiting
for the dialog box confirmation, but that is a separate tuning knob.)

When the file is chosen to be immediately stored on disk without user
interaction, the visual feedback for this may be rather miniscule.

And then, you have to open a terminal or file manager again just to
move the file to the location you wanted it to be in in the first
place.

TLDR: Do not let files sneak onto the disk too easily.
---
 chrome/browser/download/download_prefs.cc | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/chrome/browser/download/download_prefs.cc
+++ b/chrome/browser/download/download_prefs.cc
@@ -284,7 +284,7 @@ void DownloadPrefs::RegisterProfilePrefs
     user_prefs::PrefRegistrySyncable* registry) {
   registry->RegisterBooleanPref(
       prefs::kPromptForDownload,
-      false,
+      true,
       user_prefs::PrefRegistrySyncable::SYNCABLE_PREF);
   registry->RegisterStringPref(prefs::kDownloadExtensionsToOpen, std::string());
   registry->RegisterListPref(prefs::kDownloadExtensionsToOpenByPolicy, {});
