From: csagan5 <<EMAIL>>
Date: Sun, 8 Jul 2018 22:42:04 +0200
Subject: Add flag to configure maximum connections per host

With the introduction of this flag it is possible to increase the maximum
allowed connections per host; this can however be detrimental to devices
with limited CPU/memory resources and it is disabled by default.
---
 chrome/browser/about_flags.cc                            |  8 ++++++++
 chrome/browser/flag_descriptions.cc                      |  4 ++++
 chrome/browser/flag_descriptions.h                       |  3 +++
 .../common/network_features.cc                           |  3 +++
 .../common/network_features.h                            |  4 ++++
 .../common/network_switch_list.h                         |  4 ++++
 net/socket/client_socket_pool_manager.cc                 | 16 ++++++++++++++++
 7 files changed, 42 insertions(+)

--- a/chrome/browser/BUILD.gn
+++ b/chrome/browser/BUILD.gn
@@ -2295,6 +2295,7 @@ static_library("browser") {
     "//components/net_log",
     "//components/network_hints/common:mojo_bindings",
     "//components/network_session_configurator/browser",
+    "//components/network_session_configurator/common",
     "//components/network_time",
     "//components/network_time/time_tracker",
     "//components/no_state_prefetch/browser",
--- a/chrome/browser/bromite_flag_choices.h
+++ b/chrome/browser/bromite_flag_choices.h
@@ -4,4 +4,8 @@
 
 #ifndef CHROME_BROWSER_BROMITE_FLAG_CHOICES_H_
 #define CHROME_BROWSER_BROMITE_FLAG_CHOICES_H_
+const FeatureEntry::Choice kMaxConnectionsPerHostChoices[] = {
+    {features::kMaxConnectionsPerHostChoiceDefault, "", ""},
+    {features::kMaxConnectionsPerHostChoice15, switches::kMaxConnectionsPerHost, "15"},
+};
 #endif  // CHROME_BROWSER_BROMITE_FLAG_CHOICES_H_
--- a/chrome/browser/bromite_flag_entries.h
+++ b/chrome/browser/bromite_flag_entries.h
@@ -12,4 +12,8 @@
      "Enable Canvas::measureText() fingerprint deception",
      "Scale the output values of Canvas::measureText() with a randomly selected factor in the range -0.0003% to 0.0003%, which are recomputed on every document initialization. ungoogled-chromium flag, Bromite feature.",
      kOsAll, SINGLE_VALUE_TYPE(switches::kFingerprintingCanvasMeasureTextNoise)},
+    {"max-connections-per-host",
+     flag_descriptions::kMaxConnectionsPerHostName,
+     flag_descriptions::kMaxConnectionsPerHostDescription,
+     kOsAll, MULTI_VALUE_TYPE(kMaxConnectionsPerHostChoices)},
 #endif  // CHROME_BROWSER_BROMITE_FLAG_ENTRIES_H_
--- a/chrome/browser/browser_process_impl.cc
+++ b/chrome/browser/browser_process_impl.cc
@@ -26,6 +26,7 @@
 #include "base/functional/callback.h"
 #include "base/functional/callback_helpers.h"
 #include "base/location.h"
+#include "base/logging.h"
 #include "base/memory/ptr_util.h"
 #include "base/metrics/histogram_functions.h"
 #include "base/metrics/histogram_macros.h"
@@ -33,6 +34,7 @@
 #include "base/notreached.h"
 #include "base/path_service.h"
 #include "base/run_loop.h"
+#include "base/strings/string_number_conversions.h"
 #include "base/synchronization/waitable_event.h"
 #include "base/task/sequenced_task_runner.h"
 #include "base/task/single_thread_task_runner.h"
@@ -119,6 +121,7 @@
 #include "components/metrics/metrics_service.h"
 #include "components/metrics_services_manager/metrics_services_manager.h"
 #include "components/metrics_services_manager/metrics_services_manager_client.h"
+#include "components/network_session_configurator/common/network_switches.h"
 #include "components/network_time/network_time_tracker.h"
 #include "components/os_crypt/async/browser/os_crypt_async.h"
 #include "components/permissions/permissions_client.h"
@@ -153,6 +156,7 @@
 #include "extensions/common/constants.h"
 #include "media/media_buildflags.h"
 #include "mojo/public/cpp/bindings/pending_receiver.h"
+#include "net/socket/client_socket_pool_manager.h"
 #include "printing/buildflags/buildflags.h"
 #include "services/network/public/cpp/features.h"
 #include "services/network/public/cpp/network_switches.h"
@@ -421,6 +425,18 @@ void BrowserProcessImpl::Init() {
   pref_change_registrar_.Add(metrics::prefs::kMetricsReportingEnabled,
                              base::BindRepeating(&ApplyMetricsReportingPolicy));
 
+  int max_connections_per_host = 0;
+  auto switch_value = base::CommandLine::ForCurrentProcess()->GetSwitchValueASCII(
+      switches::kMaxConnectionsPerHost);
+  if (!switch_value.empty() && !base::StringToInt(switch_value, &max_connections_per_host)) {
+    LOG(DFATAL) << "--" << switches::kMaxConnectionsPerHost
+      << " expected integer; got (\"" << switch_value << "\" instead)";
+  }
+  if (max_connections_per_host != 0) {
+    net::ClientSocketPoolManager::set_max_sockets_per_group(
+        net::HttpNetworkSession::NORMAL_SOCKET_POOL, max_connections_per_host);
+  }
+
   DCHECK(!webrtc_event_log_manager_);
   webrtc_event_log_manager_ = WebRtcEventLogManager::CreateSingletonInstance();
 
--- a/chrome/browser/flag_descriptions.cc
+++ b/chrome/browser/flag_descriptions.cc
@@ -2657,6 +2657,10 @@ const char kMantisFeatureKeyDescription[
     "Feature key to use the Mantis feature on ChromeOS.";
 #endif  // BUILDFLAG(IS_CHROMEOS)
 
+const char kMaxConnectionsPerHostName[] = "Maximum connections per host";
+const char kMaxConnectionsPerHostDescription[] =
+     "Customize maximum allowed connections per host. ungoogled-chromium flag, Bromite feature.";
+
 const char kMediaRouterCastAllowAllIPsName[] =
     "Connect to Cast devices on all IP addresses";
 const char kMediaRouterCastAllowAllIPsDescription[] =
--- a/chrome/browser/flag_descriptions.h
+++ b/chrome/browser/flag_descriptions.h
@@ -1548,6 +1548,9 @@ extern const char kMantisFeatureKeyName[
 extern const char kMantisFeatureKeyDescription[];
 #endif  // IS_CHROMEOS
 
+extern const char kMaxConnectionsPerHostName[];
+extern const char kMaxConnectionsPerHostDescription[];
+
 extern const char kMediaRouterCastAllowAllIPsName[];
 extern const char kMediaRouterCastAllowAllIPsDescription[];
 
--- a/components/network_session_configurator/common/network_features.cc
+++ b/components/network_session_configurator/common/network_features.cc
@@ -8,4 +8,7 @@
 
 namespace features {
 
+const char kMaxConnectionsPerHostChoiceDefault[] = "6",
+                 kMaxConnectionsPerHostChoice15[] = "15";
+
 }  // namespace features
--- a/components/network_session_configurator/common/network_features.h
+++ b/components/network_session_configurator/common/network_features.h
@@ -10,6 +10,10 @@
 
 namespace features {
 
+NETWORK_SESSION_CONFIGURATOR_EXPORT extern const char kMaxConnectionsPerHostChoiceDefault[],
+                 kMaxConnectionsPerHostChoice6[],
+                 kMaxConnectionsPerHostChoice15[];
+
 }  // namespace features
 
 #endif  // COMPONENTS_NETWORK_SESSION_CONFIGURATOR_COMMON_NETWORK_FEATURES_H_
--- a/components/network_session_configurator/common/network_switch_list.h
+++ b/components/network_session_configurator/common/network_switch_list.h
@@ -19,6 +19,10 @@ NETWORK_SWITCH(kEnableUserAlternateProto
 // Enables the QUIC protocol.  This is a temporary testing flag.
 NETWORK_SWITCH(kEnableQuic, "enable-quic")
 
+// Allows specifying a higher number of maximum connections per host
+// (15 instead of 6, mirroring the value Mozilla uses).
+NETWORK_SWITCH(kMaxConnectionsPerHost, "max-connections-per-host")
+
 // Ignores certificate-related errors.
 NETWORK_SWITCH(kIgnoreCertificateErrors, "ignore-certificate-errors")
 
