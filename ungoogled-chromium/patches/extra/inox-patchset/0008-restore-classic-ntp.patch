--- a/chrome/browser/search/search.cc
+++ b/chrome/browser/search/search.cc
@@ -178,33 +178,8 @@ struct NewTabURLDetails {
     const GURL local_url(default_is_google
                              ? chrome::kChromeUINewTabPageURL
                              : chrome::kChromeUINewTabPageThirdPartyURL);
-    if (default_is_google) {
-      return NewTabURLDetails(local_url, NEW_TAB_URL_VALID);
-    }
 #endif
-
-    const TemplateURL* template_url =
-        GetDefaultSearchProviderTemplateURL(profile);
-    if (!profile || !template_url) {
-      return NewTabURLDetails(local_url, NEW_TAB_URL_BAD);
-    }
-
-    GURL search_provider_url(template_url->new_tab_url_ref().ReplaceSearchTerms(
-        TemplateURLRef::SearchTermsArgs(std::u16string()),
-        UIThreadSearchTermsData()));
-
-    if (!search_provider_url.is_valid()) {
-      return NewTabURLDetails(local_url, NEW_TAB_URL_NOT_SET);
-    }
-    if (!search_provider_url.SchemeIsCryptographic()) {
-      return NewTabURLDetails(local_url, NEW_TAB_URL_INSECURE);
-    }
-    if (!IsURLAllowedForSupervisedUser(search_provider_url,
-                                       CHECK_DEREF(profile))) {
-      return NewTabURLDetails(local_url, NEW_TAB_URL_BLOCKED);
-    }
-
-    return NewTabURLDetails(search_provider_url, NEW_TAB_URL_VALID);
+    return NewTabURLDetails(local_url, NEW_TAB_URL_VALID);
   }
 
   const GURL url;
