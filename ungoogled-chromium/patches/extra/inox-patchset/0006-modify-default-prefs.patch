
--- a/chrome/browser/background/extensions/background_mode_manager.cc
+++ b/chrome/browser/background/extensions/background_mode_manager.cc
@@ -350,7 +350,7 @@ BackgroundModeManager::~BackgroundModeMa
 
 // static
 void BackgroundModeManager::RegisterPrefs(PrefRegistrySimple* registry) {
-  registry->RegisterBooleanPref(prefs::kBackgroundModeEnabled, true);
+  registry->RegisterBooleanPref(prefs::kBackgroundModeEnabled, false);
 }
 
 void BackgroundModeManager::RegisterProfile(Profile* profile) {
--- a/chrome/browser/chrome_content_browser_client.cc
+++ b/chrome/browser/chrome_content_browser_client.cc
@@ -1418,7 +1418,7 @@ void ChromeContentBrowserClient::Registe
 void ChromeContentBrowserClient::RegisterProfilePrefs(
     user_prefs::PrefRegistrySyncable* registry) {
   registry->RegisterBooleanPref(prefs::kDisable3DAPIs, false);
-  registry->RegisterBooleanPref(prefs::kEnableHyperlinkAuditing, true);
+  registry->RegisterBooleanPref(prefs::kEnableHyperlinkAuditing, false);
   // Register user prefs for mapping SitePerProcess and IsolateOrigins in
   // user policy in addition to the same named ones in Local State (which are
   // used for mapping the command-line flags).
--- a/chrome/browser/net/profile_network_context_service.cc
+++ b/chrome/browser/net/profile_network_context_service.cc
@@ -620,7 +620,7 @@ void ProfileNetworkContextService::Confi
 void ProfileNetworkContextService::RegisterProfilePrefs(
     user_prefs::PrefRegistrySyncable* registry) {
   registry->RegisterBooleanPref(embedder_support::kAlternateErrorPagesEnabled,
-                                true);
+                                false);
   registry->RegisterBooleanPref(prefs::kQuicAllowed, true);
   registry->RegisterBooleanPref(prefs::kGloballyScopeHTTPAuthCacheEnabled,
                                 false);
--- a/chrome/browser/preloading/preloading_prefs.h
+++ b/chrome/browser/preloading/preloading_prefs.h
@@ -23,7 +23,7 @@ enum class NetworkPredictionOptions {
   kWifiOnlyDeprecated = 1,
   kDisabled = 2,
   kExtended = 3,
-  kDefault = kWifiOnlyDeprecated,
+  kDefault = kDisabled,
 };
 
 // Enum representing possible values of the Preload Pages opt-in state. These
--- a/chrome/browser/profiles/profile.cc
+++ b/chrome/browser/profiles/profile.cc
@@ -325,7 +325,7 @@ const char Profile::kProfileKey[] = "__P
 void Profile::RegisterProfilePrefs(user_prefs::PrefRegistrySyncable* registry) {
   registry->RegisterBooleanPref(
       prefs::kSearchSuggestEnabled,
-      true,
+      false,
       user_prefs::PrefRegistrySyncable::SYNCABLE_PREF);
 #if BUILDFLAG(IS_ANDROID)
   registry->RegisterStringPref(
--- a/chrome/browser/resources/settings/reset_page/reset_profile_dialog.html
+++ b/chrome/browser/resources/settings/reset_page/reset_profile_dialog.html
@@ -33,7 +33,7 @@
         </cr-button>
       </div>
       <div slot="footer">
-        <cr-checkbox id="sendSettings" checked>
+        <cr-checkbox id="sendSettings">
           $i18nRaw{resetPageFeedback}</cr-checkbox>
       </div>
     </cr-dialog>
--- a/chrome/browser/ui/browser_ui_prefs.cc
+++ b/chrome/browser/ui/browser_ui_prefs.cc
@@ -117,7 +117,7 @@ void RegisterBrowserUserPrefs(user_prefs
   registry->RegisterBooleanPref(prefs::kWebAppCreateInAppsMenu, true);
   registry->RegisterBooleanPref(prefs::kWebAppCreateInQuickLaunchBar, true);
   registry->RegisterBooleanPref(
-      translate::prefs::kOfferTranslateEnabled, true,
+      translate::prefs::kOfferTranslateEnabled, false,
       user_prefs::PrefRegistrySyncable::SYNCABLE_PREF);
   registry->RegisterStringPref(prefs::kCloudPrintEmail, std::string());
   registry->RegisterBooleanPref(prefs::kCloudPrintProxyEnabled, true);
--- a/components/autofill/core/common/autofill_prefs.cc
+++ b/components/autofill/core/common/autofill_prefs.cc
@@ -31,7 +31,7 @@ constexpr char kAutofillRanQuasiDuplicat
 void RegisterProfilePrefs(user_prefs::PrefRegistrySyncable* registry) {
   // Synced prefs. Used for cross-device choices, e.g., credit card Autofill.
   registry->RegisterBooleanPref(
-      kAutofillProfileEnabled, true,
+      kAutofillProfileEnabled, false,
       user_prefs::PrefRegistrySyncable::SYNCABLE_PREF);
   registry->RegisterIntegerPref(
       kAutofillLastVersionDeduped, 0,
@@ -40,7 +40,7 @@ void RegisterProfilePrefs(user_prefs::Pr
       kAutofillHasSeenIban, false,
       user_prefs::PrefRegistrySyncable::SYNCABLE_PREF);
   registry->RegisterBooleanPref(
-      kAutofillCreditCardEnabled, true,
+      kAutofillCreditCardEnabled, false,
       user_prefs::PrefRegistrySyncable::SYNCABLE_PREF);
   registry->RegisterBooleanPref(
       kAutofillPaymentCvcStorage, true,
--- a/components/bookmarks/browser/bookmark_utils.cc
+++ b/components/bookmarks/browser/bookmark_utils.cc
@@ -449,7 +449,7 @@ bool DoesBookmarkContainWords(const std:
 
 void RegisterProfilePrefs(user_prefs::PrefRegistrySyncable* registry) {
   registry->RegisterBooleanPref(
-      prefs::kShowBookmarkBar, false,
+      prefs::kShowBookmarkBar, true,
       user_prefs::PrefRegistrySyncable::SYNCABLE_PREF);
   registry->RegisterBooleanPref(prefs::kEditBookmarksEnabled, true);
   registry->RegisterBooleanPref(
--- a/components/content_settings/core/browser/cookie_settings.cc
+++ b/components/content_settings/core/browser/cookie_settings.cc
@@ -94,7 +94,7 @@ void CookieSettings::RegisterProfilePref
     user_prefs::PrefRegistrySyncable* registry) {
   registry->RegisterIntegerPref(
       prefs::kCookieControlsMode,
-      static_cast<int>(CookieControlsMode::kIncognitoOnly),
+      static_cast<int>(CookieControlsMode::kBlockThirdParty),
       user_prefs::PrefRegistrySyncable::SYNCABLE_PREF);
 }
 
--- a/components/password_manager/core/browser/password_manager.cc
+++ b/components/password_manager/core/browser/password_manager.cc
@@ -504,7 +504,7 @@ void HandleFailedLoginDetectionForPasswo
 void PasswordManager::RegisterProfilePrefs(
     user_prefs::PrefRegistrySyncable* registry) {
   registry->RegisterBooleanPref(
-      prefs::kCredentialsEnableService, true,
+      prefs::kCredentialsEnableService, false,
       user_prefs::PrefRegistrySyncable::SYNCABLE_PRIORITY_PREF);
 #if BUILDFLAG(IS_IOS)
   // Deprecated pref in profile prefs.
@@ -512,7 +512,7 @@ void PasswordManager::RegisterProfilePre
                                 false);
 #endif  // BUILDFLAG(IS_IOS)
   registry->RegisterBooleanPref(
-      prefs::kCredentialsEnableAutosignin, true,
+      prefs::kCredentialsEnableAutosignin, false,
       user_prefs::PrefRegistrySyncable::SYNCABLE_PRIORITY_PREF);
   registry->RegisterBooleanPref(
       prefs::kWasAutoSignInFirstRunExperienceShown, false,
--- a/components/payments/core/payment_prefs.cc
+++ b/components/payments/core/payment_prefs.cc
@@ -11,7 +11,7 @@ namespace payments {
 void RegisterProfilePrefs(user_prefs::PrefRegistrySyncable* registry) {
   registry->RegisterBooleanPref(kPaymentsFirstTransactionCompleted, false);
   registry->RegisterBooleanPref(
-      kCanMakePaymentEnabled, true,
+      kCanMakePaymentEnabled, false,
       user_prefs::PrefRegistrySyncable::SYNCABLE_PREF);
 }
 
