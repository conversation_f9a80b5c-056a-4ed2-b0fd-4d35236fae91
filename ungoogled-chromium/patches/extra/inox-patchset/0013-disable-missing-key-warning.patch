--- a/chrome/browser/ui/startup/google_api_keys_infobar_delegate.cc
+++ b/chrome/browser/ui/startup/google_api_keys_infobar_delegate.cc
@@ -17,9 +17,6 @@
 // static
 void GoogleApiKeysInfoBarDelegate::Create(
     infobars::ContentInfoBarManager* infobar_manager) {
-  infobar_manager->AddInfoBar(
-      CreateConfirmInfoBar(std::unique_ptr<ConfirmInfoBarDelegate>(
-          new GoogleApiKeysInfoBarDelegate())));
 }
 
 GoogleApiKeysInfoBarDelegate::GoogleApiKeysInfoBarDelegate() = default;
