# Make popups go to tabs instead

--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -48,4 +48,8 @@
      "Close window with last tab",
      "Determines whether a window should close once the last tab is closed. ungoogled-chromium flag.",
      kOsDesktop, MULTI_VALUE_TYPE(kCloseWindowWithLastTab)},
+    {"popups-to-tabs",
+     "Popups to tabs",
+     "Makes popups open in new tabs. ungoogled-chromium flag",
+     kOsAll, SINGLE_VALUE_TYPE("popups-to-tabs")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/content/renderer/render_frame_impl.cc
+++ b/content/renderer/render_frame_impl.cc
@@ -1198,6 +1198,8 @@ WindowOpenDisposition NavigationPolicyTo
     case blink::kWebNavigationPolicyNewWindow:
       return WindowOpenDisposition::NEW_WINDOW;
     case blink::kWebNavigationPolicyNewPopup:
+      if (base::CommandLine::ForCurrentProcess()->Has<PERSON>witch("popups-to-tabs"))
+        return WindowOpenDisposition::NEW_FOREGROUND_TAB;
       return WindowOpenDisposition::NEW_POPUP;
     case blink::kWebNavigationPolicyPictureInPicture:
       return WindowOpenDisposition::NEW_PICTURE_IN_PICTURE;
--- a/ui/base/mojom/window_open_disposition_mojom_traits.h
+++ b/ui/base/mojom/window_open_disposition_mojom_traits.h
@@ -5,6 +5,7 @@
 #ifndef UI_BASE_MOJOM_WINDOW_OPEN_DISPOSITION_MOJOM_TRAITS_H_
 #define UI_BASE_MOJOM_WINDOW_OPEN_DISPOSITION_MOJOM_TRAITS_H_
 
+#include "base/command_line.h"
 #include "base/notreached.h"
 #include "mojo/public/cpp/bindings/enum_traits.h"
 #include "ui/base/mojom/window_open_disposition.mojom.h"
@@ -30,6 +31,8 @@ struct EnumTraits<ui::mojom::WindowOpenD
       case WindowOpenDisposition::NEW_PICTURE_IN_PICTURE:
         return ui::mojom::WindowOpenDisposition::NEW_PICTURE_IN_PICTURE;
       case WindowOpenDisposition::NEW_POPUP:
+        if (base::CommandLine::ForCurrentProcess()->HasSwitch("popups-to-tabs"))
+          return ui::mojom::WindowOpenDisposition::NEW_FOREGROUND_TAB;
         return ui::mojom::WindowOpenDisposition::NEW_POPUP;
       case WindowOpenDisposition::NEW_WINDOW:
         return ui::mojom::WindowOpenDisposition::NEW_WINDOW;
@@ -67,6 +70,8 @@ struct EnumTraits<ui::mojom::WindowOpenD
         return true;
       case ui::mojom::WindowOpenDisposition::NEW_POPUP:
         *out = WindowOpenDisposition::NEW_POPUP;
+        if (base::CommandLine::ForCurrentProcess()->HasSwitch("popups-to-tabs"))
+          *out = WindowOpenDisposition::NEW_FOREGROUND_TAB;
         return true;
       case ui::mojom::WindowOpenDisposition::NEW_WINDOW:
         *out = WindowOpenDisposition::NEW_WINDOW;
