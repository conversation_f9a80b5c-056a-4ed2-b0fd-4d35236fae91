--- a/chrome/browser/ui/views/frame/browser_root_view.cc
+++ b/chrome/browser/ui/views/frame/browser_root_view.cc
@@ -12,6 +12,7 @@
 #include <vector>
 
 #include "base/check_op.h"
+#include "base/command_line.h"
 #include "base/containers/adapters.h"
 #include "base/feature_list.h"
 #include "base/functional/bind.h"
@@ -175,6 +176,18 @@ DragOperation GetDropEffect(const ui::Dr
   return DragOperation::kMove;
 }
 
+bool ShouldScrollChangesTab() {
+  const std::string flag_value =
+    base::CommandLine::ForCurrentProcess()->GetSwitchValueASCII("scroll-tabs");
+
+  if (flag_value == "always")
+    return true;
+  else if (flag_value == "never")
+    return false;
+
+  return browser_defaults::kScrollEventChangesTab;
+}
+
 }  // namespace
 
 BrowserRootView::DropInfo::DropInfo() = default;
@@ -187,7 +200,9 @@ BrowserRootView::DropInfo::~DropInfo() {
 
 BrowserRootView::BrowserRootView(BrowserView* browser_view,
                                  views::Widget* widget)
-    : views::internal::RootView(widget), browser_view_(browser_view) {}
+    : views::internal::RootView(widget), browser_view_(browser_view) {
+  scroll_event_changes_tab_ = ShouldScrollChangesTab();
+}
 
 BrowserRootView::~BrowserRootView() {
   // It's possible to destroy the browser while a drop is active.  In this case,
@@ -327,7 +342,7 @@ bool BrowserRootView::OnMouseWheel(const
 
   // Scroll-event-changes-tab is incompatible with scrolling tabstrip, so
   // disable it if the latter feature is enabled.
-  if (browser_defaults::kScrollEventChangesTab &&
+  if (scroll_event_changes_tab_ &&
       !base::FeatureList::IsEnabled(tabs::kScrollableTabStrip)) {
     // Switch to the left/right tab if the wheel-scroll happens over the
     // tabstrip, or the empty space beside the tabstrip.
--- a/chrome/browser/ui/views/frame/browser_root_view.h
+++ b/chrome/browser/ui/views/frame/browser_root_view.h
@@ -159,6 +159,8 @@ class BrowserRootView : public views::in
   int scroll_remainder_x_ = 0;
   int scroll_remainder_y_ = 0;
 
+  bool scroll_event_changes_tab_ = false;
+
   std::unique_ptr<DropInfo> drop_info_;
 
   base::OnceClosure on_filtering_complete_closure_;
--- a/chrome/browser/ungoogled_flag_choices.h
+++ b/chrome/browser/ungoogled_flag_choices.h
@@ -25,4 +25,13 @@ const FeatureEntry::Choice kShowAvatarBu
      "show-avatar-button",
      "never"}
 };
+const FeatureEntry::Choice kScrollEventChangesTab[] = {
+    {flags_ui::kGenericExperimentChoiceDefault, "", ""},
+    {"Always",
+     "scroll-tabs",
+     "always"},
+    {"Never",
+     "scroll-tabs",
+     "never"}
+};
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_CHOICES_H_
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -32,4 +32,8 @@
      "Hide crashed bubble",
      "Hides the bubble box with the message \"Restore Pages? Chromium didn't shut down correctly.\" that shows on startup after the browser did not exit cleanly. ungoogled-chromium flag.",
      kOsAll, SINGLE_VALUE_TYPE("hide-crashed-bubble")},
+    {"scroll-tabs",
+     "Scroll switches tab",
+     "Switch to the left/right tab if the wheel-scroll happens over the tabstrip, or the empty space beside the tabstrip. ungoogled-chromium flag.",
+     kOsDesktop, MULTI_VALUE_TYPE(kScrollEventChangesTab)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
