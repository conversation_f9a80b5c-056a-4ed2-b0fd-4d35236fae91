--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -100,4 +100,8 @@
      "Disable Sharing Hub",
      "Disables the sharing hub button. ungoogled-chromium flag.",
      kOsDesktop, SINGLE_VALUE_TYPE("disable-sharing-hub")},
+    {"disable-link-drag",
+     "Disable link drag",
+     "Prevents dragging of links and selected text. ungoogled-chromium flag.",
+     kOsDesktop, FEATURE_VALUE_TYPE(blink::features::kDisableLinkDrag)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/third_party/blink/common/features.cc
+++ b/third_party/blink/common/features.cc
@@ -19,6 +19,8 @@
 
 namespace blink::features {
 
+BASE_FEATURE(kDisableLinkDrag, "DisableLinkDrag", base::FEATURE_DISABLED_BY_DEFAULT);
+
 // -----------------------------------------------------------------------------
 // Feature definitions and associated constants (feature params, et cetera)
 //
--- a/third_party/blink/public/common/features.h
+++ b/third_party/blink/public/common/features.h
@@ -22,6 +22,8 @@
 namespace blink {
 namespace features {
 
+BLINK_COMMON_EXPORT BASE_DECLARE_FEATURE(kDisableLinkDrag);
+
 // -----------------------------------------------------------------------------
 // Feature declarations and associated constants (feature params, et cetera)
 //
--- a/third_party/blink/renderer/core/editing/selection_controller.cc
+++ b/third_party/blink/renderer/core/editing/selection_controller.cc
@@ -31,6 +31,7 @@
 
 #include "base/auto_reset.h"
 #include "base/trace_event/trace_event.h"
+#include "third_party/blink/public/common/features.h"
 #include "third_party/blink/public/common/input/web_menu_source_type.h"
 #include "third_party/blink/public/platform/web_input_event_result.h"
 #include "third_party/blink/renderer/core/annotation/annotation_agent_impl.h"
@@ -1437,10 +1438,14 @@ FrameSelection& SelectionController::Sel
 }
 
 bool IsSelectionOverLink(const MouseEventWithHitTestResults& event) {
+if (base::FeatureList::IsEnabled(features::kDisableLinkDrag)){
+  return event.IsOverLink();
+}else{
   return (event.Event().GetModifiers() & WebInputEvent::Modifiers::kAltKey) !=
              0 &&
          event.IsOverLink();
 }
+}
 
 bool IsUserNodeDraggable(const MouseEventWithHitTestResults& event) {
   Node* inner_node = event.InnerNode();
--- a/third_party/blink/renderer/core/input/mouse_event_manager.cc
+++ b/third_party/blink/renderer/core/input/mouse_event_manager.cc
@@ -5,6 +5,7 @@
 #include "third_party/blink/renderer/core/input/mouse_event_manager.h"
 
 #include "build/build_config.h"
+#include "third_party/blink/public/common/features.h"
 #include "third_party/blink/public/platform/web_input_event_result.h"
 #include "third_party/blink/renderer/bindings/core/v8/v8_drag_event_init.h"
 #include "third_party/blink/renderer/bindings/core/v8/v8_pointer_event_init.h"
@@ -623,8 +624,14 @@ WebInputEventResult MouseEventManager::H
 
   bool single_click = event.Event().click_count <= 1;
 
+if (base::FeatureList::IsEnabled(features::kDisableLinkDrag)){
+  mouse_down_may_start_drag_ = single_click && !IsSelectionOverLink(event) &&
+                               !IsExtendingSelection(event) &&
+			        !event.GetHitTestResult().IsSelected(event.GetHitTestLocation());
+}else{
   mouse_down_may_start_drag_ = single_click && !IsSelectionOverLink(event) &&
                                !IsExtendingSelection(event);
+}
 
   mouse_down_ = event.Event();
 
