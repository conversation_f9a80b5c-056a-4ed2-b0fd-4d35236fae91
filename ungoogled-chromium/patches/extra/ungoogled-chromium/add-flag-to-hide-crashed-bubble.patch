# Add flag --hide-crashed-bubble to hide the bubble box:
# "Restore Pages? Chromium didn't shut down correctly."

--- a/chrome/browser/ui/startup/infobar_utils.cc
+++ b/chrome/browser/ui/startup/infobar_utils.cc
@@ -140,7 +140,8 @@ void AddInfoBarsIfNecessary(Browser* bro
   }
 
   // Web apps should not display the session restore bubble (crbug.com/1264121)
-  if (!is_web_app && HasPendingUncleanExit(browser->profile())) {
+  if (!is_web_app && HasPendingUncleanExit(browser->profile()) &&
+      !startup_command_line.HasSwitch("hide-crashed-bubble")) {
     SessionCrashedBubble::ShowIfNotOffTheRecordProfile(
         browser, /*skip_tab_checking=*/false);
   }
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -28,4 +28,8 @@
      "Show avatar/people/profile button",
      "Show avatar/people/profile button in the browser toolbar. ungoogled-chromium flag.",
      kOsDesktop, MULTI_VALUE_TYPE(kShowAvatarButtonChoices)},
+    {"hide-crashed-bubble",
+     "Hide crashed bubble",
+     "Hides the bubble box with the message \"Restore Pages? Chromium didn't shut down correctly.\" that shows on startup after the browser did not exit cleanly. ungoogled-chromium flag.",
+     kOsAll, SINGLE_VALUE_TYPE("hide-crashed-bubble")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
