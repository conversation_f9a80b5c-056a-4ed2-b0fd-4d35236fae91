--- a/chrome/browser/ui/views/toolbar/toolbar_view.cc
+++ b/chrome/browser/ui/views/toolbar/toolbar_view.cc
@@ -460,6 +460,17 @@ void ToolbarView::Init() {
                                browser_->profile()->IsGuestSession() ||
                                browser_->profile()->IsRegularProfile();
 #endif
+
+  const std::string sab_value = base::CommandLine::ForCurrentProcess()->
+                                GetSwitchValueASCII("show-avatar-button");
+  if (sab_value == "always")
+    show_avatar_toolbar_button = true;
+  else if (sab_value == "incognito-and-guest")
+    show_avatar_toolbar_button = browser_->profile()->IsIncognitoProfile() ||
+                                 browser_->profile()->IsGuestSession();
+  else if (sab_value == "never")
+    show_avatar_toolbar_button = false;
+
   avatar_->SetVisible(show_avatar_toolbar_button);
 
 #if BUILDFLAG(ENABLE_WEBUI_TAB_STRIP)
--- a/chrome/browser/ungoogled_flag_choices.h
+++ b/chrome/browser/ungoogled_flag_choices.h
@@ -13,4 +13,16 @@ const FeatureEntry::Choice kExtensionHan
      "extension-mime-request-handling",
      "always-prompt-for-install"},
 };
+const FeatureEntry::Choice kShowAvatarButtonChoices[] = {
+    {flags_ui::kGenericExperimentChoiceDefault, "", ""},
+    {"Always",
+     "show-avatar-button",
+     "always"},
+    {"Incognito and Guest",
+     "show-avatar-button",
+     "incognito-and-guest"},
+    {"Never",
+     "show-avatar-button",
+     "never"}
+};
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_CHOICES_H_
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -24,4 +24,8 @@
      "Force punycode hostnames",
      "Force punycode in hostnames instead of Unicode when displaying Internationalized Domain Names (IDNs). ungoogled-chromium flag.",
      kOsAll, SINGLE_VALUE_TYPE("force-punycode-hostnames")},
+    {"show-avatar-button",
+     "Show avatar/people/profile button",
+     "Show avatar/people/profile button in the browser toolbar. ungoogled-chromium flag.",
+     kOsDesktop, MULTI_VALUE_TYPE(kShowAvatarButtonChoices)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
