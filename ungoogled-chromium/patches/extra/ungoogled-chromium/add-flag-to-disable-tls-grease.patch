--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -88,4 +88,8 @@
      "Hide tab close buttons",
      "Hides the close buttons on tabs. ungoogled-chromium flag.",
      kOsDesktop, SINGLE_VALUE_TYPE("hide-tab-close-buttons")},
+    {"disable-grease-tls",
+     "Disable GREASE for TLS",
+     "Turn off GREASE (Generate Random Extensions And Sustain Extensibility) for TLS connections. ungoogled-chromium flag.",
+     kOsAll, SINGLE_VALUE_TYPE("disable-grease-tls")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/net/socket/ssl_client_socket_impl.cc
+++ b/net/socket/ssl_client_socket_impl.cc
@@ -200,7 +200,8 @@ class SSLClientSocketImpl::SSLContext {
     SSL_CTX_sess_set_new_cb(ssl_ctx_.get(), NewSessionCallback);
     SSL_CTX_set_timeout(ssl_ctx_.get(), 1 * 60 * 60 /* one hour */);
 
-    SSL_CTX_set_grease_enabled(ssl_ctx_.get(), 1);
+    int grease_mode = !base::CommandLine::ForCurrentProcess()->HasSwitch("disable-grease-tls");
+    SSL_CTX_set_grease_enabled(ssl_ctx_.get(), grease_mode);
 
     // Deduplicate all certificates minted from the SSL_CTX in memory.
     SSL_CTX_set0_buffer_pool(ssl_ctx_.get(), x509_util::GetBufferPool());
