--- a/chrome/browser/ui/views/frame/browser_view.cc
+++ b/chrome/browser/ui/views/frame/browser_view.cc
@@ -2130,6 +2130,10 @@ void BrowserView::ExitFullscreen() {
 void BrowserView::UpdateExclusiveAccessBubble(
     const ExclusiveAccessBubbleParams& params,
     ExclusiveAccessBubbleHideCallback first_hide_callback) {
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch(
+          "hide-fullscreen-exit-ui"))
+    return;
+
   // Trusted pinned mode does not allow to escape. So do not show the bubble.
   bool is_trusted_pinned =
       platform_util::IsBrowserLockedFullscreen(browser_.get());
--- a/chrome/browser/ui/views/fullscreen_control/fullscreen_control_host.cc
+++ b/chrome/browser/ui/views/fullscreen_control/fullscreen_control_host.cc
@@ -73,6 +73,10 @@ bool IsExitUiEnabled() {
   // menu and controls reveal when the cursor is moved to the top.
   return false;
 #else
+  if (base::CommandLine::ForCurrentProcess()-><PERSON><PERSON><PERSON>(
+          "hide-fullscreen-exit-ui"))
+    return false;
+
   // <PERSON><PERSON><PERSON> mode is a fullscreen experience, which makes the exit UI
   // inappropriate.
   return !IsRunningInAppMode();
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -108,4 +108,8 @@
      "Hide Extensions Menu",
      "Hides the extensions container. This includes the puzzle piece icon as well as any pinned extensions. ungoogled-chromium flag.",
      kOsDesktop, SINGLE_VALUE_TYPE("hide-extensions-menu")},
+    {"hide-fullscreen-exit-ui",
+     "Hide Fullscreen Exit UI",
+     "Hides the \"X\" that appears when the mouse cursor is moved towards the top of the window in fullscreen mode. Additionally, this hides the \"Press F11 to exit full screen\" popup. ungoogled-chromium flag.",
+     kOsDesktop, SINGLE_VALUE_TYPE("hide-fullscreen-exit-ui")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
