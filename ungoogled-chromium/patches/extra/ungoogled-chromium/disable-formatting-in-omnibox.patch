# Disables omission of URL elements in Omnibox and status bubble

--- a/chrome/browser/ui/status_bubble.h
+++ b/chrome/browser/ui/status_bubble.h
@@ -17,7 +17,7 @@ class GURL;
 class StatusBubble {
  public:
   // On hover, expand status bubble to fit long URL after this delay.
-  static const int kExpandHoverDelayMS = 1600;
+  static const int kExpandHoverDelayMS = 0;
 
   virtual ~StatusBubble() = default;
 
--- a/chrome/browser/ui/toolbar/chrome_location_bar_model_delegate.cc
+++ b/chrome/browser/ui/toolbar/chrome_location_bar_model_delegate.cc
@@ -268,5 +268,5 @@ TemplateURLService* ChromeLocationBarMod
 // static
 void ChromeLocationBarModelDelegate::RegisterProfilePrefs(
     user_prefs::PrefRegistrySyncable* registry) {
-  registry->RegisterBooleanPref(omnibox::kPreventUrlElisionsInOmnibox, false);
+  registry->RegisterBooleanPref(omnibox::kPreventUrlElisionsInOmnibox, true);
 }
