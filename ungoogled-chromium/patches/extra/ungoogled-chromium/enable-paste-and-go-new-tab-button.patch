--- a/chrome/browser/ui/views/frame/tab_strip_region_view.cc
+++ b/chrome/browser/ui/views/frame/tab_strip_region_view.cc
@@ -222,12 +222,10 @@ TabStripRegionView::TabStripRegionView(s
     new_tab_button_->GetViewAccessibility().SetName(
         l10n_util::GetStringUTF16(IDS_ACCNAME_NEWTAB));
 
-#if BUILDFLAG(IS_LINUX)
       // The New Tab Button can be middle-clicked on Linux.
       new_tab_button_->SetTriggerableEventFlags(
           new_tab_button_->GetTriggerableEventFlags() |
           ui::EF_MIDDLE_MOUSE_BUTTON);
-#endif
   }
 
   reserved_grab_handle_space_ =
--- a/chrome/browser/ui/views/tabs/tab_strip.cc
+++ b/chrome/browser/ui/views/tabs/tab_strip.cc
@@ -2273,8 +2273,6 @@ void TabStrip::NewTabButtonPressed(const
 
     const ui::MouseEvent& mouse = static_cast<const ui::MouseEvent&>(event);
     if (mouse.IsOnlyMiddleMouseButton()) {
-      if (ui::Clipboard::IsSupportedClipboardBuffer(
-              ui::ClipboardBuffer::kSelection)) {
         ui::Clipboard* clipboard = ui::Clipboard::GetForCurrentThread();
         CHECK(clipboard)
             << "Clipboard instance is not available, cannot proceed with "
@@ -2285,7 +2283,6 @@ void TabStrip::NewTabButtonPressed(const
         if (!clipboard_text.empty()) {
           controller_->CreateNewTabWithLocation(clipboard_text);
         }
-      }
       return;
     }
   }
