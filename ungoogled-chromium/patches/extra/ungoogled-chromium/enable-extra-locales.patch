# Enable extra locales not normally shipped with Chromium on desktop

--- a/build/config/locales.gni
+++ b/build/config/locales.gni
@@ -186,12 +186,6 @@ _non_android_locales = [ "cy" ]
 
 # Setup |platform_pak_locales| for each platform.
 platform_pak_locales = all_chrome_locales
-if (is_android) {
-  platform_pak_locales -= _non_android_locales
-  extended_locales -= _non_android_locales
-} else {
-  platform_pak_locales -= extended_locales
-}
 
 # The base list for all platforms except Android excludes the extended locales.
 # Add or subtract platform specific locales below.
--- a/chrome/app/resources/locale_settings_linux.grd
+++ b/chrome/app/resources/locale_settings_linux.grd
@@ -4,11 +4,11 @@
     <output filename="grit/platform_locale_settings.h" type="rc_header">
       <emit emit_type='prepend'></emit>
     </output>
-    <if expr="is_android">
       <output filename="platform_locale_settings_as.pak" type="data_package" lang="as" />
       <output filename="platform_locale_settings_az.pak" type="data_package" lang="az" />
       <output filename="platform_locale_settings_be.pak" type="data_package" lang="be" />
       <output filename="platform_locale_settings_bs.pak" type="data_package" lang="bs" />
+      <output filename="platform_locale_settings_cy.pak" type="data_package" lang="cy" />
       <output filename="platform_locale_settings_eu.pak" type="data_package" lang="eu" />
       <output filename="platform_locale_settings_fr-CA.pak" type="data_package" lang="fr-CA" />
       <output filename="platform_locale_settings_gl.pak" type="data_package" lang="gl" />
@@ -31,7 +31,6 @@
       <output filename="platform_locale_settings_uz.pak" type="data_package" lang="uz" />
       <output filename="platform_locale_settings_zh-HK.pak" type="data_package" lang="zh-HK" />
       <output filename="platform_locale_settings_zu.pak" type="data_package" lang="zu" />
-    </if>
     <output filename="platform_locale_settings_af.pak" type="data_package" lang="af" />
     <output filename="platform_locale_settings_am.pak" type="data_package" lang="am" />
     <output filename="platform_locale_settings_ar.pak" type="data_package" lang="ar" />
--- a/chrome/app/resources/locale_settings_mac.grd
+++ b/chrome/app/resources/locale_settings_mac.grd
@@ -4,6 +4,33 @@
     <output filename="grit/platform_locale_settings.h" type="rc_header">
       <emit emit_type='prepend'></emit>
     </output>
+      <output filename="platform_locale_settings_as.pak" type="data_package" lang="as" />
+      <output filename="platform_locale_settings_az.pak" type="data_package" lang="az" />
+      <output filename="platform_locale_settings_be.pak" type="data_package" lang="be" />
+      <output filename="platform_locale_settings_bs.pak" type="data_package" lang="bs" />
+      <output filename="platform_locale_settings_cy.pak" type="data_package" lang="cy" />
+      <output filename="platform_locale_settings_eu.pak" type="data_package" lang="eu" />
+      <output filename="platform_locale_settings_fr-CA.pak" type="data_package" lang="fr-CA" />
+      <output filename="platform_locale_settings_gl.pak" type="data_package" lang="gl" />
+      <output filename="platform_locale_settings_hy.pak" type="data_package" lang="hy" />
+      <output filename="platform_locale_settings_is.pak" type="data_package" lang="is" />
+      <output filename="platform_locale_settings_ka.pak" type="data_package" lang="ka" />
+      <output filename="platform_locale_settings_kk.pak" type="data_package" lang="kk" />
+      <output filename="platform_locale_settings_km.pak" type="data_package" lang="km" />
+      <output filename="platform_locale_settings_ky.pak" type="data_package" lang="ky" />
+      <output filename="platform_locale_settings_lo.pak" type="data_package" lang="lo" />
+      <output filename="platform_locale_settings_mk.pak" type="data_package" lang="mk" />
+      <output filename="platform_locale_settings_mn.pak" type="data_package" lang="mn" />
+      <output filename="platform_locale_settings_my.pak" type="data_package" lang="my" />
+      <output filename="platform_locale_settings_ne.pak" type="data_package" lang="ne" />
+      <output filename="platform_locale_settings_or.pak" type="data_package" lang="or" />
+      <output filename="platform_locale_settings_pa.pak" type="data_package" lang="pa" />
+      <output filename="platform_locale_settings_si.pak" type="data_package" lang="si" />
+      <output filename="platform_locale_settings_sq.pak" type="data_package" lang="sq" />
+      <output filename="platform_locale_settings_sr-Latn.pak" type="data_package" lang="sr-Latn" />
+      <output filename="platform_locale_settings_uz.pak" type="data_package" lang="uz" />
+      <output filename="platform_locale_settings_zh-HK.pak" type="data_package" lang="zh-HK" />
+      <output filename="platform_locale_settings_zu.pak" type="data_package" lang="zu" />
     <output filename="platform_locale_settings_af.pak" type="data_package" lang="af" />
     <output filename="platform_locale_settings_am.pak" type="data_package" lang="am" />
     <output filename="platform_locale_settings_ar.pak" type="data_package" lang="ar" />
--- a/chrome/app/resources/locale_settings_win.grd
+++ b/chrome/app/resources/locale_settings_win.grd
@@ -4,6 +4,33 @@
     <output filename="grit/platform_locale_settings.h" type="rc_header">
       <emit emit_type='prepend'></emit>
     </output>
+      <output filename="platform_locale_settings_as.pak" type="data_package" lang="as" />
+      <output filename="platform_locale_settings_az.pak" type="data_package" lang="az" />
+      <output filename="platform_locale_settings_be.pak" type="data_package" lang="be" />
+      <output filename="platform_locale_settings_bs.pak" type="data_package" lang="bs" />
+      <output filename="platform_locale_settings_cy.pak" type="data_package" lang="cy" />
+      <output filename="platform_locale_settings_eu.pak" type="data_package" lang="eu" />
+      <output filename="platform_locale_settings_fr-CA.pak" type="data_package" lang="fr-CA" />
+      <output filename="platform_locale_settings_gl.pak" type="data_package" lang="gl" />
+      <output filename="platform_locale_settings_hy.pak" type="data_package" lang="hy" />
+      <output filename="platform_locale_settings_is.pak" type="data_package" lang="is" />
+      <output filename="platform_locale_settings_ka.pak" type="data_package" lang="ka" />
+      <output filename="platform_locale_settings_kk.pak" type="data_package" lang="kk" />
+      <output filename="platform_locale_settings_km.pak" type="data_package" lang="km" />
+      <output filename="platform_locale_settings_ky.pak" type="data_package" lang="ky" />
+      <output filename="platform_locale_settings_lo.pak" type="data_package" lang="lo" />
+      <output filename="platform_locale_settings_mk.pak" type="data_package" lang="mk" />
+      <output filename="platform_locale_settings_mn.pak" type="data_package" lang="mn" />
+      <output filename="platform_locale_settings_my.pak" type="data_package" lang="my" />
+      <output filename="platform_locale_settings_ne.pak" type="data_package" lang="ne" />
+      <output filename="platform_locale_settings_or.pak" type="data_package" lang="or" />
+      <output filename="platform_locale_settings_pa.pak" type="data_package" lang="pa" />
+      <output filename="platform_locale_settings_si.pak" type="data_package" lang="si" />
+      <output filename="platform_locale_settings_sq.pak" type="data_package" lang="sq" />
+      <output filename="platform_locale_settings_sr-Latn.pak" type="data_package" lang="sr-Latn" />
+      <output filename="platform_locale_settings_uz.pak" type="data_package" lang="uz" />
+      <output filename="platform_locale_settings_zh-HK.pak" type="data_package" lang="zh-HK" />
+      <output filename="platform_locale_settings_zu.pak" type="data_package" lang="zu" />
     <output filename="platform_locale_settings_af.pak" type="data_package" lang="af" />
     <output filename="platform_locale_settings_am.pak" type="data_package" lang="am" />
     <output filename="platform_locale_settings_ar.pak" type="data_package" lang="ar" />
