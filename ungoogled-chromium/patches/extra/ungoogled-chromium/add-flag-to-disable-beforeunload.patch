# Add --disable-beforeunload to always disable beforeunload JavaScript dialogs

--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -16,4 +16,8 @@
      "Disable search engine collection",
      "Prevents search engines from being added automatically. ungoogled-chromium flag.",
      k<PERSON><PERSON>ll, SINGLE_VALUE_TYPE("disable-search-engine-collection")},
+    {"disable-beforeunload",
+     "Disable beforeunload",
+     "Disables JavaScript dialog boxes triggered by beforeunload. ungoogled-chromium flag.",
+     kOsAll, SINGLE_VALUE_TYPE("disable-beforeunload")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/components/javascript_dialogs/app_modal_dialog_manager.cc
+++ b/components/javascript_dialogs/app_modal_dialog_manager.cc
@@ -7,6 +7,7 @@
 #include <algorithm>
 #include <utility>
 
+#include "base/command_line.h"
 #include "base/functional/bind.h"
 #include "base/metrics/histogram_macros.h"
 #include "base/strings/utf_string_conversions.h"
@@ -145,7 +146,8 @@ void AppModalDialogManager::RunBeforeUnl
   ChromeJavaScriptDialogExtraData* extra_data =
       &javascript_dialog_extra_data_[web_contents];
 
-  if (extra_data->suppress_javascript_messages_) {
+  if (extra_data->suppress_javascript_messages_
+        || base::CommandLine::ForCurrentProcess()->HasSwitch("disable-beforeunload")) {
     // If a site harassed the user enough for them to put it on mute, then it
     // lost its privilege to deny unloading.
     std::move(callback).Run(true, std::u16string());
