--- a/chrome/browser/ui/views/tabs/tab.cc
+++ b/chrome/browser/ui/views/tabs/tab.cc
@@ -12,6 +12,7 @@
 #include <memory>
 #include <utility>
 
+#include "base/command_line.h"
 #include "base/debug/alias.h"
 #include "base/functional/bind.h"
 #include "base/i18n/rtl.h"
@@ -754,6 +755,13 @@ void Tab::HideHover(TabStyle::HideHoverS
   DeprecatedLayoutImmediately();
 }
 
+std::u16string Tab::GetRenderedTooltipText(const gfx::Point& p) const {
+  if (base::CommandLine::ForCurrentProcess()->GetSwitchValueASCII("tab-hover-cards") == "tooltip")
+    return GetTooltipText(data_.title, GetAlertStateToShow(data_.alert_state));
+  // Tab hover cards replace tooltips for tabs.
+  return std::u16string();
+}
+
 // This function updates the accessible name for the tab whenever any of the
 // parameters that influence the accessible name change. It ultimately calls
 // BrowserView::GetAccessibleTabLabel to get the updated accessible name.
--- a/chrome/browser/ui/views/tabs/tab.h
+++ b/chrome/browser/ui/views/tabs/tab.h
@@ -100,6 +100,7 @@ class Tab : public gfx::AnimationDelegat
   void OnMouseEntered(const ui::MouseEvent& event) override;
   void OnMouseExited(const ui::MouseEvent& event) override;
   void OnGestureEvent(ui::GestureEvent* event) override;
+  std::u16string GetRenderedTooltipText(const gfx::Point& p) const override;
   gfx::Size CalculatePreferredSize(
       const views::SizeBounds& available_size) const override;
   void PaintChildren(const views::PaintInfo& info) override;
--- a/chrome/browser/ui/views/tabs/tab_strip.cc
+++ b/chrome/browser/ui/views/tabs/tab_strip.cc
@@ -17,6 +17,7 @@
 #include <utility>
 #include <vector>
 
+#include "base/command_line.h"
 #include "base/check.h"
 #include "base/compiler_specific.h"
 #include "base/containers/adapters.h"
@@ -2003,6 +2004,8 @@ void TabStrip::OnMouseEventInTab(views::
 }
 
 void TabStrip::UpdateHoverCard(Tab* tab, HoverCardUpdateType update_type) {
+  if (base::CommandLine::ForCurrentProcess()->GetSwitchValueASCII("tab-hover-cards") == "tooltip" ||
+      base::CommandLine::ForCurrentProcess()->GetSwitchValueASCII("tab-hover-cards") == "none") return;
   tab_container_->UpdateHoverCard(tab, update_type);
 }
 
--- a/chrome/browser/ungoogled_flag_choices.h
+++ b/chrome/browser/ungoogled_flag_choices.h
@@ -70,4 +70,13 @@ const FeatureEntry::Choice kCloseConfirm
      "close-confirmation",
      "multiple"},
 };
+const FeatureEntry::Choice kTabHoverCards[] = {
+    {flags_ui::kGenericExperimentChoiceDefault, "", ""},
+    {"None",
+     "tab-hover-cards",
+     "none"},
+    {"Tooltip",
+     "tab-hover-cards",
+     "tooltip"},
+};
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_CHOICES_H_
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -80,4 +80,8 @@
      "Custom New Tab Page",
      "Allows setting a custom URL for the new tab page. Value can be internal (e.g. `about:blank` or `chrome://new-tab-page`), external (e.g. `example.com`), or local (e.g. `file:///tmp/startpage.html`). This applies for incognito windows as well when not set to a `chrome://` internal page. ungoogled-chromium flag",
      kOsDesktop, ORIGIN_LIST_VALUE_TYPE("custom-ntp", "")},
+    {"tab-hover-cards",
+     "Tab Hover Cards",
+     "Allows removing the tab hover cards or using a tooltip as a replacement. ungoogled-chromium flag.",
+     kOsDesktop, MULTI_VALUE_TYPE(kTabHoverCards)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
