--- a/chrome/browser/ui/qrcode_generator/qrcode_generator_bubble_controller.cc
+++ b/chrome/browser/ui/qrcode_generator/qrcode_generator_bubble_controller.cc
@@ -13,6 +13,7 @@
 #include "chrome/common/pref_names.h"
 #include "components/prefs/pref_change_registrar.h"
 #include "components/prefs/pref_service.h"
+#include "components/sharing_message/features.h"
 #include "content/public/browser/web_contents.h"
 #include "url/gurl.h"
 
@@ -38,7 +39,7 @@ QRCodeGeneratorBubbleController::~QRCode
 
 // static
 bool QRCodeGeneratorBubbleController::IsGeneratorAvailable(const GURL& url) {
-  if (!url.SchemeIsHTTPOrHTTPS()) {
+  if (!url.SchemeIsHTTPOrHTTPS() || base::FeatureList::IsEnabled(kDisableQRGenerator)) {
     return false;
   }
 
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -64,4 +64,8 @@
      "Remove Tabsearch Button",
      "Removes the tabsearch button from the tabstrip. ungoogled-chromium flag",
      kOsDesktop, SINGLE_VALUE_TYPE("remove-tabsearch-button")},
+    {"disable-qr-generator",
+     "Disable QR Generator",
+     "Disables the QR generator for sharing page links. ungoogled-chromium flag",
+     kOsDesktop, FEATURE_VALUE_TYPE(kDisableQRGenerator)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/components/sharing_message/features.cc
+++ b/components/sharing_message/features.cc
@@ -7,3 +7,5 @@
 #include "build/build_config.h"
 
 BASE_FEATURE(kClickToCall, "ClickToCall", base::FEATURE_DISABLED_BY_DEFAULT);
+
+BASE_FEATURE(kDisableQRGenerator, "DisableQRGenerator", base::FEATURE_DISABLED_BY_DEFAULT);
--- a/components/sharing_message/features.h
+++ b/components/sharing_message/features.h
@@ -10,4 +10,5 @@
 
 BASE_DECLARE_FEATURE(kClickToCall);
 
+BASE_DECLARE_FEATURE(kDisableQRGenerator);
 #endif  // COMPONENTS_SHARING_MESSAGE_FEATURES_H_
