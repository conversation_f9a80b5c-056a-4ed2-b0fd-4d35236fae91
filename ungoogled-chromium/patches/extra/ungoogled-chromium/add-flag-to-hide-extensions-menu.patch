--- a/chrome/browser/ui/views/extensions/extensions_toolbar_container.cc
+++ b/chrome/browser/ui/views/extensions/extensions_toolbar_container.cc
@@ -7,6 +7,7 @@
 #include <algorithm>
 #include <memory>
 
+#include "base/command_line.h"
 #include "base/feature_list.h"
 #include "base/functional/bind.h"
 #include "base/functional/callback_helpers.h"
@@ -35,6 +36,7 @@
 #include "chrome/browser/ui/views/side_panel/side_panel_ui.h"
 #include "chrome/browser/ui/views/toolbar/toolbar_action_hover_card_controller.h"
 #include "chrome/browser/ui/views/toolbar/toolbar_actions_bar_bubble_views.h"
+#include "chrome/browser/ui/views/toolbar/toolbar_view.h"
 #include "chrome/common/pref_names.h"
 #include "chrome/grit/generated_resources.h"
 #include "components/feature_engagement/public/event_constants.h"
@@ -544,6 +546,17 @@ void ExtensionsToolbarContainer::An<PERSON><PERSON>
   widget->widget_delegate()->AsBubbleDialogDelegate()->SetAnchorView(
       anchor_view && anchor_view->GetVisible() ? anchor_view
                                                : GetExtensionsButton());
+
+  // Fix the position of widgets. Without this fix, extension-installed-bubble
+  // and extension-uninstall-dialog may be out of the window border on Linux.
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch("hide-extensions-menu"))
+  {
+    views::View* anchor_view = BrowserView::GetBrowserViewForBrowser(browser_)
+      ->toolbar_button_provider()->GetAppMenuButton();
+    widget->widget_delegate()->AsBubbleDialogDelegate()
+      ->SetAnchorView(anchor_view);
+  }
+
   widget->Show();
 }
 
@@ -958,6 +971,9 @@ void ExtensionsToolbarContainer::UpdateC
 }
 
 bool ExtensionsToolbarContainer::ShouldContainerBeVisible() const {
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch("hide-extensions-menu"))
+    return false;
+
   // The container (and extensions-menu button) should not be visible if we have
   // no extensions.
   if (!HasAnyExtensions()) {
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -104,4 +104,8 @@
      "Disable link drag",
      "Prevents dragging of links and selected text. ungoogled-chromium flag.",
      kOsDesktop, FEATURE_VALUE_TYPE(blink::features::kDisableLinkDrag)},
+    {"hide-extensions-menu",
+     "Hide Extensions Menu",
+     "Hides the extensions container. This includes the puzzle piece icon as well as any pinned extensions. ungoogled-chromium flag.",
+     kOsDesktop, SINGLE_VALUE_TYPE("hide-extensions-menu")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
