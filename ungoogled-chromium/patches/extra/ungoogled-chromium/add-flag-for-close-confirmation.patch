--- a/chrome/browser/ui/browser.cc
+++ b/chrome/browser/ui/browser.cc
@@ -138,6 +138,7 @@
 #include "chrome/browser/ui/views/frame/browser_view.h"
 #include "chrome/browser/ui/views/frame/contents_web_view.h"
 #include "chrome/browser/ui/views/frame/multi_contents_view.h"
+#include "chrome/browser/ui/views/message_box_dialog.h"
 #include "chrome/browser/ui/views/status_bubble_views.h"
 #include "chrome/browser/ui/web_applications/app_browser_controller.h"
 #include "chrome/browser/ui/web_applications/web_app_launch_utils.h"
@@ -620,6 +621,7 @@ Browser::Browser(const CreateParams& par
       omit_from_session_restore_(params.omit_from_session_restore),
       should_trigger_session_restore_(params.should_trigger_session_restore),
       cancel_download_confirmation_state_(NOT_PROMPTED),
+      close_multitab_confirmation_state_(NOT_PROMPTED),
       override_bounds_(params.initial_bounds),
       initial_show_state_(params.initial_show_state),
       initial_workspace_(params.initial_workspace),
@@ -994,20 +996,22 @@ Browser::WarnBeforeClosingResult Browser
     return WarnBeforeClosingResult::kOkToClose;
   }
 
-  // `CanCloseWithInProgressDownloads()` may trigger a modal dialog.
-  bool can_close_with_downloads = CanCloseWithInProgressDownloads();
-  if (can_close_with_downloads &&
-      !ShouldShowCookieMigrationNoticeForBrowser(*this)) {
-    return WarnBeforeClosingResult::kOkToClose;
-  }
+  if (CanCloseWithMultipleTabs()) {
+    // `CanCloseWithInProgressDownloads()` may trigger a modal dialog.
+    bool can_close_with_downloads = CanCloseWithInProgressDownloads();
+    if (can_close_with_downloads &&
+        !ShouldShowCookieMigrationNoticeForBrowser(*this)) {
+      return WarnBeforeClosingResult::kOkToClose;
+    }
 
-  // If there is no download warning, show the cookie migration notice now.
-  // Otherwise, the download warning is being shown. Cookie migration notice
-  // will be shown after, if needed.
-  if (can_close_with_downloads) {
-    ShowCookieClearOnExitMigrationNotice(
-        *this, base::BindOnce(&Browser::CookieMigrationNoticeResponse,
-                              weak_factory_.GetWeakPtr()));
+    // If there is no download warning, show the cookie migration notice now.
+    // Otherwise, the download warning is being shown. Cookie migration notice
+    // will be shown after, if needed.
+    if (can_close_with_downloads) {
+      ShowCookieClearOnExitMigrationNotice(
+          *this, base::BindOnce(&Browser::CookieMigrationNoticeResponse,
+                                weak_factory_.GetWeakPtr()));
+    }
   }
 
   DCHECK(!warn_before_closing_callback_)
@@ -1045,6 +1049,7 @@ bool Browser::TryToCloseWindow(
 
 void Browser::ResetTryToCloseWindow() {
   cancel_download_confirmation_state_ = NOT_PROMPTED;
+  close_multitab_confirmation_state_ = NOT_PROMPTED;
   unload_controller_.ResetTryToCloseWindow();
 }
 
@@ -3496,6 +3501,58 @@ bool Browser::CanCloseWithInProgressDown
   return false;
 }
 
+bool Browser::CanCloseWithMultipleTabs() {
+  if (!base::CommandLine::ForCurrentProcess()->HasSwitch("close-confirmation"))
+    return true;
+
+  // If we've prompted, we need to hear from the user before we
+  // can close.
+  if (close_multitab_confirmation_state_ != NOT_PROMPTED)
+    return close_multitab_confirmation_state_ != WAITING_FOR_RESPONSE;
+
+  // If we're not running a full browser process with a profile manager
+  // (testing), it's ok to close the browser.
+  if (!g_browser_process->profile_manager())
+    return true;
+
+  // Figure out how many windows are open total
+  int total_window_count = 0;
+  for (Browser* browser : *BrowserList::GetInstance()) {
+    // Don't count this browser window or any other in the process of closing.
+    // Window closing may be delayed, and windows that are in the process of
+    // closing don't count against our totals.
+    if (browser == this || browser->IsAttemptingToCloseBrowser())
+      continue;
+    total_window_count++;
+  }
+
+  const auto flag_value = base::CommandLine::ForCurrentProcess()->GetSwitchValueASCII("close-confirmation");
+  bool show_confirmation_last_window = flag_value == "last";
+
+  if (show_confirmation_last_window) {
+    if (total_window_count >= 1 || this->tab_strip_model()->count() <= 1)
+      return true;
+  } else {
+    if (total_window_count == 0)
+      return true;
+    if (this->tab_strip_model()->count() == 0)
+      tab_strip_model_delegate_->AddTabAt(GURL(), -1, true);
+  }
+
+  close_multitab_confirmation_state_ = WAITING_FOR_RESPONSE;
+
+  auto callback = base::BindOnce(&Browser::MultitabResponse,
+                                 weak_factory_.GetWeakPtr());
+  MessageBoxDialog::Show(window_->GetNativeWindow(),
+                         u"Do you want to close this window?", std::u16string(),
+                         chrome::MESSAGE_BOX_TYPE_QUESTION, u"Close", u"Cancel",
+                         std::u16string(), std::move(callback));
+
+  // Return false so the browser does not close.  We'll close if the user
+  // confirms in the dialog.
+  return false;
+}
+
 void Browser::InProgressDownloadResponse(bool cancel_downloads) {
   if (cancel_downloads) {
     cancel_download_confirmation_state_ = RESPONSE_RECEIVED;
@@ -3521,6 +3578,22 @@ void Browser::InProgressDownloadResponse
 
   std::move(warn_before_closing_callback_)
       .Run(WarnBeforeClosingResult::kDoNotClose);
+}
+
+void Browser::MultitabResponse(chrome::MessageBoxResult result) {
+  if (result == chrome::MESSAGE_BOX_RESULT_YES) {
+    close_multitab_confirmation_state_ = RESPONSE_RECEIVED;
+    std::move(warn_before_closing_callback_)
+        .Run(WarnBeforeClosingResult::kOkToClose);
+    return;
+  }
+
+  // Sets the confirmation state to NOT_PROMPTED so that if the user tries to
+  // close again we'll show the warning again.
+  close_multitab_confirmation_state_ = NOT_PROMPTED;
+
+  std::move(warn_before_closing_callback_)
+      .Run(WarnBeforeClosingResult::kDoNotClose);
 }
 
 void Browser::CookieMigrationNoticeResponse(bool proceed_closing) {
--- a/chrome/browser/ui/browser.h
+++ b/chrome/browser/ui/browser.h
@@ -33,6 +33,7 @@
 #include "chrome/browser/ui/browser_window/public/browser_window_interface.h"
 #include "chrome/browser/ui/browser_window/public/desktop_browser_window_capabilities_delegate.h"
 #include "chrome/browser/ui/chrome_web_modal_dialog_manager_delegate.h"
+#include "chrome/browser/ui/simple_message_box.h"
 #include "chrome/browser/ui/tabs/tab_strip_model.h"
 #include "chrome/browser/ui/tabs/tab_strip_model_observer.h"
 #include "chrome/browser/ui/unload_controller.h"
@@ -1147,12 +1148,17 @@ class Browser : public TabStripModelObse
   // Returns true if the window can close, false otherwise.
   bool CanCloseWithInProgressDownloads();
 
+  // Called when the window is closing to check if more than one tabs are open
+  bool CanCloseWithMultipleTabs();
+
   // Called when the user has decided whether to proceed or not with the browser
   // closure.  |cancel_downloads| is true if the downloads should be canceled
   // and the browser closed, false if the browser should stay open and the
   // downloads running.
   void InProgressDownloadResponse(bool cancel_downloads);
 
+  void MultitabResponse(chrome::MessageBoxResult result);
+
   // Called when the user has decided whether to proceed or not with the browser
   // closure, in case the cookie migration notice was shown. |proceed_closing|
   // is true if the browser can  be closed.
@@ -1333,6 +1339,8 @@ class Browser : public TabStripModelObse
   // when the browser is closed with in-progress downloads.
   CancelDownloadConfirmationState cancel_download_confirmation_state_;
 
+  CancelDownloadConfirmationState close_multitab_confirmation_state_;
+
   /////////////////////////////////////////////////////////////////////////////
 
   // Override values for the bounds of the window and its maximized or minimized
--- a/chrome/browser/ungoogled_flag_choices.h
+++ b/chrome/browser/ungoogled_flag_choices.h
@@ -61,4 +61,13 @@ const FeatureEntry::Choice kCloseWindowW
      "close-window-with-last-tab",
      "never"},
 };
+const FeatureEntry::Choice kCloseConfirmation[] = {
+    {flags_ui::kGenericExperimentChoiceDefault, "", ""},
+    {"Show confirmation with last window",
+     "close-confirmation",
+     "last"},
+    {"Show confirmation with multiple windows",
+     "close-confirmation",
+     "multiple"},
+};
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_CHOICES_H_
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -72,4 +72,8 @@
      "Remove Grab Handle",
      "Removes the reserved empty space in the tabstrip for moving the window. ungoogled-chromium flag",
      kOsDesktop, SINGLE_VALUE_TYPE("remove-grab-handle")},
+    {"close-confirmation",
+     "Close Confirmation",
+     "Show a warning prompt when closing the browser window. ungoogled-chromium flag",
+     kOsDesktop, MULTI_VALUE_TYPE(kCloseConfirmation)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
