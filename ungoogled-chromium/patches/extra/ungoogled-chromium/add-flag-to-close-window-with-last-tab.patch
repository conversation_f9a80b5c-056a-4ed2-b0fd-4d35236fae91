--- a/chrome/browser/ui/tabs/tab_strip_model.cc
+++ b/chrome/browser/ui/tabs/tab_strip_model.cc
@@ -19,6 +19,7 @@
 
 #include "base/check.h"
 #include "base/check_op.h"
+#include "base/command_line.h"
 #include "base/containers/adapters.h"
 #include "base/containers/flat_map.h"
 #include "base/containers/span.h"
@@ -1145,6 +1146,10 @@ void TabStripModel::CloseAllTabsInGroup(
 
 void TabStripModel::CloseWebContentsAt(int index, uint32_t close_types) {
   CHECK(ContainsIndex(index));
+  if (base::CommandLine::ForCurrentProcess()->
+          GetSwitchValueASCII("close-window-with-last-tab") == "never" &&
+          !closing_all_ && count() < 2)
+    delegate()->AddTabAt(GURL(), -1, true);
   CloseTabs({GetWebContentsAt(index)}, close_types);
 }
 
@@ -2928,9 +2933,13 @@ TabStripModel::GetGroupsDestroyedFromRem
 void TabStripModel::ExecuteCloseTabsByIndices(
     base::RepeatingCallback<std::vector<int>()> get_indices_to_close,
     uint32_t close_types) {
-  ReentrancyCheck reentrancy_check(&reentrancy_guard_);
   const std::vector<int> indices_to_close =
       std::move(get_indices_to_close).Run();
+  if (base::CommandLine::ForCurrentProcess()->
+          GetSwitchValueASCII("close-window-with-last-tab") == "never" &&
+          static_cast<int>(indices_to_close.size()) == count())
+      delegate()->AddTabAt(GURL(), -1, true);
+  ReentrancyCheck reentrancy_check(&reentrancy_guard_);
   CloseTabs(GetWebContentsesByIndices(indices_to_close), close_types);
 }
 
--- a/chrome/browser/ungoogled_flag_choices.h
+++ b/chrome/browser/ungoogled_flag_choices.h
@@ -55,4 +55,10 @@ const FeatureEntry::Choice kOmniboxAutoc
      "omnibox-autocomplete-filtering",
      "search-bookmarks-chrome"},
 };
+const FeatureEntry::Choice kCloseWindowWithLastTab[] = {
+    {flags_ui::kGenericExperimentChoiceDefault, "", ""},
+    {"Never",
+     "close-window-with-last-tab",
+     "never"},
+};
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_CHOICES_H_
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -44,4 +44,8 @@
      "Omnibox Autocomplete Filtering",
      "Restrict omnibox autocomplete results to a combination of search suggestions (if enabled), bookmarks, and internal chrome pages. ungoogled-chromium flag.",
      kOsAll, MULTI_VALUE_TYPE(kOmniboxAutocompleteFiltering)},
+    {"close-window-with-last-tab",
+     "Close window with last tab",
+     "Determines whether a window should close once the last tab is closed. ungoogled-chromium flag.",
+     kOsDesktop, MULTI_VALUE_TYPE(kCloseWindowWithLastTab)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
