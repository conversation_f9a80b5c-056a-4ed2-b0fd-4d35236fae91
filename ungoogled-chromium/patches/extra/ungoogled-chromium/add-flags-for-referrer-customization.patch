--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -116,4 +116,16 @@
      "Enable themes in Incognito mode",
      "Allows themes to override Google's built-in Incognito theming. ungoogled-chromium flag.",
      kOsDesktop, SINGLE_VALUE_TYPE("enable-incognito-themes")},
+    {"remove-referrers",
+     "Remove Referrers",
+     "Removes all referrers. ungoogled-chromium flag.",
+     kOsAll, FEATURE_VALUE_TYPE(features::kNoReferrers)},
+    {"remove-cross-origin-referrers",
+     "Remove Cross-Origin Referrers",
+     "Removes all cross-origin referrers. Has lower precedence than remove-referrers. ungoogled-chromium flag.",
+     kOsAll, FEATURE_VALUE_TYPE(network::features::kNoCrossOriginReferrers)},
+    {"minimal-referrers",
+     "Minimal Referrers",
+     "Removes all cross-origin referrers and strips same-origin referrers down to the origin. Has lower precedence than remove-cross-origin-referrers. ungoogled-chromium flag.",
+     kOsAll, FEATURE_VALUE_TYPE(network::features::kMinimalReferrers)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/content/browser/renderer_host/navigation_request.cc
+++ b/content/browser/renderer_host/navigation_request.cc
@@ -172,6 +172,7 @@
 #include "services/network/public/cpp/permissions_policy/fenced_frame_permissions_policies.h"
 #include "services/network/public/cpp/permissions_policy/permissions_policy_declaration.h"
 #include "services/network/public/cpp/permissions_policy/permissions_policy_features.h"
+#include "services/network/public/cpp/referrer_sanitizer.h"
 #include "services/network/public/cpp/resource_request_body.h"
 #include "services/network/public/cpp/supports_loading_mode/supports_loading_mode_parser.h"
 #include "services/network/public/cpp/url_loader_completion_status.h"
@@ -393,10 +394,10 @@ void AddAdditionalRequestHeaders(
       net::HttpRequestHeaders::kUserAgent,
       ComputeUserAgentValue(*headers, user_agent_override, browser_context));
 
-  if (!render_prefs.enable_referrers) {
-    *referrer =
-        blink::mojom::Referrer(GURL(), network::mojom::ReferrerPolicy::kNever);
-  }
+  auto sanitized_referrer = referrer_sanitizer::sanitize_referrer(
+      referrer->url, url, referrer->policy, render_prefs.enable_referrers);
+  *referrer = blink::mojom::Referrer(std::move(sanitized_referrer.first),
+                                     std::move(sanitized_referrer.second));
 
   // Next, set the HTTP Origin if needed.
   if (NeedsHTTPOrigin(headers, method)) {
--- a/content/renderer/render_frame_impl.cc
+++ b/content/renderer/render_frame_impl.cc
@@ -133,6 +133,7 @@
 #include "services/network/public/cpp/content_decoding_interceptor.h"
 #include "services/network/public/cpp/features.h"
 #include "services/network/public/cpp/not_implemented_url_loader_factory.h"
+#include "services/network/public/cpp/referrer_sanitizer.h"
 #include "services/network/public/cpp/weak_wrapper_shared_url_loader_factory.h"
 #include "services/network/public/mojom/fetch_api.mojom.h"
 #include "services/network/public/mojom/restricted_cookie_manager.mojom.h"
@@ -4522,10 +4523,13 @@ void RenderFrameImpl::FinalizeRequestInt
 
   request.SetHasUserGesture(frame_->HasTransientUserActivation());
 
-  if (!GetWebView()->GetRendererPreferences().enable_referrers) {
-    request.SetReferrerString(WebString());
-    request.SetReferrerPolicy(network::mojom::ReferrerPolicy::kNever);
-  }
+  auto sanitized_referrer = referrer_sanitizer::sanitize_referrer(
+      WebStringToGURL(request.ReferrerString()),
+      WebStringToGURL(request.Url().GetString()), request.GetReferrerPolicy(),
+      GetWebView()->GetRendererPreferences().enable_referrers);
+  request.SetReferrerString(
+      WebString::FromUTF8(sanitized_referrer.first.spec()));
+  request.SetReferrerPolicy(std::move(sanitized_referrer.second));
 }
 
 void RenderFrameImpl::DidLoadResourceFromMemoryCache(
--- a/services/network/network_service_network_delegate.cc
+++ b/services/network/network_service_network_delegate.cc
@@ -32,6 +32,7 @@
 #include "services/network/network_service_proxy_delegate.h"
 #include "services/network/pending_callback_chain.h"
 #include "services/network/public/cpp/features.h"
+#include "services/network/public/cpp/referrer_sanitizer.h"
 #include "services/network/url_loader.h"
 #include "url/gurl.h"
 
@@ -69,9 +70,14 @@ NetworkServiceNetworkDelegate::~NetworkS
 void NetworkServiceNetworkDelegate::MaybeTruncateReferrer(
     net::URLRequest* const request,
     const GURL& effective_url) {
-  if (!enable_referrers_) {
-    request->SetReferrer(std::string());
-    request->set_referrer_policy(net::ReferrerPolicy::NO_REFERRER);
+
+  auto sanitized_referrer = referrer_sanitizer::sanitize_referrer(
+      GURL(request->referrer()), effective_url,
+      request->referrer_policy(), enable_referrers_);
+  if (sanitized_referrer.first != effective_url.spec() ||
+      sanitized_referrer.second != request->referrer_policy()) {
+    request->SetReferrer(sanitized_referrer.first.spec());
+    request->set_referrer_policy(std::move(sanitized_referrer.second));
     return;
   }
 
--- a/services/network/public/cpp/BUILD.gn
+++ b/services/network/public/cpp/BUILD.gn
@@ -122,6 +122,8 @@ component("cpp") {
     "private_network_access_check_result.cc",
     "private_network_access_check_result.h",
     "record_ontransfersizeupdate_utils.h",
+    "referrer_sanitizer.cc",
+    "referrer_sanitizer.h",
     "request_destination.cc",
     "request_destination.h",
     "request_mode.cc",
--- a/services/network/public/cpp/features.cc
+++ b/services/network/public/cpp/features.cc
@@ -15,6 +15,14 @@
 #include "url/origin.h"
 
 namespace network::features {
+BASE_FEATURE(kMinimalReferrers,
+             "MinimalReferrers",
+             base::FEATURE_DISABLED_BY_DEFAULT);
+
+BASE_FEATURE(kNoCrossOriginReferrers,
+             "NoCrossOriginReferrers",
+             base::FEATURE_DISABLED_BY_DEFAULT);
+
 
 // Enables the Accept-CH support disabler. If this feature is activated, Chrome
 // ignore Accept-CH response headers for a site that is specified in the
--- a/services/network/public/cpp/features.h
+++ b/services/network/public/cpp/features.h
@@ -17,6 +17,10 @@ class Origin;
 }  // namespace url
 
 namespace network::features {
+COMPONENT_EXPORT(NETWORK_CPP_FLAGS_AND_SWITCHES) BASE_DECLARE_FEATURE(kMinimalReferrers);
+
+COMPONENT_EXPORT(NETWORK_CPP_FLAGS_AND_SWITCHES) BASE_DECLARE_FEATURE(kNoCrossOriginReferrers);
+
 
 COMPONENT_EXPORT(NETWORK_CPP_FLAGS_AND_SWITCHES)
 BASE_DECLARE_FEATURE(kBlockAcceptClientHints);
--- /dev/null
+++ b/services/network/public/cpp/referrer_sanitizer.cc
@@ -0,0 +1,49 @@
+// Copyright 2023 The ungoogled-chromium Authors
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+#include "services/network/public/cpp/referrer_sanitizer.h"
+
+#include "base/feature_list.h"
+#include "services/network/public/cpp/features.h"
+#include "url/origin.h"
+
+namespace referrer_sanitizer {
+
+static GURL sanitize_referrer_internal(const GURL& origin,
+                                       const GURL& destination) {
+  if ((base::FeatureList::IsEnabled(network::features::kNoCrossOriginReferrers) ||
+       base::FeatureList::IsEnabled(network::features::kMinimalReferrers)) &&
+      !url::IsSameOriginWith(origin, destination)) {
+    return GURL();
+  } else if (base::FeatureList::IsEnabled(network::features::kMinimalReferrers)) {
+    return url::Origin::Create(origin).GetURL();
+  }
+  return origin;
+}
+
+std::pair<GURL, net::ReferrerPolicy> sanitize_referrer(
+    const GURL& origin,
+    const GURL& destination,
+    const net::ReferrerPolicy& referrer_policy,
+    bool enable_referrers) {
+  if (!enable_referrers) {
+    return {GURL(), net::ReferrerPolicy::NO_REFERRER};
+  }
+  return {sanitize_referrer_internal(std::move(origin), std::move(destination)),
+          referrer_policy};
+}
+
+std::pair<GURL, network::mojom::ReferrerPolicy> sanitize_referrer(
+    const GURL& origin,
+    const GURL& destination,
+    const network::mojom::ReferrerPolicy& referrer_policy,
+    bool enable_referrers) {
+  if (!enable_referrers) {
+    return {GURL(), network::mojom::ReferrerPolicy::kNever};
+  }
+  return {sanitize_referrer_internal(std::move(origin), std::move(destination)),
+          referrer_policy};
+}
+
+}  // namespace referrer_sanitizer
--- /dev/null
+++ b/services/network/public/cpp/referrer_sanitizer.h
@@ -0,0 +1,32 @@
+// Copyright 2023 The ungoogled-chromium Authors
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+#ifndef SERVICES_NETWORK_PUBLIC_CPP_REFERRER_SANITIZER_H_
+#define SERVICES_NETWORK_PUBLIC_CPP_REFERRER_SANITIZER_H_
+
+#include <string>
+#include <utility>
+#include "net/url_request/referrer_policy.h"
+#include "services/network/public/mojom/referrer_policy.mojom-shared.h"
+#include "url/gurl.h"
+
+namespace referrer_sanitizer {
+
+COMPONENT_EXPORT(NETWORK_CPP)
+std::pair<GURL, net::ReferrerPolicy> sanitize_referrer(
+    const GURL& origin,
+    const GURL& destination,
+    const net::ReferrerPolicy& referrer_policy,
+    bool enable_referrers);
+
+COMPONENT_EXPORT(NETWORK_CPP)
+std::pair<GURL, network::mojom::ReferrerPolicy> sanitize_referrer(
+    const GURL& origin,
+    const GURL& destination,
+    const network::mojom::ReferrerPolicy& referrer_policy,
+    bool enable_referrers);
+
+}  // namespace referrer_sanitizer
+
+#endif  // SERVICES_NETWORK_PUBLIC_CPP_REFERRER_SANITIZER_H_
--- a/third_party/blink/renderer/modules/service_worker/web_service_worker_fetch_context_impl.cc
+++ b/third_party/blink/renderer/modules/service_worker/web_service_worker_fetch_context_impl.cc
@@ -12,10 +12,12 @@
 #include "base/task/single_thread_task_runner.h"
 #include "mojo/public/cpp/bindings/pending_remote.h"
 #include "net/cookies/site_for_cookies.h"
+#include "services/network/public/cpp/referrer_sanitizer.h"
 #include "services/network/public/cpp/wrapper_shared_url_loader_factory.h"
 #include "services/network/public/mojom/fetch_api.mojom-shared.h"
 #include "third_party/blink/public/common/loader/loader_constants.h"
 #include "third_party/blink/public/mojom/fetch/fetch_api_request.mojom-blink.h"
+#include "third_party/blink/public/platform/url_conversion.h"
 #include "third_party/blink/public/platform/url_loader_throttle_provider.h"
 #include "third_party/blink/public/platform/web_url_request_extra_data.h"
 #include "third_party/blink/public/platform/websocket_handshake_throttle_provider.h"
@@ -155,10 +157,13 @@ void WebServiceWorkerFetchContextImpl::F
 
   request.SetURLRequestExtraData(std::move(url_request_extra_data));
 
-  if (!renderer_preferences_.enable_referrers) {
-    request.SetReferrerString(WebString());
-    request.SetReferrerPolicy(network::mojom::ReferrerPolicy::kNever);
-  }
+  auto sanitized_referrer = referrer_sanitizer::sanitize_referrer(
+      WebStringToGURL(request.ReferrerString()),
+      WebStringToGURL(request.Url().GetString()), request.GetReferrerPolicy(),
+      renderer_preferences_.enable_referrers);
+  request.SetReferrerString(
+      WebString::FromUTF8(sanitized_referrer.first.spec()));
+  request.SetReferrerPolicy(std::move(sanitized_referrer.second));
 }
 
 std::vector<std::unique_ptr<URLLoaderThrottle>>
--- a/third_party/blink/renderer/platform/loader/fetch/url_loader/dedicated_or_shared_worker_global_scope_context_impl.cc
+++ b/third_party/blink/renderer/platform/loader/fetch/url_loader/dedicated_or_shared_worker_global_scope_context_impl.cc
@@ -13,6 +13,7 @@
 #include "base/task/sequenced_task_runner.h"
 #include "base/task/single_thread_task_runner.h"
 #include "base/task/thread_pool.h"
+#include "services/network/public/cpp/referrer_sanitizer.h"
 #include "services/network/public/cpp/resource_request.h"
 #include "services/network/public/cpp/wrapper_shared_url_loader_factory.h"
 #include "third_party/blink/public/common/loader/loader_constants.h"
@@ -24,6 +25,7 @@
 #include "third_party/blink/public/platform/modules/service_worker/web_service_worker_provider_context.h"
 #include "third_party/blink/public/platform/platform.h"
 #include "third_party/blink/public/platform/resource_load_info_notifier_wrapper.h"
+#include "third_party/blink/public/platform/url_conversion.h"
 #include "third_party/blink/public/platform/url_loader_throttle_provider.h"
 #include "third_party/blink/public/platform/weak_wrapper_resource_load_info_notifier.h"
 #include "third_party/blink/public/platform/web_security_origin.h"
@@ -381,10 +383,13 @@ void DedicatedOrSharedWorkerGlobalScopeC
   auto url_request_extra_data = base::MakeRefCounted<WebURLRequestExtraData>();
   request.SetURLRequestExtraData(std::move(url_request_extra_data));
 
-  if (!renderer_preferences_.enable_referrers) {
-    request.SetReferrerString(WebString());
-    request.SetReferrerPolicy(network::mojom::ReferrerPolicy::kNever);
-  }
+  auto sanitized_referrer = referrer_sanitizer::sanitize_referrer(
+      WebStringToGURL(request.ReferrerString()),
+      WebStringToGURL(request.Url().GetString()), request.GetReferrerPolicy(),
+      renderer_preferences_.enable_referrers);
+  request.SetReferrerString(
+      WebString::FromUTF8(sanitized_referrer.first.spec()));
+  request.SetReferrerPolicy(std::move(sanitized_referrer.second));
 }
 
 std::vector<std::unique_ptr<URLLoaderThrottle>>
