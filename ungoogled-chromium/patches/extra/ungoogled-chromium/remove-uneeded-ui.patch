# Removes in order:
# 'Learn more' links on the performance page
# more 'Learn more' links on the performance page
# link to Chrome store on the extensions page when empty
# link to Chrome store in extensions page sidebar
# 'Tabs from other devices' entry on the History page sidebar
# the 'Learn more' link on new incognito tabs
# Live captions entry from the settings page
# link to Google's accessibility site from the settings page
# update status icon on the About page
# update status text on the About page
# link to Google's help site on the About page
# external link for theme entry on settings page
# the Google account line from the Payment methods page
# Safety check section on the settings page
# Google sign-in and Anonymized Data Collection sections
# help bubble for the removed data collection section
# Privacy guide promo on the privacy page
# Privacy guide section on the privacy page
# The password leak detection toggle on the security settings page
# Advanced Protection Program link on the security settings page
# the 'Learn more' link from the search engine entry on the settings page
# Safety Check entry on the side menu on the settings page
# the (?) learn more button on many settings pages
# 'Sign in to see tabs from other devices' in the history submenu
# the 'Vist Chrome Web Store' entry in the extensions section of the main menu
# the side panel entry in All Bookmarks
# the feedback entry in the third party cookie popup
# the 'Learn more' link on crashed tabs
# the Third-party sign-in site settings (FedCM)
# non-functional AI options
# disable Live<PERSON>aption flag by default, this also removes non-functional Live Caption checkbox from media controls
# the new feature badges

--- a/chrome/app/settings_chromium_strings.grdp
+++ b/chrome/app/settings_chromium_strings.grdp
@@ -385,13 +385,13 @@ Chromium understands forms better and ca
 
   <!-- Performance Page -->
   <message name="IDS_SETTINGS_PERFORMANCE_MEMORY_SAVER_MODE_SETTING_DESCRIPTION" desc="Description for the memory saver mode setting">
-    Chromium frees up memory from inactive tabs. This gives active tabs and other apps more computer resources and keeps Chromium fast. Your inactive tabs automatically become active again when you go back to them. <ph name="BEGIN_LINK">&lt;a href="$1" target="_blank" id="learn-more" aria-description="$2"&gt;</ph>Learn more about Memory Saver<ph name="END_LINK">&lt;/a&gt;</ph>
+    Chromium frees up memory from inactive tabs. This gives active tabs and other apps more computer resources and keeps Chromium fast. Your inactive tabs automatically become active again when you go back to them.
   </message>
   <message name="IDS_SETTINGS_PERFORMANCE_BATTERY_SAVER_MODE_SETTING_DESCRIPTION" desc="Description for the energy saver mode setting">
-    Chromium conserves battery power by limiting background activity and visual effects, such as smooth scrolling and video frame rates. <ph name="BEGIN_LINK">&lt;a href="$1" target="_blank" id="learn-more" aria-description="$2"&gt;</ph>Learn more about Energy Saver<ph name="END_LINK">&lt;/a&gt;</ph>
+    Chromium conserves battery power by limiting background activity and visual effects, such as smooth scrolling and video frame rates.
   </message>
   <message name="IDS_SETTINGS_PERFORMANCE_PRELOAD_TOGGLE_SUMMARY" desc="Summary for the preload pages setting">
-    Chromium preloads pages which makes browsing and searching faster. <ph name="BEGIN_LINK">&lt;a href="$1" target="_blank" id="learn-more" aria-description="$2"&gt;</ph>Learn more about preload pages<ph name="END_LINK">&lt;/a&gt;</ph>
+    Chromium preloads pages which makes browsing and searching faster.
   </message>
 
   <!-- Languages Page -->
--- a/chrome/app/settings_strings.grdp
+++ b/chrome/app/settings_strings.grdp
@@ -1058,7 +1058,7 @@
     Active site
   </message>
   <message name="IDS_SETTINGS_PERFORMANCE_TAB_DISCARDING_EXCEPTIONS_ADD_DIALOG_HELP" desc="Help text shown on the second tab of the tab discarding exception list add dialog. Explains how to use the filter format to add an exclusion rule to the list.">
-    Sites you add will always stay active and memory won't be freed up from them. <ph name="BEGIN_LINK">&lt;a href="$1" target="_blank"&gt;</ph>Learn more about keeping specific sites active<ph name="END_LINK">&lt;/a&gt;</ph>
+    Sites you add will always stay active and memory won't be freed up from them.
   </message>
   <message name="IDS_SETTINGS_PERFORMANCE_BATTERY_PAGE_TITLE" desc="Title of the power section in the performance settings page.">
     Power
@@ -1082,7 +1082,7 @@
     Inactive tabs appearance
   </message>
   <message name="IDS_SETTINGS_PERFORMANCE_DISCARD_RING_TREATMENT_ENABLED_DESCRIPTION_WITH_LEARN_LINK" desc="Label for a performance settings toggle description that controls whether the inactive tab UI is shown. Also contains a link to the help center article.">
-    A dotted circle appears around site icons. <ph name="BEGIN_LINK">&lt;a href="$1" target="_blank" aria-description="$2"&gt;</ph>Learn more about inactive tabs<ph name="END_LINK">&lt;/a&gt;</ph>
+    A dotted circle appears around site icons.
   </message>
   <message name="IDS_SETTINGS_PERFORMANCE_TAB_HOVER_PREVIEW_CARD_LINK_TITLE" desc="Title for the link row that points to tab hover preview card apperance settings.">
     Tab hover preview card appearance
@@ -1094,7 +1094,7 @@
     Performance issue alerts
   </message>
   <message name="IDS_SETTINGS_PERFORMANCE_INTERVENTION_NOTIFICATION_ENABLED_DESCRIPTION" desc="Label for a performance settings toggle that controls whether performance intervention notifications should be shown and link to learn more about performance issue alerts.">
-    Get notifications that suggest ways to improve detected performance issues. <ph name="BEGIN_LINK">&lt;a href="$1" target="_blank" aria-description="$2"&gt;</ph>Learn more about performance issue alerts<ph name="END_LINK">&lt;/a&gt;</ph>
+    Get notifications that suggest ways to improve detected performance issues.
   </message>
 
   <!-- Languages Page -->
--- a/chrome/browser/resources/extensions/item_list.html.ts
+++ b/chrome/browser/resources/extensions/item_list.html.ts
@@ -30,12 +30,6 @@ export function getHtml(this: Extensions
         </extensions-mv2-deprecation-panel>
       </div>` : ''}
 
-    <div id="no-items" class="empty-list-message"
-        ?hidden="${!this.shouldShowEmptyItemsMessage_()}">
-      <span @click="${this.onNoExtensionsClick_}">
-        $i18nRaw{noExtensionsOrApps}
-      </span>
-    </div>
     <div id="no-search-results" class="empty-list-message"
         ?hidden="${!this.shouldShowEmptySearchMessage_()}">
       <span>$i18n{noSearchResults}</span>
--- a/chrome/browser/resources/extensions/sidebar.html.ts
+++ b/chrome/browser/resources/extensions/sidebar.html.ts
@@ -33,24 +33,6 @@ export function getHtml(this: Extensions
     <cr-ripple></cr-ripple>
   </a>
 </cr-menu-selector>
-<div class="separator" ?hidden="${!this.inDevMode}"></div>
-      ${this.inDevMode ? html`
-        <div class="cr-nav-menu-item" id="moreExtensions">
-          <span id="promo-message-text" class="cr-secondary-text"
-            .innerHTML="${this.computeDocsPromoText_()}">
-          </span>
-        </div>
-        `: ''}
-<div class="separator"></div>
-<div class="cr-nav-menu-item" id="moreExtensions">
-  <cr-icon id="web-store-icon" icon="extensions-icons:web_store">
-  </cr-icon>
-  <span id="discover-more-text" class="cr-secondary-text"
-      @click="${this.onMoreExtensionsClick_}"
-      .innerHTML="${this.computeDiscoverMoreText_()}">
-  </span>
-  <cr-ripple></cr-ripple>
-</div>
 <!--_html_template_end_-->`;
   // clang-format on
 }
--- a/chrome/browser/resources/extensions/sidebar.ts
+++ b/chrome/browser/resources/extensions/sidebar.ts
@@ -8,7 +8,6 @@ import './icons.html.js';
 import type {CrMenuSelector} from 'chrome://resources/cr_elements/cr_menu_selector/cr_menu_selector.js';
 import {I18nMixinLit} from 'chrome://resources/cr_elements/i18n_mixin_lit.js';
 import {assert} from 'chrome://resources/js/assert.js';
-import {loadTimeData} from 'chrome://resources/js/load_time_data.js';
 import {CrLitElement} from 'chrome://resources/lit/v3_0/lit.rollup.js';
 import type {PropertyValues} from 'chrome://resources/lit/v3_0/lit.rollup.js';
 
@@ -113,21 +112,6 @@ export class ExtensionsSidebarElement ex
     }
   }
 
-  protected computeDiscoverMoreText_(): TrustedHTML {
-    return this.i18nAdvanced('sidebarDiscoverMore', {
-      tags: ['a'],
-      attrs: ['target'],
-      substitutions: [loadTimeData.getString('getMoreExtensionsUrl')],
-    });
-  }
-
-  protected computeDocsPromoText_(): TrustedHTML {
-    return this.i18nAdvanced('sidebarDocsPromo', {
-      tags: ['a'],
-      attrs: ['target'],
-      substitutions: [loadTimeData.getString('extensionsWhatsNewURL')],
-    });
-  }
 }
 
 declare global {
--- a/chrome/browser/resources/history/side_bar.html
+++ b/chrome/browser/resources/history/side_bar.html
@@ -11,13 +11,6 @@
     $i18n{historyMenuItem}
     <cr-ripple></cr-ripple>
   </a>
-  <a id="syncedTabs" role="menuitem" href="/syncedTabs"
-      class="page-item cr-nav-menu-item"
-      path="syncedTabs" @click="${this.onItemClick_}">
-    <cr-icon icon="cr:phonelink"></cr-icon>
-    $i18n{openTabsMenuItem}
-    <cr-ripple></cr-ripple>
-  </a>
   <a role="menuitem" id="clear-browsing-data"
       class="cr-nav-menu-item"
       href="chrome://settings/clearBrowserData"
--- a/chrome/browser/resources/new_tab_page_incognito_guest/incognito_tab.html
+++ b/chrome/browser/resources/new_tab_page_incognito_guest/incognito_tab.html
@@ -24,9 +24,6 @@ document.querySelector('#incognitothemec
   <h1>$i18n{incognitoTabHeading}</h1>
   <p id="subtitle">
     <span>$i18n{incognitoTabDescription}</span>
-    <a class="learn-more-button"
-        href="$i18n{learnMoreLink}"
-        aria-label="$i18nPolymer{learnMoreA11yLabel}">$i18n{learnMore}</a>
   </p>
   <div id="bulletpoints-wrapper">
     <div class="bulletpoints first">$i18nRaw{incognitoTabFeatures}</div>
--- a/chrome/browser/resources/settings/a11y_page/a11y_page.html
+++ b/chrome/browser/resources/settings/a11y_page/a11y_page.html
@@ -99,10 +99,5 @@
             sub-label="$i18n{toastAlertLevelDescription}">
         </settings-toggle-button>
 </if>
-        <cr-link-row class="hr" label="$i18n{moreFeaturesLink}"
-            on-click="onMoreFeaturesLinkClick_" sub-label="$i18n{a11yWebStore}"
-            button-aria-description="$i18n{opensInNewTab}"
-            external>
-        </cr-link-row>
       </div>
     </settings-section>
--- a/chrome/browser/resources/settings/about_page/about_page.html
+++ b/chrome/browser/resources/settings/about_page/about_page.html
@@ -57,7 +57,7 @@
       <div class="cr-row two-line">
         <!-- Set the icon from the iconset (when it's obsolete/EOL and
           when update is done) or set the src (when it's updating). -->
-<if expr="not is_chromeos">
+<if expr="False">
         <div class="icon-container"
             hidden="[[!shouldShowIcons_(showUpdateStatus_)]]">
           <cr-icon
@@ -72,7 +72,7 @@
         </div>
 </if>
         <div class="flex cr-padded-text">
-<if expr="not is_chromeos">
+<if expr="False">
           <div id="updateStatusMessage" hidden="[[!showUpdateStatus_]]">
             <div role="alert" aria-live="polite"
                 inner-h-t-m-l="[[getUpdateStatusMessage_(
@@ -128,9 +128,6 @@
         </div>
       </template>
 </if>
-      <cr-link-row class="hr" id="help" on-click="onHelpClick_"
-          label="$i18n{aboutGetHelpUsingChrome}"
-          external></cr-link-row>
 <if expr="_google_chrome">
       <cr-link-row class="hr" id="reportIssue" on-click="onReportIssueClick_"
           hidden="[[!prefs.feedback_allowed.value]]"
--- a/chrome/browser/resources/settings/appearance_page/appearance_page.html
+++ b/chrome/browser/resources/settings/appearance_page/appearance_page.html
@@ -40,12 +40,11 @@
     </style>
     <settings-section page-title="$i18n{appearancePageTitle}">
       <div route-path="default">
-        <div class="settings-row first" id="themeRow"
+        <div class="cr-row first" id="themeRow"
             hidden="[[!pageVisibility.setTheme]]">
-          <cr-link-row id="openTheme" class="first"
-              hidden="[[!pageVisibility.setTheme]]"
-              label="$i18n{themes}" sub-label="[[themeSublabel_]]"
-              on-click="onThemeClick_" external></cr-link-row>
+          <div class="flex cr-padded-text">
+            <div>$i18n{themes}</div><div class="secondary">[[themeSublabel_]]</div>
+          </div>
 <if expr="not is_linux">
           <template is="dom-if" if="[[prefs.extensions.theme.id.value]]">
             <div class="separator"></div>
--- a/chrome/browser/resources/settings/autofill_page/payments_section.html
+++ b/chrome/browser/resources/settings/autofill_page/payments_section.html
@@ -83,11 +83,6 @@
   </div>
 </template>
 
-<div id="manageLink" class="cr-row first">
-  <!-- This span lays out the link correctly, relative to the text. -->
-  <div class="cr-padded-text">$i18nRaw{manageCreditCardsLabel}</div>
-</div>
-
 <div class="cr-row continuation">
   <h2 class="flex">$i18n{creditCards}</h2>
   <template is="dom-if"
--- a/chrome/browser/resources/settings/autofill_page/payments_section.ts
+++ b/chrome/browser/resources/settings/autofill_page/payments_section.ts
@@ -286,14 +286,6 @@ export class SettingsPaymentsSectionElem
     // Record that the user opened the payments settings.
     chrome.metricsPrivate.recordUserAction('AutofillCreditCardsViewed');
 
-    // Measure clicks on the 'Google Account' link for managing payment methods.
-    const manageAccountAnchor = this.$.manageLink.querySelector('a');
-    if (manageAccountAnchor !== null) {
-      manageAccountAnchor.addEventListener('click', () => {
-        MetricsBrowserProxyImpl.getInstance().recordAction(
-            'Autofill.PaymentMethodsSettingsPage.ManagePaymentMethodsLinkClicked');
-      });
-    }
   }
 
   override disconnectedCallback() {
--- a/chrome/browser/resources/settings/privacy_page/personalization_options.html
+++ b/chrome/browser/resources/settings/privacy_page/personalization_options.html
@@ -24,7 +24,7 @@
 
     </style>
 
-<if expr="not is_chromeos">
+<if expr="False">
     <div id="chromeSigninUserChoiceSetting" class="hr cr-row" role="group"
         hidden="[[!chromeSigninUserChoiceInfo_.shouldShowSettings]]"
         aria-label="$i18n{chromeSigninChoiceTitle}">
@@ -90,12 +90,6 @@
     </settings-toggle-button>
 </if><!-- not chromeos -->
 </if><!-- _google_chrome -->
-    <settings-toggle-button id="urlCollectionToggle"
-        class="hr"
-        pref="{{prefs.url_keyed_anonymized_data_collection.enabled}}"
-        label="$i18n{urlKeyedAnonymizedDataCollection}"
-        sub-label="$i18n{urlKeyedAnonymizedDataCollectionDesc}">
-    </settings-toggle-button>
 <if expr="_google_chrome">
 <if expr="not is_chromeos">
     <settings-toggle-button id="spellCheckControl"
--- a/chrome/browser/resources/settings/privacy_page/personalization_options.ts
+++ b/chrome/browser/resources/settings/privacy_page/personalization_options.ts
@@ -199,10 +199,6 @@ export class SettingsPersonalizationOpti
         'chrome-signin-user-choice-info-change',
         this.setChromeSigninUserChoiceInfo_.bind(this));
     // </if>
-
-    this.registerHelpBubble(
-        ANONYMIZED_URL_COLLECTION_ID,
-        this.$.urlCollectionToggle.getBubbleAnchor(), {anchorPaddingTop: 10});
   }
 
   // <if expr="is_chromeos">
--- a/chrome/browser/resources/settings/privacy_page/privacy_page.html
+++ b/chrome/browser/resources/settings/privacy_page/privacy_page.html
@@ -52,15 +52,6 @@
             label="$i18n{clearBrowsingData}"
             sub-label="$i18n{clearBrowsingDataDescription}"
             on-click="onClearBrowsingDataClick_"></cr-link-row>
-        <template is="dom-if" if="[[isPrivacyGuideAvailable]]">
-          <cr-link-row id="privacyGuideLinkRow" class="hr"
-              start-icon="privacy20:signpost"
-              label="$i18n{privacyGuideLabel}"
-              sub-label="$i18n{privacyGuideSublabel}"
-              on-click="onPrivacyGuideClick_"
-              role-description="$i18n{subpageArrowRoleDescription}">
-          </cr-link-row>
-        </template>
         <cr-link-row id="thirdPartyCookiesLinkRow"
             start-icon="privacy:cookie"
             class="hr" label="$i18n{thirdPartyCookiesLinkRowLabel}"
--- a/chrome/browser/resources/settings/privacy_page/privacy_page_index.ts
+++ b/chrome/browser/resources/settings/privacy_page/privacy_page_index.ts
@@ -8,7 +8,6 @@ import '../basic_page/basic_page.js';
 import '../safety_hub/safety_hub_entry_point.js';
 import '../settings_page/settings_section.js';
 import '../settings_shared.css.js';
-import './privacy_guide/privacy_guide_promo.js';
 import './privacy_page.js';
 
 import {PrefsMixin} from '/shared/settings/prefs/prefs_mixin.js';
@@ -104,13 +103,6 @@ export class SettingsPrivacyPageIndexEle
   private getDefaultViews_(): string[] {
     const defaultViews = ['old'];
 
-    if (this.isPrivacyGuideAvailable) {
-      defaultViews.push('privacyGuidePromo');
-    }
-
-    if (this.showPage_(this.pageVisibility_.safetyHub)) {
-      defaultViews.push('safetyHubEntryPoint');
-    }
 
     return defaultViews;
   }
@@ -231,7 +223,6 @@ export class SettingsPrivacyPageIndexEle
       this.showPrivacyGuidePromo_ = false;
       return;
     }
-    this.showPrivacyGuidePromo_ = true;
     if (!this.privacyGuidePromoWasShown_) {
       this.privacyGuideBrowserProxy_.incrementPromoImpressionCount();
       this.privacyGuidePromoWasShown_ = true;
--- a/chrome/browser/resources/settings/privacy_page/security_page.html
+++ b/chrome/browser/resources/settings/privacy_page/security_page.html
@@ -113,13 +113,6 @@
     <div class="cr-row first">
       <h2 class="cr-title-text">$i18n{advancedPageTitle}</h2>
     </div>
-    <settings-toggle-button id="passwordsLeakToggle"
-          label="$i18n{passwordsLeakDetectionLabel}"
-          pref="{{prefs.generated.password_leak_detection}}"
-          sub-label="[[getPasswordsLeakToggleSubLabel_(
-                        prefs.profile.password_manager_leak_detection.*,
-                        prefs.generated.password_leak_detection.*)]]">
-    </settings-toggle-button>
     <template is="dom-if" if="[[!enableHttpsFirstModeNewSettings_]]" restamp>
       <settings-toggle-button id="httpsOnlyModeToggle"
           pref="{{prefs.generated.https_first_mode_enabled}}"
@@ -170,9 +163,3 @@
         on-click="onManageCertificatesClick_" external>
     </cr-link-row>
 
-    <cr-link-row id="advancedProtectionProgramLink" class="hr"
-        label="$i18n{advancedProtectionProgramTitle}"
-        sub-label="$i18n{advancedProtectionProgramDesc}"
-        on-click="onAdvancedProtectionProgramLinkClick_"
-        external>
-    </cr-link-row>
--- a/chrome/browser/resources/settings/search_page/search_page.html
+++ b/chrome/browser/resources/settings/search_page/search_page.html
@@ -42,12 +42,6 @@
         $i18n{searchPageTitle}
         <div class="secondary">
           $i18n{searchEngineChoiceEntryPointSubtitle}
-          <a href="$i18n{searchExplanationLearnMoreURL}"
-              aria-description="$i18n{opensInNewTab}"
-              aria-label="$i18n{searchExplanationLearnMoreA11yLabel}"
-              target="_blank">
-            $i18n{learnMore}
-          </a>
         </div>
         <template is="dom-if" if="[[isDefaultSearchControlledByPolicy_(
             prefs.default_search_provider_data.template_url_data)]]">
--- a/chrome/browser/resources/settings/settings_page/settings_subpage.html
+++ b/chrome/browser/resources/settings/settings_page/settings_subpage.html
@@ -94,7 +94,7 @@
       </template>
       <h1 class="cr-title-text">[[pageTitle]]</h1>
       <slot name="subpage-title-extra"></slot>
-      <template is="dom-if" if="[[learnMoreUrl]]">
+      <template is="dom-if" if="[[false]]">
         <cr-icon-button iron-icon="cr:help-outline" suppress-rtl-flip
             aria-label="[[getLearnMoreAriaLabel_(pageTitle)]]"
             aria-description="$i18n{opensInNewTab}" on-click="onHelpClick_">
--- a/chrome/browser/ui/tabs/recent_tabs_sub_menu_model.cc
+++ b/chrome/browser/ui/tabs/recent_tabs_sub_menu_model.cc
@@ -369,7 +369,6 @@ void RecentTabsSubMenuModel::Build() {
   AddSeparator(ui::NORMAL_SEPARATOR);
   history_separator_index_ = GetItemCount() - 1;
   BuildLocalEntries();
-  BuildTabsFromOtherDevices();
 }
 
 void RecentTabsSubMenuModel::BuildLocalEntries() {
--- a/chrome/browser/ui/toolbar/app_menu_model.cc
+++ b/chrome/browser/ui/toolbar/app_menu_model.cc
@@ -959,16 +959,6 @@ void ExtensionsMenuModel::Build(Browser*
   SetElementIdentifierAt(
       GetIndexOfCommandId(IDC_EXTENSIONS_SUBMENU_MANAGE_EXTENSIONS).value(),
       kManageExtensionsMenuItem);
-  AddItemWithStringId(IDC_EXTENSIONS_SUBMENU_VISIT_CHROME_WEB_STORE,
-                      IDS_EXTENSIONS_SUBMENU_CHROME_WEBSTORE_ITEM);
-  SetElementIdentifierAt(
-      GetIndexOfCommandId(IDC_EXTENSIONS_SUBMENU_VISIT_CHROME_WEB_STORE)
-          .value(),
-      kVisitChromeWebStoreMenuItem);
-#if BUILDFLAG(GOOGLE_CHROME_BRANDING)
-  SetCommandIcon(this, IDC_EXTENSIONS_SUBMENU_VISIT_CHROME_WEB_STORE,
-                 vector_icons::kGoogleChromeWebstoreIcon);
-#endif
 }
 
 ////////////////////////////////////////////////////////////////////////////////
--- a/chrome/browser/ui/views/bookmarks/bookmark_menu_delegate.cc
+++ b/chrome/browser/ui/views/bookmarks/bookmark_menu_delegate.cc
@@ -1241,12 +1241,14 @@ void BookmarkMenuDelegate::BuildOtherNod
   menu->AppendMenuItem(
       IDC_SHOW_BOOKMARK_SIDE_PANEL,
       l10n_util::GetStringUTF16(IDS_BOOKMARKS_ALL_BOOKMARKS_OPEN_SIDE_PANEL),
-      bookmarks_side_panel_icon);
+      bookmarks_side_panel_icon)->SetVisible(false);
+
   bool other_folder_children_count =
       GetBookmarkMergedSurfaceService()->GetChildrenCount(
           BookmarkParentFolder::OtherFolder());
   if (other_folder_children_count) {
     menu->AppendSeparator();
     other_node_menu_separator_ = menu->GetSubmenu()->children().back().get();
+    other_node_menu_separator_->SetVisible(false);
   }
 }
--- a/chrome/browser/ui/views/location_bar/cookie_controls/cookie_controls_content_view.cc
+++ b/chrome/browser/ui/views/location_bar/cookie_controls/cookie_controls_content_view.cc
@@ -188,14 +188,7 @@ void CookieControlsContentView::SetEnfor
 }
 
 void CookieControlsContentView::SetFeedbackSectionVisibility(bool visible) {
-  if (visible && base::FeatureList::IsEnabled(
-                     content_settings::features::kUserBypassFeedback)) {
-    feedback_section_->SetVisible(true);
-    // Ensure that the feedback row is always below ACT feature rows.
-    ReorderChildView(feedback_section_, children().size());
-  } else {
     feedback_section_->SetVisible(false);
-  }
 }
 
 void CookieControlsContentView::SetCookiesRowVisible(bool visible) {
--- a/chrome/browser/ui/views/sad_tab_view.cc
+++ b/chrome/browser/ui/views/sad_tab_view.cc
@@ -160,9 +160,6 @@ SadTabView::SadTabView(content::WebConte
   // There is currently no good way to handle kEnd alignment for a single
   // element.
   actions_container->SetLayoutManagerUseConstrainedSpace(false);
-
-  EnableHelpLink(actions_container);
-
   action_button_ =
       actions_container->AddChildView(std::make_unique<views::MdTextButton>(
           base::BindRepeating(&SadTabView::PerformAction,
@@ -263,22 +260,6 @@ void SadTabView::AttachToWebView() {
   }
 }
 
-void SadTabView::EnableHelpLink(views::FlexLayoutView* actions_container) {
-#if BUILDFLAG(IS_CHROMEOS)
-  // Do not show the help link in the kiosk session to prevent escape from a
-  // kiosk app.
-  if (chromeos::IsKioskSession()) {
-    return;
-  }
-#endif
-  auto* help_link =
-      actions_container->AddChildView(std::make_unique<views::Link>(
-          l10n_util::GetStringUTF16(GetHelpLinkTitle())));
-  help_link->SetCallback(base::BindRepeating(
-      &SadTab::PerformAction, base::Unretained(this), Action::HELP_LINK));
-  help_link->SetProperty(views::kTableVertAlignKey,
-                         views::LayoutAlignment::kCenter);
-}
 
 void SadTabView::OnBoundsChanged(const gfx::Rect& previous_bounds) {
   // Specify the maximum message and title width explicitly.
--- a/chrome/browser/ui/webui/settings/settings_localized_strings_provider.cc
+++ b/chrome/browser/ui/webui/settings/settings_localized_strings_provider.cc
@@ -986,40 +986,20 @@ void AddPerformanceStrings(content::WebU
        IDS_SETTINGS_PERFORMANCE_TAB_HOVER_PREVIEW_CARD_LINK_SUBTITLE},
       {"performanceInterventionEnabledLabel",
        IDS_SETTINGS_PERFORMANCE_INTERVENTION_NOTIFICATION_ENABLED_LABEL},
-  };
-  html_source->AddLocalizedStrings(kLocalizedStrings);
-
-  static constexpr struct {
-    const char* id;
-    int message_id;
-    const char16_t* url;
-  } kLearnMoreStrings[] = {
       {"batterySaverModeDescription",
-       IDS_SETTINGS_PERFORMANCE_BATTERY_SAVER_MODE_SETTING_DESCRIPTION,
-       chrome::kBatterySaverModeLearnMoreUrl},
+       IDS_SETTINGS_PERFORMANCE_BATTERY_SAVER_MODE_SETTING_DESCRIPTION},
       {"discardRingTreatmentEnabledDescriptionWithLearnLink",
-       IDS_SETTINGS_PERFORMANCE_DISCARD_RING_TREATMENT_ENABLED_DESCRIPTION_WITH_LEARN_LINK,
-       chrome::kDiscardRingTreatmentLearnMoreUrl},
+       IDS_SETTINGS_PERFORMANCE_DISCARD_RING_TREATMENT_ENABLED_DESCRIPTION_WITH_LEARN_LINK},
       {"memorySaverModeDescription",
-       IDS_SETTINGS_PERFORMANCE_MEMORY_SAVER_MODE_SETTING_DESCRIPTION,
-       chrome::kMemorySaverModeLearnMoreUrl},
+       IDS_SETTINGS_PERFORMANCE_MEMORY_SAVER_MODE_SETTING_DESCRIPTION},
       {"performanceInterventionEnabledDescription",
-       IDS_SETTINGS_PERFORMANCE_INTERVENTION_NOTIFICATION_ENABLED_DESCRIPTION,
-       chrome::kPerformanceInterventionLearnMoreUrl},
+       IDS_SETTINGS_PERFORMANCE_INTERVENTION_NOTIFICATION_ENABLED_DESCRIPTION},
       {"preloadingToggleSummary",
-       IDS_SETTINGS_PERFORMANCE_PRELOAD_TOGGLE_SUMMARY,
-       chrome::kPreloadingLearnMoreUrl}};
-
-  const std::u16string settings_opens_in_new_tab =
-      l10n_util::GetStringUTF16(IDS_SETTINGS_OPENS_IN_NEW_TAB);
-
-  for (const auto& learn_more_string : kLearnMoreStrings) {
-    html_source->AddString(
-        learn_more_string.id,
-        l10n_util::GetStringFUTF16(learn_more_string.message_id,
-                                   learn_more_string.url,
-                                   settings_opens_in_new_tab));
-  }
+       IDS_SETTINGS_PERFORMANCE_PRELOAD_TOGGLE_SUMMARY},
+      {"tabDiscardingExceptionsAddDialogHelp",
+       IDS_SETTINGS_PERFORMANCE_TAB_DISCARDING_EXCEPTIONS_ADD_DIALOG_HELP},
+  };
+  html_source->AddLocalizedStrings(kLocalizedStrings);
 
   html_source->AddString(
       "tabDiscardTimerFiveMinutes",
@@ -1065,11 +1045,6 @@ void AddPerformanceStrings(content::WebU
           base::NumberToString16(
               performance_manager::user_tuning::BatterySaverModeManager::
                   kLowBatteryThresholdPercent)));
-  html_source->AddString(
-      "tabDiscardingExceptionsAddDialogHelp",
-      l10n_util::GetStringFUTF16(
-          IDS_SETTINGS_PERFORMANCE_TAB_DISCARDING_EXCEPTIONS_ADD_DIALOG_HELP,
-          chrome::kMemorySaverModeTabDiscardingHelpUrl));
 
   html_source->AddString("discardRingTreatmentLearnMoreUrl",
                          chrome::kDiscardRingTreatmentLearnMoreUrl);
@@ -3585,7 +3560,7 @@ void AddSiteSettingsStrings(content::Web
       base::FeatureList::IsEnabled(blink::features::kWebPrinting));
 
   html_source->AddBoolean("enableFederatedIdentityApiContentSetting",
-                          base::FeatureList::IsEnabled(features::kFedCm));
+                          false);
 
   base::CommandLine& cmd = *base::CommandLine::ForCurrentProcess();
   html_source->AddBoolean(
--- a/chrome/browser/ui/webui/settings/settings_ui.cc
+++ b/chrome/browser/ui/webui/settings/settings_ui.cc
@@ -598,27 +598,20 @@ SettingsUI::SettingsUI(content::WebUI* w
                ->UserIsActivePasswordChangeUser()},
   };
 
-  const bool show_ai_settings_for_testing = base::FeatureList::IsEnabled(
-      optimization_guide::features::kAiSettingsPageForceAvailable);
-
-  // Show the AI features section in the AI page if any of the AI features are
-  // enabled.
-  bool show_ai_features_section = show_ai_settings_for_testing;
   for (auto [name, visible] : optimization_guide_features) {
-    html_source->AddBoolean(name, visible || show_ai_settings_for_testing);
-    show_ai_features_section |= visible;
+    html_source->AddBoolean(name, false);
   }
 
   // Within the AI subpage are separate sections for Glic and for all other AI
   // features, the visibility of these are separately controlled but we want to
   // show the subpage if any of the AI features or Glic are enabled.
   html_source->AddBoolean("showAiPage",
-                          show_glic_section || show_ai_features_section);
+                          false);
   html_source->AddBoolean("showAiPageAiFeatureSection",
-                          show_ai_features_section);
+                          false);
   html_source->AddBoolean(
       "enableAiSettingsInPrivacyGuide",
-      optimization_guide::features::IsPrivacyGuideAiSettingsEnabled());
+      false);
 
   // Delete Browsing Data
   html_source->AddBoolean(
--- a/media/base/media_switches.cc
+++ b/media/base/media_switches.cc
@@ -905,7 +905,7 @@ BASE_FEATURE(kOnDeviceWebSpeech,
              base::FEATURE_ENABLED_BY_DEFAULT);
 
 // Enables the Live Caption feature on supported devices.
-BASE_FEATURE(kLiveCaption, "LiveCaption", base::FEATURE_ENABLED_BY_DEFAULT);
+BASE_FEATURE(kLiveCaption, "LiveCaption", base::FEATURE_DISABLED_BY_DEFAULT);
 
 // Logs a DumpWithoutCrashing() call each time the Speech On-Device API (SODA)
 // fails to load. Used to diagnose issues when rolling out new versions of the
--- a/ui/views/controls/menu/menu_item_view.cc
+++ b/ui/views/controls/menu/menu_item_view.cc
@@ -869,7 +869,7 @@ void MenuItemView::SetAlerted() {
 }
 
 bool MenuItemView::ShouldShowNewBadge() const {
-  return is_new_;
+  return false;
 }
 
 bool MenuItemView::IsTraversableByKeyboard() const {
