--- a/chrome/browser/chrome_content_browser_client.cc
+++ b/chrome/browser/chrome_content_browser_client.cc
@@ -821,11 +821,15 @@ bool HandleNewTabPageLocationOverride(
 
   // Don't change the URL when incognito mode.
   if (profile->IsOffTheRecord()) {
+   if (!base::CommandLine::ForCurrentProcess()->Has<PERSON><PERSON>("custom-ntp"))
     return false;
   }
 
   std::string ntp_location =
       profile->GetPrefs()->GetString(prefs::kNewTabPageLocationOverride);
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch("custom-ntp"))
+    ntp_location = base::CommandLine::ForCurrentProcess()->GetSwitchValueASCII("custom-ntp");
+  if (profile->IsOffTheRecord() && ntp_location.find("chrome://") != std::string::npos) return false;
   if (ntp_location.empty()) {
     return false;
   }
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -76,4 +76,8 @@
      "Close Confirmation",
      "Show a warning prompt when closing the browser window. ungoogled-chromium flag",
      kOsDesktop, MULTI_VALUE_TYPE(kCloseConfirmation)},
+    {"custom-ntp",
+     "Custom New Tab Page",
+     "Allows setting a custom URL for the new tab page. Value can be internal (e.g. `about:blank` or `chrome://new-tab-page`), external (e.g. `example.com`), or local (e.g. `file:///tmp/startpage.html`). This applies for incognito windows as well when not set to a `chrome://` internal page. ungoogled-chromium flag",
+     kOsDesktop, ORIGIN_LIST_VALUE_TYPE("custom-ntp", "")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/components/webui/flags/flags_state.cc
+++ b/components/webui/flags/flags_state.cc
@@ -234,6 +234,7 @@ std::string GetCombinedOriginListValue(c
       command_line.GetSwitchValueASCII(command_line_switch);
   const std::string new_value =
       flags_storage.GetOriginListFlag(internal_entry_name);
+  if (command_line_switch == "custom-ntp") return existing_value.empty() ? new_value : existing_value;
   return CombineAndSanitizeOriginLists(existing_value, new_value);
 }
 
@@ -484,6 +485,7 @@ void FlagsState::SetOriginListFlag(const
                                    const std::string& value,
                                    FlagsStorage* flags_storage) {
   const std::string new_value =
+      internal_name == "custom-ntp" ? value :
       CombineAndSanitizeOriginLists(std::string(), value);
   flags_storage->SetOriginListFlag(internal_name, new_value);
 
