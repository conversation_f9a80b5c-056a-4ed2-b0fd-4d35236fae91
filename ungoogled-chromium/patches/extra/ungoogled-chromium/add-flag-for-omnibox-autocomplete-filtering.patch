--- a/chrome/browser/ungoogled_flag_choices.h
+++ b/chrome/browser/ungoogled_flag_choices.h
@@ -40,4 +40,19 @@ const FeatureEntry::Choice kBookmarkBarN
      "bookmark-bar-ntp",
      "never"},
 };
+const FeatureEntry::Choice kOmniboxAutocompleteFiltering[] = {
+    {flags_ui::kGenericExperimentChoiceDefault, "", ""},
+    {"Search suggestions only",
+     "omnibox-autocomplete-filtering",
+     "search"},
+    {"Search suggestions and bookmarks",
+     "omnibox-autocomplete-filtering",
+     "search-bookmarks"},
+    {"Search suggestions and internal chrome pages",
+     "omnibox-autocomplete-filtering",
+     "search-chrome"},
+    {"Search suggestions, bookmarks, and internal chrome pages",
+     "omnibox-autocomplete-filtering",
+     "search-bookmarks-chrome"},
+};
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_CHOICES_H_
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -40,4 +40,8 @@
      "Bookmark Bar on New-Tab-Page",
      "Disable the Bookmark Bar on the New-Tab-Page. ungoogled-chromium flag.",
      kOsDesktop, MULTI_VALUE_TYPE(kBookmarkBarNewTab)},
+    {"omnibox-autocomplete-filtering",
+     "Omnibox Autocomplete Filtering",
+     "Restrict omnibox autocomplete results to a combination of search suggestions (if enabled), bookmarks, and internal chrome pages. ungoogled-chromium flag.",
+     kOsAll, MULTI_VALUE_TYPE(kOmniboxAutocompleteFiltering)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/components/omnibox/browser/autocomplete_controller.cc
+++ b/components/omnibox/browser/autocomplete_controller.cc
@@ -23,6 +23,7 @@
 
 #include "base/check_op.h"
 #include "base/containers/contains.h"
+#include "base/command_line.h"
 #include "base/feature_list.h"
 #include "base/format_macros.h"
 #include "base/functional/bind.h"
@@ -570,6 +571,15 @@ AutocompleteController::AutocompleteCont
       steady_state_omnibox_position_(
           metrics::OmniboxEventProto::UNKNOWN_POSITION) {
   provider_types &= ~OmniboxFieldTrial::GetDisabledProviderTypes();
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch("omnibox-autocomplete-filtering")) {
+    const std::string flag_value = base::CommandLine::ForCurrentProcess()->GetSwitchValueASCII("omnibox-autocomplete-filtering");
+    provider_types &= AutocompleteProvider::TYPE_KEYWORD | AutocompleteProvider::TYPE_SEARCH |
+        AutocompleteProvider::TYPE_HISTORY_URL | AutocompleteProvider::TYPE_BOOKMARK | AutocompleteProvider::TYPE_BUILTIN;
+    if (!base::Contains(flag_value, "bookmarks"))
+      provider_types &= ~AutocompleteProvider::TYPE_BOOKMARK;
+    if (!base::Contains(flag_value, "chrome"))
+      provider_types &= ~AutocompleteProvider::TYPE_BUILTIN;
+  }
 
   // Providers run in the order they're added. Async providers should run first
   // so their async requests can be kicked off before waiting a few milliseconds
--- a/components/omnibox/browser/history_url_provider.cc
+++ b/components/omnibox/browser/history_url_provider.cc
@@ -488,6 +488,9 @@ void HistoryURLProvider::Start(const Aut
   if (fixed_up_input.type() != metrics::OmniboxInputType::QUERY)
     matches_.push_back(what_you_typed_match);
 
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch("omnibox-autocomplete-filtering"))
+    return;
+
   // We'll need the history service to run both passes, so try to obtain it.
   history::HistoryService* const history_service =
       client()->GetHistoryService();
--- a/components/omnibox/browser/search_provider.cc
+++ b/components/omnibox/browser/search_provider.cc
@@ -15,6 +15,7 @@
 #include <vector>
 
 #include "base/base64.h"
+#include "base/command_line.h"
 #include "base/feature_list.h"
 #include "base/functional/bind.h"
 #include "base/functional/callback.h"
@@ -632,6 +633,9 @@ void SearchProvider::Run(bool query_is_p
 }
 
 void SearchProvider::DoHistoryQuery(bool minimal_changes) {
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch("omnibox-autocomplete-filtering"))
+    return;
+
   // The history query results are synchronous, so if minimal_changes is true,
   // we still have the last results and don't need to do anything.
   if (minimal_changes)
--- a/components/url_formatter/url_fixer.cc
+++ b/components/url_formatter/url_fixer.cc
@@ -15,6 +15,8 @@
 #include <string_view>
 
 #include "base/check_op.h"
+#include "base/containers/contains.h"
+#include "base/command_line.h"
 #include "base/files/file_path.h"
 #include "base/files/file_util.h"
 #include "base/i18n/char_iterator.h"
@@ -648,6 +650,8 @@ GURL FixupURLInternal(const std::string&
 
     FixupHost(trimmed, parts.host, parts.scheme.is_valid(), desired_tld, &url);
     if (chrome_url && !parts.host.is_valid()) {
+     if (!base::CommandLine::ForCurrentProcess()->HasSwitch("omnibox-autocomplete-filtering") ||
+         base::Contains(base::CommandLine::ForCurrentProcess()->GetSwitchValueASCII("omnibox-autocomplete-filtering"), "chrome"))
       url.append(kChromeUIDefaultHost);
     }
     FixupPort(trimmed, parts.port, &url);
