# Keep local history longer than 90 days

--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -52,4 +52,8 @@
      "Popups to tabs",
      "Makes popups open in new tabs. ungoogled-chromium flag",
      kOs<PERSON>ll, SINGLE_VALUE_TYPE("popups-to-tabs")},
+    {"keep-old-history",
+     "Keep old history",
+     "Keep history older than 3 months. ungoogled-chromium flag",
+     kOsAll, SINGLE_VALUE_TYPE("keep-old-history")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/components/history/core/browser/history_backend.cc
+++ b/components/history/core/browser/history_backend.cc
@@ -14,6 +14,7 @@
 #include <utility>
 #include <vector>
 
+#include "base/command_line.h"
 #include "base/compiler_specific.h"
 #include "base/containers/flat_set.h"
 #include "base/feature_list.h"
@@ -1327,7 +1328,8 @@ void HistoryBackend::InitImpl(
   db_->GetStartDate(&first_recorded_time_);
 
   // Start expiring old stuff.
-  expirer_.StartExpiringOldStuff(base::Days(kExpireDaysThreshold));
+  if (!base::CommandLine::ForCurrentProcess()->HasSwitch("keep-old-history"))
+    expirer_.StartExpiringOldStuff(base::Days(kExpireDaysThreshold));
 }
 
 void HistoryBackend::OnMemoryPressure(
@@ -1549,6 +1551,8 @@ void HistoryBackend::AddPagesWithDetails
 }
 
 bool HistoryBackend::IsExpiredVisitTime(const base::Time& time) const {
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch("keep-old-history"))
+    return false;
   return time < expirer_.GetCurrentExpirationTime();
 }
 
