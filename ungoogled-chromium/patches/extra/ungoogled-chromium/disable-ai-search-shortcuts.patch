--- a/chrome/browser/ui/search_engines/template_url_table_model.cc
+++ b/chrome/browser/ui/search_engines/template_url_table_model.cc
@@ -117,23 +117,10 @@ void TemplateURLTableModel::Reload() {
       extension_entries;
   // Keywords that can be made the default first.
   for (TemplateURL* template_url : urls) {
-    // Skip @gemini if feature disabled.
-    if (template_url->starter_pack_id() ==
-            template_url_starter_pack_data::kGemini &&
-        !OmniboxFieldTrial::IsStarterPackExpansionEnabled()) {
-      continue;
-    }
-    // Skip @page if feature disabled.
-    if (template_url->starter_pack_id() ==
-            template_url_starter_pack_data::kPage &&
-        !omnibox_feature_configs::ContextualSearch::Get().starter_pack_page) {
-      continue;
-    }
-    // Skip @aimode if feature disabled.
-    if (template_url->starter_pack_id() ==
-            template_url_starter_pack_data::kAiMode &&
-        (!omnibox_feature_configs::Toolbelt::Get().enabled ||
-         !ai_mode_enabled_)) {
+    // Disable all Google AI starter pack options
+    if (template_url->starter_pack_id() == template_url_starter_pack_data::kGemini ||
+        template_url->starter_pack_id() == template_url_starter_pack_data::kAiMode ||
+        template_url->starter_pack_id() == template_url_starter_pack_data::kPage) {
       continue;
     }
 
--- a/components/omnibox/browser/featured_search_provider.cc
+++ b/components/omnibox/browser/featured_search_provider.cc
@@ -290,21 +290,10 @@ void FeaturedSearchProvider::AddFeatured
   for (TemplateURL* turl : turls) {
     if (turl->starter_pack_id() > 0 &&
         turl->is_active() == TemplateURLData::ActiveStatus::kTrue) {
-      // Skip @gemini if feature disabled.
-      if (turl->starter_pack_id() == template_url_starter_pack_data::kGemini &&
-          !OmniboxFieldTrial::IsStarterPackExpansionEnabled()) {
-        continue;
-      }
-      // Skip @page if feature disabled.
-      if (turl->starter_pack_id() == template_url_starter_pack_data::kPage &&
-          !omnibox_feature_configs::ContextualSearch::Get().starter_pack_page) {
-        continue;
-      }
-      // Skip @aimode if feature disabled.
-      if (turl->starter_pack_id() == template_url_starter_pack_data::kAiMode &&
-          (!omnibox_feature_configs::Toolbelt::Get().enabled ||
-           !client_->GetAimEligibilityService() ||
-           !client_->GetAimEligibilityService()->IsAimEligible())) {
+      // Disable all Google AI starter pack options
+      if (turl->starter_pack_id() == template_url_starter_pack_data::kGemini ||
+          turl->starter_pack_id() == template_url_starter_pack_data::kAiMode ||
+          turl->starter_pack_id() == template_url_starter_pack_data::kPage) {
         continue;
       }
       // The history starter pack engine is disabled in incognito mode.
