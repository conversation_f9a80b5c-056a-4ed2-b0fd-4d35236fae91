--- a/chrome/browser/browser_features.cc
+++ b/chrome/browser/browser_features.cc
@@ -54,7 +54,7 @@ BASE_FEATURE(kBookmarkTriggerForPreconne
 // switch.
 BASE_FEATURE(kCertificateTransparencyAskBeforeEnabling,
              "CertificateTransparencyAskBeforeEnabling",
-#if BUILDFLAG(GOOGLE_CHROME_BRANDING)
+#if true
              base::FEATURE_ENABLED_BY_DEFAULT);
 #else
              base::FEATURE_DISABLED_BY_DEFAULT);
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -136,4 +136,8 @@
      "Remove Client Hints",
      "Removes client hints (information sent to servers about your system, similar to a user agent). ungoogled-chromium flag.",
      kOsAll, FEATURE_VALUE_TYPE(blink::features::kRemoveClientHints)},
+    {"enforce-certificate-transparency",
+     "Enforce Certificate Transparency",
+     "Enforce Certificate Transparency for certificates that sites present. This is enabled by default. ungoogled-chromium flag.",
+     kOsAll, FEATURE_VALUE_TYPE(features::kCertificateTransparencyAskBeforeEnabling)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
