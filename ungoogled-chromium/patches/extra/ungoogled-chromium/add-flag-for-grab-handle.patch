--- a/chrome/browser/ui/views/frame/tab_strip_region_view.cc
+++ b/chrome/browser/ui/views/frame/tab_strip_region_view.cc
@@ -74,6 +74,7 @@ class FrameGrabHandle : public views::Vi
     // Reserve some space for the frame to be grabbed by, even if the tabstrip
     // is full.
     // TODO(tbergquist): Define this relative to the NTB insets again.
+    if (base::CommandLine::ForCurrentProcess()->HasSwitch("remove-grab-handle")) return gfx::Size(0, 0);
     return gfx::<PERSON>ze(42, 0);
   }
 };
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -68,4 +68,8 @@
      "Disable QR Generator",
      "Disables the QR generator for sharing page links. ungoogled-chromium flag",
      kOsDesktop, FEATURE_VALUE_TYPE(kDisableQRGenerator)},
+    {"remove-grab-handle",
+     "Remove Grab Handle",
+     "Removes the reserved empty space in the tabstrip for moving the window. ungoogled-chromium flag",
+     kOsDesktop, SINGLE_VALUE_TYPE("remove-grab-handle")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
