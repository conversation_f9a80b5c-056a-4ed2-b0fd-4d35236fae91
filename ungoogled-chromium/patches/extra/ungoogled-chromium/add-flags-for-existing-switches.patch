--- a/chrome/browser/about_flags.cc
+++ b/chrome/browser/about_flags.cc
@@ -4929,10 +4929,12 @@ constexpr char kWebiumFlag[] = "webium";
 #include "chrome/browser/ungoogled_flag_choices.h"
 #include "chrome/browser/bromite_flag_choices.h"
 #include "chrome/browser/ungoogled_platform_flag_choices.h"
+#include "chrome/browser/existing_switch_flag_choices.h"
 const FeatureEntry kFeatureEntries[] = {
 #include "chrome/browser/ungoogled_flag_entries.h"
 #include "chrome/browser/bromite_flag_entries.h"
 #include "chrome/browser/ungoogled_platform_flag_entries.h"
+#include "chrome/browser/existing_switch_flag_entries.h"
 // Include generated flags for flag unexpiry; see //docs/flag_expiry.md and
 // //tools/flags/generate_unexpire_flags.py.
 #include "build/chromeos_buildflags.h"
--- /dev/null
+++ b/chrome/browser/existing_switch_flag_choices.h
@@ -0,0 +1,19 @@
+// Copyright (c) 2023 The ungoogled-chromium Authors. All rights reserved.
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+#ifndef CHROME_BROWSER_EXISTING_SWITCH_FLAG_CHOICES_H_
+#define CHROME_BROWSER_EXISTING_SWITCH_FLAG_CHOICES_H_
+const FeatureEntry::Choice kWebRTCIPPolicy[] = {
+    {"Disable non proxied udp", "", ""},
+    {"Default",
+     "webrtc-ip-handling-policy",
+     "default"},
+    {"Default public and private interfaces",
+     "webrtc-ip-handling-policy",
+     "default_public_and_private_interfaces"},
+    {"Default public interface only",
+     "webrtc-ip-handling-policy",
+     "default_public_interface_only"},
+};
+#endif  // CHROME_BROWSER_EXISTING_SWITCH_FLAG_CHOICES_H_
--- /dev/null
+++ b/chrome/browser/existing_switch_flag_entries.h
@@ -0,0 +1,43 @@
+// Copyright (c) 2023 The ungoogled-chromium Authors. All rights reserved.
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+#ifndef CHROME_BROWSER_EXISTING_SWITCH_FLAG_ENTRIES_H_
+#define CHROME_BROWSER_EXISTING_SWITCH_FLAG_ENTRIES_H_
+    {"disable-top-sites",
+     "Disable Top Sites",
+     "Disables the top sites and most visited entries on the new tab page. Chromium feature, ungoogled-chromium flag.",
+     kOsAll, SINGLE_VALUE_TYPE("disable-top-sites")},
+    {"disable-webgl",
+     "Disable WebGL",
+     "Disable all versions of WebGL. Chromium feature, ungoogled-chromium flag.",
+     kOsAll, SINGLE_VALUE_TYPE("disable-webgl")},
+    {"enable-low-end-device-mode",
+     "Enable low-end device mode",
+     "Force low-end device mode when set. Chromium feature, ungoogled-chromium flag.",
+     kOsAll, SINGLE_VALUE_TYPE("enable-low-end-device-mode")},
+    {"force-dark-mode",
+     "Force Dark Mode",
+     "Forces dark mode in UI for platforms that support it. Chromium feature, ungoogled-chromium flag.",
+     kOsAll, SINGLE_VALUE_TYPE("force-dark-mode")},
+    {"incognito",
+     "Start in incognito",
+     "Start in Incognito. Chromium feature, ungoogled-chromium flag.",
+     kOsDesktop, SINGLE_VALUE_TYPE("incognito")},
+    {"no-default-browser-check",
+     "No default browser check",
+     "Disables the default browser check. Chromium feature, ungoogled-chromium flag.",
+     kOsDesktop, SINGLE_VALUE_TYPE("no-default-browser-check")},
+    {"no-pings",
+     "No Pings",
+     "Don't send hyperlink auditing pings. Chromium feature, ungoogled-chromium flag.",
+     kOsAll, SINGLE_VALUE_TYPE("no-pings")},
+    {"start-maximized",
+     "Start Maximized",
+     "Starts the browser maximized, regardless of any previous settings. Chromium feature, ungoogled-chromium flag.",
+     kOsDesktop, SINGLE_VALUE_TYPE("start-maximized")},
+    {"webrtc-ip-handling-policy",
+     "WebRTC IP policy",
+     "Restrict which IP addresses and interfaces WebRTC uses. Chromium feature, ungoogled-chromium flag.",
+     kOsAll, MULTI_VALUE_TYPE(kWebRTCIPPolicy)},
+#endif  // CHROME_BROWSER_EXISTING_SWITCH_FLAG_ENTRIES_H_
