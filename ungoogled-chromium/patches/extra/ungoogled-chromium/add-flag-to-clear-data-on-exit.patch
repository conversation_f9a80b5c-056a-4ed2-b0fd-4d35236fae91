--- a/chrome/browser/browser_features.cc
+++ b/chrome/browser/browser_features.cc
@@ -368,4 +368,5 @@ BASE_FEATURE(kRemovalOfIWAsFromTabCaptur
              "RemovalOfIWAsFromTabCapture",
              base::FEATURE_ENABLED_BY_DEFAULT);
 
+BASE_FEATURE(kClearDataOnExit, "ClearDataOnExit", base::FEATURE_DISABLED_BY_DEFAULT);
 }  // namespace features
--- a/chrome/browser/browser_features.h
+++ b/chrome/browser/browser_features.h
@@ -121,6 +121,7 @@ BASE_DECLARE_FEATURE(kRemovalOfIWAsFromT
 // module, e.g.
 // //chrome/browser/<foo_module>/features.h
 //
+BASE_DECLARE_FEATURE(kClearDataOnExit);
 }  // namespace features
 
 #endif  // CHROME_BROWSER_BROWSER_FEATURES_H_
--- a/chrome/browser/browsing_data/chrome_browsing_data_lifetime_manager.cc
+++ b/chrome/browser/browsing_data/chrome_browsing_data_lifetime_manager.cc
@@ -19,6 +19,7 @@
 #include "base/task/task_traits.h"
 #include "base/values.h"
 #include "build/build_config.h"
+#include "chrome/browser/browser_features.h"
 #include "chrome/browser/browser_process.h"
 #include "chrome/browser/browsing_data/chrome_browsing_data_remover_constants.h"
 #include "chrome/browser/browsing_data/chrome_browsing_data_remover_delegate.h"
@@ -148,6 +149,21 @@ class BrowsingDataRemoverObserver
 #endif
 };
 
+uint64_t AllOriginTypeMask() {
+  return content::BrowsingDataRemover::ORIGIN_TYPE_PROTECTED_WEB |
+         content::BrowsingDataRemover::ORIGIN_TYPE_UNPROTECTED_WEB;
+}
+
+uint64_t AllRemoveMask() {
+  return content::BrowsingDataRemover::DATA_TYPE_CACHE |
+         content::BrowsingDataRemover::DATA_TYPE_DOWNLOADS |
+         chrome_browsing_data_remover::DATA_TYPE_CONTENT_SETTINGS |
+         chrome_browsing_data_remover::DATA_TYPE_FORM_DATA |
+         chrome_browsing_data_remover::DATA_TYPE_HISTORY |
+         chrome_browsing_data_remover::DATA_TYPE_PASSWORDS |
+         chrome_browsing_data_remover::DATA_TYPE_SITE_DATA;
+}
+
 uint64_t GetOriginTypeMask(const base::Value::List& data_types) {
   uint64_t result = 0;
   for (const auto& data_type : data_types) {
@@ -326,9 +342,10 @@ void ChromeBrowsingDataLifetimeManager::
   const base::Value::List& data_types = profile_->GetPrefs()->GetList(
       browsing_data::prefs::kClearBrowsingDataOnExitList);
 
-  if (!data_types.empty() &&
+  bool cdoe = base::FeatureList::IsEnabled(features::kClearDataOnExit);
+  if (cdoe || (!data_types.empty() &&
       IsConditionSatisfiedForBrowsingDataRemoval(GetSyncTypesForPolicyPref(
-          profile_, browsing_data::prefs::kClearBrowsingDataOnExitList))) {
+          profile_, browsing_data::prefs::kClearBrowsingDataOnExitList)))) {
     profile_->GetPrefs()->SetBoolean(
         browsing_data::prefs::kClearBrowsingDataOnExitDeletionPending, true);
     auto* remover = profile_->GetBrowsingDataRemover();
@@ -339,8 +356,8 @@ void ChromeBrowsingDataLifetimeManager::
       DCHECK(keep_browser_alive);
 #endif
     remover->RemoveAndReply(base::Time(), base::Time::Max(),
-                            GetRemoveMask(data_types),
-                            GetOriginTypeMask(data_types),
+                            cdoe ? AllRemoveMask() : GetRemoveMask(data_types),
+                            cdoe ? AllOriginTypeMask() : GetOriginTypeMask(data_types),
                             BrowsingDataRemoverObserver::Create(
                                 remover, /*filterable_deletion=*/true, profile_,
                                 keep_browser_alive));
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -56,4 +56,8 @@
      "Keep old history",
      "Keep history older than 3 months. ungoogled-chromium flag",
      kOsAll, SINGLE_VALUE_TYPE("keep-old-history")},
+    {"clear-data-on-exit",
+     "Clear data on exit",
+     "Clears all browsing data on exit. ungoogled-chromium flag",
+     kOsDesktop, FEATURE_VALUE_TYPE(features::kClearDataOnExit)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
