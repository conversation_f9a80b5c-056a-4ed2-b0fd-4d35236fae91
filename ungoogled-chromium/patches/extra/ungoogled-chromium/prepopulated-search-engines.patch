# Disables Site search Starter Pack
#
--- a/components/omnibox/common/omnibox_features.cc
+++ b/components/omnibox/common/omnibox_features.cc
@@ -288,7 +288,7 @@ BASE_FEATURE(kOmniboxSiteSearch, "Omnibo
 // Enables additional site search providers for the Site search Starter Pack.
 BASE_FEATURE(kStarterPackExpansion,
              "StarterPackExpansion",
-             enable_if(!IS_ANDROID && !IS_IOS));
+             base::FEATURE_DISABLED_BY_DEFAULT);
 
 // Enables an informational IPH message at the bottom of the Omnibox directing
 // users to certain starter pack engines.
