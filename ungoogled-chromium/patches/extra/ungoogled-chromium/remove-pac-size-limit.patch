--- a/net/proxy_resolution/pac_file_fetcher_impl.cc
+++ b/net/proxy_resolution/pac_file_fetcher_impl.cc
@@ -359,13 +359,6 @@ bool PacFileFetcherImpl::ConsumeBytesRea
     return false;
   }
 
-  // Enforce maximum size bound.
-  if (num_bytes + bytes_read_so_far_.size() >
-      static_cast<size_t>(max_response_bytes_)) {
-    result_code_ = ERR_FILE_TOO_BIG;
-    request->Cancel();
-    return false;
-  }
 
   bytes_read_so_far_.append(buf_->data(), num_bytes);
   return true;
