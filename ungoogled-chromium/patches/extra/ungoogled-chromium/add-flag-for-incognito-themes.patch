--- a/chrome/browser/ui/views/frame/browser_frame.cc
+++ b/chrome/browser/ui/views/frame/browser_frame.cc
@@ -707,5 +707,7 @@ bool BrowserFrame::RegenerateFrameOnThem
 }
 
 bool BrowserFrame::IsIncognitoBrowser() const {
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch("enable-incognito-themes"))
+    return false;
   return browser_view_->browser()->profile()->IsIncognitoProfile();
 }
--- a/chrome/browser/ui/webui/ntp/ntp_resource_cache.cc
+++ b/chrome/browser/ui/webui/ntp/ntp_resource_cache.cc
@@ -60,6 +60,7 @@
 #include "ui/base/webui/jstemplate_builder.h"
 #include "ui/base/webui/web_ui_util.h"
 #include "ui/color/color_provider.h"
+#include "ui/color/color_provider_manager.h"
 #include "ui/gfx/color_utils.h"
 #include "ui/native_theme/native_theme.h"
 
@@ -467,7 +468,9 @@ void NTPResourceCache::CreateNewTabIncog
       profile_->GetPrefs()->GetString(prefs::kCurrentThemeID);
 
   // Colors.
-  const ui::ColorProvider& cp = web_contents->GetColorProvider();
+  auto key = native_theme->GetColorProviderKey(nullptr);
+  key.color_mode = ui::ColorProviderKey::ColorMode::kDark;
+  const ui::ColorProvider& cp = *ui::ColorProviderManager::Get().GetColorProviderFor(key);
   substitutions["colorBackground"] = color_utils::SkColorToRgbaString(
       GetThemeColor(native_theme, cp, kColorNewTabPageBackground));
   substitutions["backgroundPosition"] = GetNewTabBackgroundPositionCSS(tp);
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -112,4 +112,8 @@
      "Hide Fullscreen Exit UI",
      "Hides the \"X\" that appears when the mouse cursor is moved towards the top of the window in fullscreen mode. Additionally, this hides the \"Press F11 to exit full screen\" popup. ungoogled-chromium flag.",
      kOsDesktop, SINGLE_VALUE_TYPE("hide-fullscreen-exit-ui")},
+    {"enable-incognito-themes",
+     "Enable themes in Incognito mode",
+     "Allows themes to override Google's built-in Incognito theming. ungoogled-chromium flag.",
+     kOsDesktop, SINGLE_VALUE_TYPE("enable-incognito-themes")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
