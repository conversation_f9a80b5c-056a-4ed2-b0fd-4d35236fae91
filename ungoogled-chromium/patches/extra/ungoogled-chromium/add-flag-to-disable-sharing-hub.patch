--- a/chrome/browser/sharing_hub/sharing_hub_features.cc
+++ b/chrome/browser/sharing_hub/sharing_hub_features.cc
@@ -4,6 +4,7 @@
 
 #include "chrome/browser/sharing_hub/sharing_hub_features.h"
 
+#include "base/command_line.h"
 #include "build/build_config.h"
 #include "chrome/browser/profiles/profile.h"
 #include "chrome/common/pref_names.h"
@@ -36,6 +37,7 @@ bool ScreenshotsDisabledByPolicy(content
 }  // namespace
 
 bool SharingHubOmniboxEnabled(content::BrowserContext* context) {
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch("disable-sharing-hub")) return false;
 #if BUILD<PERSON>AG(IS_CHROMEOS)
   return false;
 #else
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -96,4 +96,8 @@
      "Custom HTTP Accept Header",
      "Set a custom value for the Accept header which is sent by the browser with every HTTP request.  (e.g. `text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8`). ungoogled-chromium flag.",
      kOsAll, ORIGIN_LIST_VALUE_TYPE("http-accept-header", "")},
+    {"disable-sharing-hub",
+     "Disable Sharing Hub",
+     "Disables the sharing hub button. ungoogled-chromium flag.",
+     kOsDesktop, SINGLE_VALUE_TYPE("disable-sharing-hub")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
