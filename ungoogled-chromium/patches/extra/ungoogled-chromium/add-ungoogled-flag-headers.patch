--- a/chrome/browser/about_flags.cc
+++ b/chrome/browser/about_flags.cc
@@ -4925,7 +4925,13 @@ constexpr char kWebiumFlag[] = "webium";
 // calculate and verify checksum.
 //
 // When adding a new choice, add it to the end of the list.
+#include "chrome/browser/ungoogled_flag_choices.h"
+#include "chrome/browser/bromite_flag_choices.h"
+#include "chrome/browser/ungoogled_platform_flag_choices.h"
 const FeatureEntry kFeatureEntries[] = {
+#include "chrome/browser/ungoogled_flag_entries.h"
+#include "chrome/browser/bromite_flag_entries.h"
+#include "chrome/browser/ungoogled_platform_flag_entries.h"
 // Include generated flags for flag unexpiry; see //docs/flag_expiry.md and
 // //tools/flags/generate_unexpire_flags.py.
 #include "build/chromeos_buildflags.h"
--- /dev/null
+++ b/chrome/browser/bromite_flag_choices.h
@@ -0,0 +1,7 @@
+// Copyright (c) 2020 The ungoogled-chromium Authors. All rights reserved.
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+#ifndef CHROME_BROWSER_BROMITE_FLAG_CHOICES_H_
+#define CHROME_BROWSER_BROMITE_FLAG_CHOICES_H_
+#endif  // CHROME_BROWSER_BROMITE_FLAG_CHOICES_H_
--- /dev/null
+++ b/chrome/browser/bromite_flag_entries.h
@@ -0,0 +1,7 @@
+// Copyright (c) 2020 The ungoogled-chromium Authors. All rights reserved.
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+#ifndef CHROME_BROWSER_BROMITE_FLAG_ENTRIES_H_
+#define CHROME_BROWSER_BROMITE_FLAG_ENTRIES_H_
+#endif  // CHROME_BROWSER_BROMITE_FLAG_ENTRIES_H_
--- /dev/null
+++ b/chrome/browser/ungoogled_flag_choices.h
@@ -0,0 +1,7 @@
+// Copyright (c) 2020 The ungoogled-chromium Authors. All rights reserved.
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+#ifndef CHROME_BROWSER_UNGOOGLED_FLAG_CHOICES_H_
+#define CHROME_BROWSER_UNGOOGLED_FLAG_CHOICES_H_
+#endif  // CHROME_BROWSER_UNGOOGLED_FLAG_CHOICES_H_
--- /dev/null
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -0,0 +1,7 @@
+// Copyright (c) 2020 The ungoogled-chromium Authors. All rights reserved.
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+#ifndef CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
+#define CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
+#endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- /dev/null
+++ b/chrome/browser/ungoogled_platform_flag_choices.h
@@ -0,0 +1,7 @@
+// Copyright (c) 2020 The ungoogled-chromium Authors. All rights reserved.
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+#ifndef CHROME_BROWSER_UNGOOGLED_PLATFORM_FLAG_CHOICES_H_
+#define CHROME_BROWSER_UNGOOGLED_PLATFORM_FLAG_CHOICES_H_
+#endif  // CHROME_BROWSER_UNGOOGLED_PLATFORM_FLAG_CHOICES_H_
--- /dev/null
+++ b/chrome/browser/ungoogled_platform_flag_entries.h
@@ -0,0 +1,7 @@
+// Copyright (c) 2020 The ungoogled-chromium Authors. All rights reserved.
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+#ifndef CHROME_BROWSER_UNGOOGLED_PLATFORM_FLAG_ENTRIES_H_
+#define CHROME_BROWSER_UNGOOGLED_PLATFORM_FLAG_ENTRIES_H_
+#endif  // CHROME_BROWSER_UNGOOGLED_PLATFORM_FLAG_ENTRIES_H_
