--- a/chrome/browser/ui/views/tabs/tab.cc
+++ b/chrome/browser/ui/views/tabs/tab.cc
@@ -1163,7 +1163,11 @@ void Tab::UpdateIconVisibility() {
     // Close button is shown on active tabs regardless of the size.
     showing_close_button_ = true;
 #endif  // BUILDFLAG(IS_CHROMEOS)
+    if (base::CommandLine::ForCurrentProcess()->HasSwitch("hide-tab-close-buttons")) {
+      showing_close_button_ = false;
+    } else {
     available_width -= close_button_width;
+    }
 
     showing_alert_indicator_ =
         has_alert_icon && alert_icon_width <= available_width;
@@ -1192,6 +1196,8 @@ void Tab::UpdateIconVisibility() {
         !controller_->IsLockedForOnTask() &&
 #endif
         large_enough_for_close_button;
+    if (base::CommandLine::ForCurrentProcess()->HasSwitch("hide-tab-close-buttons"))
+      showing_close_button_ = false;
     if (showing_close_button_) {
       available_width -= close_button_width;
     }
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -84,4 +84,8 @@
      "Tab Hover Cards",
      "Allows removing the tab hover cards or using a tooltip as a replacement. ungoogled-chromium flag.",
      kOsDesktop, MULTI_VALUE_TYPE(kTabHoverCards)},
+    {"hide-tab-close-buttons",
+     "Hide tab close buttons",
+     "Hides the close buttons on tabs. ungoogled-chromium flag.",
+     kOsDesktop, SINGLE_VALUE_TYPE("hide-tab-close-buttons")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
