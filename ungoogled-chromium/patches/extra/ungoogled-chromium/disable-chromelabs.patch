--- a/chrome/browser/ui/toolbar/chrome_labs/chrome_labs_utils.cc
+++ b/chrome/browser/ui/toolbar/chrome_labs/chrome_labs_utils.cc
@@ -140,35 +140,6 @@ bool AreNewChromeLabsExperimentsAvailabl
 }
 
 bool IsChromeLabsEnabled() {
-  // Always early out on the stable channel regardless of other conditions.
-  if (chrome::GetChannel() == version_info::Channel::STABLE) {
-    return false;
-  }
-  if (base::CommandLine::ForCurrentProcess()->HasSwitch(
-          variations::switches::kEnableBenchmarking)) {
-    return true;
-  }
-  // Could be null in unit tests.
-  if (!g_browser_process->local_state()) {
-    return false;
-  }
-  if (g_browser_process->local_state()->GetInteger(
-          chrome_labs_prefs::kChromeLabsActivationThreshold) ==
-      chrome_labs_prefs::kChromeLabsActivationThresholdDefaultValue) {
-    g_browser_process->local_state()->SetInteger(
-        chrome_labs_prefs::kChromeLabsActivationThreshold,
-        base::RandInt(1, 100));
-  }
-
-  // The percentage of users that should see the feature.
-  const int kChromeLabsActivationPercentage = 99;
-
-  if (force_activation_for_testing ||
-      g_browser_process->local_state()->GetInteger(
-          chrome_labs_prefs::kChromeLabsActivationThreshold) <=
-          kChromeLabsActivationPercentage) {
-    return true;
-  }
   return false;
 }
 
