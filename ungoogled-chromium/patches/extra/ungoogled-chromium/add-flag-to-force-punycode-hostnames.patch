# Add flag to force punycode in hostnames instead of Unicode when displaying Internationalized Domain Names (IDNs) to mitigate homograph attacks

--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -20,4 +20,8 @@
      "Disable beforeunload",
      "Disables JavaScript dialog boxes triggered by beforeunload. ungoogled-chromium flag.",
      k<PERSON><PERSON><PERSON>, SINGLE_VALUE_TYPE("disable-beforeunload")},
+    {"force-punycode-hostnames",
+     "Force punycode hostnames",
+     "Force punycode in hostnames instead of Unicode when displaying Internationalized Domain Names (IDNs). ungoogled-chromium flag.",
+     kOsAll, SINGLE_VALUE_TYPE("force-punycode-hostnames")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/components/url_formatter/url_formatter.cc
+++ b/components/url_formatter/url_formatter.cc
@@ -10,6 +10,7 @@
 #include <utility>
 #include <vector>
 
+#include "base/command_line.h"
 #include "base/memory/raw_ptr.h"
 #include "base/no_destructor.h"
 #include "base/numerics/safe_conversions.h"
@@ -316,6 +317,13 @@ IDNConversionResult IDNToUnicodeWithAdju
   host16.reserve(host.length());
   host16.insert(host16.end(), host.begin(), host.end());
 
+  if (base::CommandLine::ForCurrentProcess()->HasSwitch("force-punycode-hostnames")) {
+    // Leave as punycode.
+    IDNConversionResult result;
+    result.result = host16;
+    return result;
+  }
+
   // Compute the top level domain to be used in spoof checks later.
   std::string_view top_level_domain;
   std::u16string top_level_domain_unicode;
