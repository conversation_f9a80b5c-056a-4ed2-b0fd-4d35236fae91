--- a/chrome/browser/ui/browser_command_controller.cc
+++ b/chrome/browser/ui/browser_command_controller.cc
@@ -1585,7 +1585,8 @@ void BrowserCommandController::InitComma
   command_updater_.UpdateCommandEnabled(IDC_WINDOW_CLOSE_OTHER_TABS,
                                         normal_window);
 
-  const bool enable_tab_search_commands = browser_->is_type_normal();
+  const bool enable_tab_search_commands = browser_->is_type_normal() &&
+      !base::CommandLine::ForCurrentProcess()->HasSwitch("remove-tabsearch-button");
   command_updater_.UpdateCommandEnabled(IDC_TAB_SEARCH,
                                         enable_tab_search_commands);
   command_updater_.UpdateCommandEnabled(IDC_TAB_SEARCH_CLOSE,
--- a/chrome/browser/ui/ui_features.cc
+++ b/chrome/browser/ui/ui_features.cc
@@ -594,6 +594,10 @@ static std::string GetCountryCode() {
 }
 
 bool HasTabSearchToolbarButton() {
+  if (base::CommandLine::ForCurrentProcess()->Has<PERSON>witch("remove-tabsearch-button")) {
+    return false;
+  }
+
   static const bool is_tab_search_moving = [] {
     if (GetCountryCode() == "us" &&
         base::FeatureList::IsEnabled(
--- a/chrome/browser/ui/views/frame/browser_view.cc
+++ b/chrome/browser/ui/views/frame/browser_view.cc
@@ -5148,6 +5148,7 @@ void BrowserView::AddedToWidget() {
 
   toolbar_->Init();
 
+  if (!base::CommandLine::ForCurrentProcess()->HasSwitch("remove-tabsearch-button"))
   if (GetIsNormalType()) {
     if (features::HasTabSearchToolbarButton()) {
       tab_search_bubble_host_ = std::make_unique<TabSearchBubbleHost>(
--- a/chrome/browser/ui/views/frame/tab_strip_region_view.cc
+++ b/chrome/browser/ui/views/frame/tab_strip_region_view.cc
@@ -4,6 +4,7 @@
 
 #include "chrome/browser/ui/views/frame/tab_strip_region_view.h"
 
+#include "base/command_line.h"
 #include "base/functional/bind.h"
 #include "base/strings/utf_string_conversions.h"
 #include "base/task/single_thread_task_runner.h"
@@ -127,6 +128,7 @@ TabStripRegionView::TabStripRegionView(s
   std::unique_ptr<TabStripActionContainer> tab_strip_action_container;
   std::unique_ptr<ProductSpecificationsButton> product_specifications_button;
   if (browser &&
+      !base::CommandLine::ForCurrentProcess()->HasSwitch("remove-tabsearch-button") &&
       (browser->GetType() == BrowserWindowInterface::Type::TYPE_NORMAL)) {
     if (features::HasTabSearchToolbarButton()) {
       tab_strip_action_container = std::make_unique<TabStripActionContainer>(
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -60,4 +60,8 @@
      "Clear data on exit",
      "Clears all browsing data on exit. ungoogled-chromium flag",
      kOsDesktop, FEATURE_VALUE_TYPE(features::kClearDataOnExit)},
+    {"remove-tabsearch-button",
+     "Remove Tabsearch Button",
+     "Removes the tabsearch button from the tabstrip. ungoogled-chromium flag",
+     kOsDesktop, SINGLE_VALUE_TYPE("remove-tabsearch-button")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
