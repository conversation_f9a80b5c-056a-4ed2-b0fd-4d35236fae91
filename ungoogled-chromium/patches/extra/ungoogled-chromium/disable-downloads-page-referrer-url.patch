--- a/components/download/public/common/download_features.cc
+++ b/components/download/public/common/download_features.cc
@@ -44,7 +44,7 @@ BASE_FEATURE(kRefreshExpirationDate,
 
 BASE_FEATURE(kDisplayInitiatorOrigin,
              "DownloadsDisplayInitiatorOrigin",
-             base::FEATURE_ENABLED_BY_DEFAULT);
+             base::FEATURE_DISABLED_BY_DEFAULT);
 
 BASE_FEATURE(kDownloadNotificationServiceUnifiedAPI,
              "DownloadNotificationServiceUnifiedAPI",
