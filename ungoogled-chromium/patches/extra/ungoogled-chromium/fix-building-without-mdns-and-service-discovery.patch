# Fix building with enable_service_discovery=false and enable_mds=false

--- a/chrome/browser/media/router/discovery/mdns/dns_sd_device_lister.cc
+++ b/chrome/browser/media/router/discovery/mdns/dns_sd_device_lister.cc
@@ -40,37 +40,22 @@ DnsSdDeviceLister::DnsSdDeviceLister(
 DnsSdDeviceLister::~DnsSdDeviceLister() = default;
 
 void DnsSdDeviceLister::Discover() {
-#if BUILDFLAG(ENABLE_SERVICE_DISCOVERY)
-  if (!device_lister_) {
-    device_lister_ = local_discovery::ServiceDiscoveryDeviceLister::Create(
-        this, service_discovery_client_, service_type_);
-    device_lister_->Start();
-  }
-  device_lister_->DiscoverNewDevices();
-#endif
 }
 
 void DnsSdDeviceLister::Reset() {
-  device_lister_.reset();
 }
 
 void DnsSdDeviceLister::OnDeviceChanged(
     const std::string& service_type,
     bool added,
     const ServiceDescription& service_description) {
-  DnsSdService service;
-  FillServiceInfo(service_description, &service);
-  delegate_->ServiceChanged(device_lister_->service_type(), added, service);
 }
 
 void DnsSdDeviceLister::OnDeviceRemoved(const std::string& service_type,
                                         const std::string& service_name) {
-  delegate_->ServiceRemoved(service_type, service_name);
 }
 
 void DnsSdDeviceLister::OnDeviceCacheFlushed(const std::string& service_type) {
-  delegate_->ServicesFlushed(device_lister_->service_type());
-  device_lister_->DiscoverNewDevices();
 }
 
 void DnsSdDeviceLister::OnPermissionRejected() {
