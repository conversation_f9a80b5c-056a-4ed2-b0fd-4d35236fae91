--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -132,4 +132,8 @@
      "Reduced System Info",
      "Reduces the amount of system information obatainable through headers and javascript, also causes hardwareConcurrency to respond with two cores. ungoogled-chromium flag.",
      kOsAll, FEATURE_VALUE_TYPE(blink::features::kReducedSystemInfo)},
+    {"remove-client-hints",
+     "Remove Client Hints",
+     "Removes client hints (information sent to servers about your system, similar to a user agent). ungoogled-chromium flag.",
+     kOsAll, FEATURE_VALUE_TYPE(blink::features::kRemoveClientHints)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/content/browser/client_hints/client_hints.cc
+++ b/content/browser/client_hints/client_hints.cc
@@ -821,6 +821,8 @@ void UpdateNavigationRequestClientUaHead
     net::HttpRequestHeaders* headers,
     const std::optional<GURL>& request_url) {
   DCHECK(frame_tree_node);
+  if (base::FeatureList::IsEnabled(blink::features::kRemoveClientHints))
+    return;
   if (!ShouldAddClientHints(origin, frame_tree_node, delegate, request_url)) {
     return;
   }
@@ -842,6 +844,8 @@ void AddRequestClientHintsHeaders(
     FrameTreeNode* frame_tree_node,
     const network::ParsedPermissionsPolicy& container_policy,
     const std::optional<GURL>& request_url) {
+  if (base::FeatureList::IsEnabled(blink::features::kRemoveClientHints))
+    return;
   ClientHintsExtendedData data(origin, frame_tree_node, delegate, request_url);
   UpdateIFramePermissionsPolicyWithDelegationSupportForClientHints(
       data, container_policy);
--- a/third_party/blink/common/features.cc
+++ b/third_party/blink/common/features.cc
@@ -21,6 +21,7 @@ namespace blink::features {
 
 BASE_FEATURE(kDisableLinkDrag, "DisableLinkDrag", base::FEATURE_DISABLED_BY_DEFAULT);
 BASE_FEATURE(kReducedSystemInfo, "ReducedSystemInfo", base::FEATURE_DISABLED_BY_DEFAULT);
+BASE_FEATURE(kRemoveClientHints, "RemoveClientHints", base::FEATURE_DISABLED_BY_DEFAULT);
 
 // -----------------------------------------------------------------------------
 // Feature definitions and associated constants (feature params, et cetera)
--- a/third_party/blink/public/common/features.h
+++ b/third_party/blink/public/common/features.h
@@ -24,6 +24,7 @@ namespace features {
 
 BLINK_COMMON_EXPORT BASE_DECLARE_FEATURE(kDisableLinkDrag);
 BLINK_COMMON_EXPORT BASE_DECLARE_FEATURE(kReducedSystemInfo);
+BLINK_COMMON_EXPORT BASE_DECLARE_FEATURE(kRemoveClientHints);
 
 // -----------------------------------------------------------------------------
 // Feature declarations and associated constants (feature params, et cetera)
--- a/third_party/blink/renderer/core/frame/navigator_ua.cc
+++ b/third_party/blink/renderer/core/frame/navigator_ua.cc
@@ -5,6 +5,7 @@
 #include "third_party/blink/renderer/core/frame/navigator_ua.h"
 
 #include "base/compiler_specific.h"
+#include "third_party/blink/public/common/features.h"
 #include "third_party/blink/public/common/user_agent/user_agent_metadata.h"
 #include "third_party/blink/renderer/core/frame/navigator_ua_data.h"
 #include "third_party/blink/renderer/platform/wtf/text/wtf_string.h"
@@ -14,6 +15,8 @@ namespace blink {
 NavigatorUAData* NavigatorUA::userAgentData() {
   NavigatorUAData* ua_data =
       MakeGarbageCollected<NavigatorUAData>(GetUAExecutionContext());
+  if (base::FeatureList::IsEnabled(blink::features::kRemoveClientHints))
+    return ua_data;
 
   UserAgentMetadata metadata = GetUserAgentMetadata();
   ua_data->SetBrandVersionList(metadata.brand_version_list);
--- a/third_party/blink/renderer/core/loader/frame_fetch_context.cc
+++ b/third_party/blink/renderer/core/loader/frame_fetch_context.cc
@@ -52,6 +52,7 @@
 #include "third_party/blink/public/common/associated_interfaces/associated_interface_provider.h"
 #include "third_party/blink/public/common/client_hints/client_hints.h"
 #include "third_party/blink/public/common/device_memory/approximated_device_memory.h"
+#include "third_party/blink/public/common/features.h"
 #include "third_party/blink/public/common/switches.h"
 #include "third_party/blink/public/mojom/fetch/fetch_api_request.mojom-blink.h"
 #include "third_party/blink/public/mojom/loader/request_context_frame_type.mojom-blink.h"
@@ -528,6 +529,8 @@ void FrameFetchContext::ModifyRequestFor
 void FrameFetchContext::AddClientHintsIfNecessary(
     const std::optional<float> resource_width,
     ResourceRequest& request) {
+  if (base::FeatureList::IsEnabled(blink::features::kRemoveClientHints))
+    return;
   if (GetResourceFetcherProperties().IsDetached()) {
     return;
   }
