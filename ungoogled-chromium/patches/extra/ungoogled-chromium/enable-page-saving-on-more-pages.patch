# Add more URL schemes allowed for saving

--- a/chrome/browser/ui/browser_commands.cc
+++ b/chrome/browser/ui/browser_commands.cc
@@ -611,11 +611,6 @@ int GetContentRestrictions(const Browser
     CoreTabHelper* core_tab_helper =
         CoreTabHelper::FromWebContents(current_tab);
     content_restrictions = core_tab_helper->content_restrictions();
-    NavigationEntry* last_committed_entry =
-        current_tab->GetController().GetLastCommittedEntry();
-    if (!content::IsSavableURL(last_committed_entry->GetURL())) {
-      content_restrictions |= CONTENT_RESTRICTION_SAVE;
-    }
   }
   return content_restrictions;
 }
@@ -1938,8 +1933,7 @@ bool CanSavePage(const Browser* browser)
       policy::DownloadRestriction::ALL_FILES) {
     return false;
   }
-  return !browser->is_type_devtools() &&
-         !(GetContentRestrictions(browser) & CONTENT_RESTRICTION_SAVE);
+  return true;
 }
 
 void Print(<PERSON><PERSON><PERSON>* browser) {
--- a/components/offline_pages/core/offline_page_model.cc
+++ b/components/offline_pages/core/offline_page_model.cc
@@ -21,7 +21,7 @@ OfflinePageModel::SavePageParams::~SaveP
 
 // static
 bool OfflinePageModel::CanSaveURL(const GURL& url) {
-  return url.is_valid() && url.SchemeIsHTTPOrHTTPS();
+  return url.is_valid();
 }
 
 OfflinePageModel::OfflinePageModel() = default;
--- a/content/common/url_schemes.cc
+++ b/content/common/url_schemes.cc
@@ -24,13 +24,21 @@ namespace {
 bool g_registered_url_schemes = false;
 
 const char* const kDefaultSavableSchemes[] = {
+  url::kAboutScheme,
+  url::kBlobScheme,
+  url::kContentScheme,
   url::kHttpScheme,
   url::kHttpsScheme,
   url::kFileScheme,
   url::kFileSystemScheme,
   kChromeDevToolsScheme,
   kChromeUIScheme,
-  url::kDataScheme
+  url::kDataScheme,
+  url::kJavaScriptScheme,
+  url::kMailToScheme,
+  url::kWsScheme,
+  url::kWssScheme,
+  kViewSourceScheme
 };
 
 // These lists are lazily initialized below and are leaked on shutdown to
--- a/content/public/common/url_utils.cc
+++ b/content/public/common/url_utils.cc
@@ -33,11 +33,7 @@ bool HasWebUIOrigin(const url::Origin& o
 }
 
 bool IsSavableURL(const GURL& url) {
-  for (auto& scheme : GetSavableSchemes()) {
-    if (url.SchemeIs(scheme))
-      return true;
-  }
-  return false;
+  return true;
 }
 
 bool IsURLHandledByNetworkStack(const GURL& url) {
