# Disables the intranet redirect detector. It generates extra DNS requests and the functionality using this is disabled
# See this page for more information: https://mikewest.org/2012/02/chrome-connects-to-three-random-domains-at-startup

--- a/chrome/browser/intranet_redirect_detector.cc
+++ b/chrome/browser/intranet_redirect_detector.cc
@@ -117,9 +117,7 @@ void IntranetRedirectDetector::FinishSle
   simple_loaders_.clear();
   resulting_origins_.clear();
 
-  const base::CommandLine* cmd_line = base::CommandLine::ForCurrentProcess();
-  if (cmd_line->HasSwitch(switches::kDisableBackgroundNetworking))
-    return;
+  return;
 
   DCHECK(simple_loaders_.empty() && resulting_origins_.empty());
 
