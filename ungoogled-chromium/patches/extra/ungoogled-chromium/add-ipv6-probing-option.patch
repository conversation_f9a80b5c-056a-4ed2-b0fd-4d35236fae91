# Disables IPv6 probing and adds an option to change the IPv6 probing result

--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -4,4 +4,8 @@
 
 #ifndef CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
 #define CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
+    {"set-ipv6-probe-false",
+     "SetIpv6ProbeFalse",
+     "Forces the result of the browser's IPv6 probing (i.e. IPv6 connectivity test) to be unsuccessful. This causes IPv4 addresses to be prioritized over IPv6 addresses. Without this flag, the probing result is set to be successful, which causes IPv6 to be used over IPv4 when possible. ungoogled-chromium flag.",
+     kOsAll, FEATURE_VALUE_TYPE(net::features::kSetIpv6ProbeFalse)},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/net/base/features.cc
+++ b/net/base/features.cc
@@ -19,6 +19,8 @@
 
 namespace net::features {
 
+BASE_FEATURE(kSetIpv6ProbeFalse, "SetIpv6ProbeFalse", base::FEATURE_DISABLED_BY_DEFAULT);
+
 BASE_FEATURE(kAlpsForHttp2, "AlpsForHttp2", base::FEATURE_ENABLED_BY_DEFAULT);
 
 BASE_FEATURE(kAvoidH2Reprioritization,
--- a/net/base/features.h
+++ b/net/base/features.h
@@ -21,6 +21,8 @@
 
 namespace net::features {
 
+NET_EXPORT BASE_DECLARE_FEATURE(kSetIpv6ProbeFalse);
+
 // Enables ALPS extension of TLS 1.3 for HTTP/2, see
 // https://vasilvv.github.io/tls-alps/draft-vvv-tls-alps.html and
 // https://vasilvv.github.io/httpbis-alps/draft-vvv-httpbis-alps.html.
--- a/net/dns/host_resolver_manager.cc
+++ b/net/dns/host_resolver_manager.cc
@@ -152,11 +152,6 @@ const size_t kMaxHostLength = 4096;
 // cached.
 const int kIPv6ProbePeriodMs = 1000;
 
-// Google DNS address used for IPv6 probes.
-const uint8_t kIPv6ProbeAddress[] = {0x20, 0x01, 0x48, 0x60, 0x48, 0x60,
-                                     0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
-                                     0x00, 0x00, 0x88, 0x88};
-
 // True if |hostname| ends with either ".local" or ".local.".
 bool ResemblesMulticastDNSName(std::string_view hostname) {
   return hostname.ends_with(".local") || hostname.ends_with(".local.");
@@ -1497,33 +1492,10 @@ int HostResolverManager::StartIPv6Reacha
     return OK;
   }
 
-  if (probing_ipv6_) {
-    ipv6_request_callbacks_.push_back(std::move(callback));
-    return ERR_IO_PENDING;
-  }
-  // Cache the result for kIPv6ProbePeriodMs (measured from after
-  // StartGloballyReachableCheck() completes).
-  int rv = OK;
-  bool cached = true;
-  if (last_ipv6_probe_time_.is_null() ||
-      (tick_clock_->NowTicks() - last_ipv6_probe_time_).InMilliseconds() >
-          kIPv6ProbePeriodMs) {
-    probing_ipv6_ = true;
-    rv = StartGloballyReachableCheck(
-        IPAddress(kIPv6ProbeAddress), net_log, client_socket_factory,
-        base::BindOnce(&HostResolverManager::FinishIPv6ReachabilityCheck,
-                       weak_ptr_factory_.GetWeakPtr(), std::move(callback)));
-    if (rv != ERR_IO_PENDING) {
-      SetLastIPv6ProbeResult((rv == OK) ? true : false);
-      rv = OK;
-    }
-    cached = false;
-  }
-  net_log.AddEvent(
-      NetLogEventType::HOST_RESOLVER_MANAGER_IPV6_REACHABILITY_CHECK, [&] {
-        return NetLogIPv6AvailableParams(last_ipv6_probe_result_, cached);
-      });
-  return rv;
+  probing_ipv6_ = false;
+  last_ipv6_probe_result_ = !base::FeatureList::IsEnabled(features::kSetIpv6ProbeFalse);
+  last_ipv6_probe_time_ = base::TimeTicks();
+  return OK;
 }
 
 void HostResolverManager::SetLastIPv6ProbeResult(bool last_ipv6_probe_result) {
