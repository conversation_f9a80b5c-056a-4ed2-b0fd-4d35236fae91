# Add flag to disable automatic search engine collection

--- a/chrome/browser/ui/tab_helpers.cc
+++ b/chrome/browser/ui/tab_helpers.cc
@@ -544,7 +544,9 @@ void TabHelpers::AttachTabHelpers(WebCon
       profile, web_contents);
 #endif  // BUILDFLAG(SAFE_BROWSING_AVAILABLE)
   SafetyTipWebContentsObserver::CreateForWebContents(web_contents);
+  if (!base::CommandLine::ForCurrentProcess()->HasSwitch("disable-search-engine-collection")) {
   SearchEngineTabHelper::CreateForWebContents(web_contents);
+  }
   if (site_engagement::SiteEngagementService::IsEnabled()) {
     site_engagement::SiteEngagementService::Helper::CreateForWebContents(
         web_contents,
--- a/chrome/browser/ungoogled_flag_entries.h
+++ b/chrome/browser/ungoogled_flag_entries.h
@@ -12,4 +12,8 @@
      "Handling of extension MIME type requests",
      "Used when deciding how to handle a request for a CRX or User Script MIME type. ungoogled-chromium flag.",
      kOsAll, MULTI_VALUE_TYPE(kExtensionHandlingChoices)},
+    {"disable-search-engine-collection",
+     "Disable search engine collection",
+     "Prevents search engines from being added automatically. ungoogled-chromium flag.",
+     kOsAll, SINGLE_VALUE_TYPE("disable-search-engine-collection")},
 #endif  // CHROME_BROWSER_UNGOOGLED_FLAG_ENTRIES_H_
--- a/chrome/renderer/chrome_render_frame_observer.cc
+++ b/chrome/renderer/chrome_render_frame_observer.cc
@@ -259,14 +259,16 @@ void ChromeRenderFrameObserver::DidFinis
   if (frame->Parent() || frame->IsInFencedFrameTree())
     return;
 
-  GURL osdd_url = frame->GetDocument().OpenSearchDescriptionURL();
-  if (!osdd_url.is_empty()) {
-    mojo::Remote<chrome::mojom::OpenSearchDescriptionDocumentHandler>
-        osdd_handler;
-    render_frame()->GetBrowserInterfaceBroker().GetInterface(
-        osdd_handler.BindNewPipeAndPassReceiver());
-    osdd_handler->PageHasOpenSearchDescriptionDocument(
-        frame->GetDocument().Url(), osdd_url);
+  if (!base::CommandLine::ForCurrentProcess()->HasSwitch("disable-search-engine-collection")) {
+    GURL osdd_url = frame->GetDocument().OpenSearchDescriptionURL();
+    if (!osdd_url.is_empty()) {
+      mojo::Remote<chrome::mojom::OpenSearchDescriptionDocumentHandler>
+          osdd_handler;
+      render_frame()->GetBrowserInterfaceBroker().GetInterface(
+          osdd_handler.BindNewPipeAndPassReceiver());
+      osdd_handler->PageHasOpenSearchDescriptionDocument(
+          frame->GetDocument().Url(), osdd_url);
+    }
   }
 }
 
--- a/components/search_engines/template_url_service.cc
+++ b/components/search_engines/template_url_service.cc
@@ -15,6 +15,7 @@
 #include "base/base64url.h"
 #include "base/check_deref.h"
 #include "base/check_is_test.h"
+#include "base/command_line.h"
 #include "base/containers/contains.h"
 #include "base/containers/fixed_flat_map.h"
 #include "base/containers/flat_map.h"
@@ -402,6 +403,12 @@ bool IsGoogleAiModeUrl(GURL url) {
                        google_util::DISALLOW_NON_STANDARD_PORTS);
 }
 
+bool ShouldAutocollect() {
+  const base::CommandLine& command_line =
+    *base::CommandLine::ForCurrentProcess();
+  return !command_line.HasSwitch("disable-search-engine-collection");
+}
+
 }  // namespace
 
 // TemplateURLService::LessWithPrefix -----------------------------------------
@@ -582,6 +589,7 @@ TemplateURLService::TemplateURLService(
     std::unique_ptr<TemplateURLServiceClient> client,
     const base::RepeatingClosure& dsp_change_callback)
     : prefs_(prefs),
+      should_autocollect_(true),
       search_engine_choice_service_(search_engine_choice_service),
       prepopulate_data_resolver_(prepopulate_data_resolver),
       search_terms_data_(std::move(search_terms_data)),
@@ -665,8 +673,8 @@ bool TemplateURLService::CanAddAutogener
   // that may interfere with search queries).  An easy heuristic for this is
   // whether the user has a TemplateURL that has been manually modified (e.g.,
   // renamed) connected to the same host.
-  return !url.is_valid() || url.host().empty() ||
-         CanAddAutogeneratedKeywordForHost(url.host());
+  return should_autocollect_ && (!url.is_valid() || url.host().empty() ||
+         CanAddAutogeneratedKeywordForHost(url.host()));
 }
 
 bool TemplateURLService::IsPrepopulatedOrDefaultProviderByPolicy(
@@ -2493,6 +2501,8 @@ SyncDataMap TemplateURLService::CreateGU
 }
 
 void TemplateURLService::Init() {
+  should_autocollect_ = ShouldAutocollect();
+
   if (client_) {
     client_->SetOwner(this);
   }
@@ -2658,6 +2668,9 @@ void TemplateURLService::ChangeToLoadedS
 
 bool TemplateURLService::CanAddAutogeneratedKeywordForHost(
     const std::string& host) const {
+  if (!should_autocollect_)
+    return false;
+
   const TemplateURLSet* urls = provider_map_->GetURLsForHost(host);
   if (!urls) {
     return true;
--- a/components/search_engines/template_url_service.h
+++ b/components/search_engines/template_url_service.h
@@ -905,6 +905,8 @@ class TemplateURLService final : public
 
   raw_ref<TemplateURLPrepopulateData::Resolver> prepopulate_data_resolver_;
 
+  bool should_autocollect_; // Whether search engines should be auto-collected
+
   std::unique_ptr<SearchTermsData> search_terms_data_ =
       std::make_unique<SearchTermsData>();
 
