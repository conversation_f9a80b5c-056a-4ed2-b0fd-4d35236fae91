--- a/chrome/browser/chrome_browser_main.cc
+++ b/chrome/browser/chrome_browser_main.cc
@@ -1000,6 +1000,7 @@ int ChromeBrowserMainParts::PreCreateThr
   if (first_run::IsChromeFirstRun()) {
     if (!base::CommandLine::ForCurrentProcess()->HasSwitch(switches::kApp) &&
         !base::CommandLine::ForCurrentProcess()->HasSwitch(switches::kAppId)) {
+      browser_creator_->AddFirstRunTabs({GURL("chrome://ungoogled-first-run")});
       browser_creator_->AddFirstRunTabs(master_prefs_->new_tabs);
     }
   }
--- a/chrome/browser/ui/webui/chrome_web_ui_configs.cc
+++ b/chrome/browser/ui/webui/chrome_web_ui_configs.cc
@@ -47,6 +47,7 @@
 #include "chrome/browser/ui/webui/signin_internals_ui.h"
 #include "chrome/browser/ui/webui/sync_internals/sync_internals_ui.h"
 #include "chrome/browser/ui/webui/translate_internals/translate_internals_ui.h"
+#include "chrome/browser/ui/webui/ungoogled_first_run.h"
 #include "chrome/browser/ui/webui/usb_internals/usb_internals_ui.h"
 #include "chrome/browser/ui/webui/user_actions/user_actions_ui.h"
 #include "chrome/browser/ui/webui/version/version_ui.h"
@@ -266,6 +267,7 @@ void RegisterChromeWebUIConfigs() {
   map.AddWebUIConfig(std::make_unique<SiteEngagementUIConfig>());
   map.AddWebUIConfig(std::make_unique<SyncInternalsUIConfig>());
   map.AddWebUIConfig(std::make_unique<TranslateInternalsUIConfig>());
+  map.AddWebUIConfig(std::make_unique<UngoogledFirstRunUIConfig>());
   map.AddWebUIConfig(std::make_unique<UsbInternalsUIConfig>());
   map.AddWebUIConfig(std::make_unique<UserActionsUIConfig>());
   map.AddWebUIConfig(std::make_unique<VersionUIConfig>());
--- /dev/null
+++ b/chrome/browser/ui/webui/ungoogled_first_run.h
@@ -0,0 +1,144 @@
+#ifndef CHROME_BROWSER_UI_WEBUI_UNGOOGLED_FIRST_RUN_H_
+#define CHROME_BROWSER_UI_WEBUI_UNGOOGLED_FIRST_RUN_H_
+
+#include "base/memory/ref_counted_memory.h"
+#include "chrome/browser/profiles/profile.h"
+#include "content/public/browser/url_data_source.h"
+#include "content/public/browser/web_ui.h"
+#include "content/public/browser/web_ui_controller.h"
+#include "content/public/browser/webui_config.h"
+#include "services/network/public/mojom/content_security_policy.mojom.h"
+
+class UFRDataSource : public content::URLDataSource {
+ public:
+  UFRDataSource() {}
+  UFRDataSource(const UFRDataSource&) = delete;
+  UFRDataSource& operator=(const UFRDataSource&) = delete;
+  std::string GetSource() { return "ungoogled-first-run"; }
+  std::string GetMimeType(const GURL& url) { return "text/html"; }
+  std::string GetContentSecurityPolicy(network::mojom::CSPDirectiveName directive) {
+    if (directive == network::mojom::CSPDirectiveName::ScriptSrc)
+      return "script-src 'unsafe-inline'";
+    return std::string();
+  }
+  void StartDataRequest(const GURL& url,
+                        const content::WebContents::Getter& wc_getter,
+                        GotDataCallback callback) {
+    std::string source = R"(
+<title>ungoogled-chromium first run page</title>
+<meta name="color-scheme" content="light dark">
+<style>
+ @import url(chrome://resources/css/text_defaults_md.css);
+ html{color:#202124; background:white; line-height:1.1em}
+ a{color:#1967d2}
+ h2{margin:0; padding:0.67em 1.33em}
+ p,details{border-top:.063em solid #f0f0f0; margin:0; padding:1em 2em}
+ ul,ol{padding-left:2em}
+ code{background:rgba(128 128 128 / .2); padding:0 0.5em; border-radius:0.25em}
+ summary{cursor:pointer}
+ section{width:60em; margin:4em auto; border-radius:.5em;
+         background:white; box-shadow:0 .063em .125em 0 #c4c5c6, 0 .125em .375em .125em #e2e3e3}
+ @media(prefers-color-scheme:dark){
+  html{color:#e8eaed; background:#202124}
+  a{color:#8ab4f8}
+  p,details{border-top:.063em solid #3f4042}
+  section{background:#292a2d; box-shadow:0 .063em .125em 0 #161719, 0 .125em .375em .125em #1b1c1f}
+ }
+</style>
+<base target="_blank">
+<section>
+ <h2>ungoogled-chromium</h2><p>
+ This browser was built with ungoogled-chromium patches and differs from the default Chromium experience
+ in a few ways. Look over the sections below and the
+ <a href="https://github.com/ungoogled-software/ungoogled-chromium/blob/master/docs/default_settings.md">
+  changes to the default settings</a> to see what may be important to you.<p>
+ This page can always be accessed again at <a href="chrome://ungoogled-first-run">chrome://ungoogled-first-run</a>
+</section>
+<section>
+ <h2>How-To</h2>
+ <details><summary><b>Install and update extensions</b></summary><br>
+  <a href="https://github.com/NeverDecaf">NeverDecaf</a> has created an extension to make this process easy:
+  <ol>
+   <li>Set <a href="chrome://flags/#extension-mime-request-handling">chrome://flags/#extension-mime-request-handling</a>
+    to <code>Always prompt for install</code> and relaunch.</li>
+   <li>Then click on the latest <code>Chromium.Web.Store.crx</code> link on
+    <a href="https://github.com/NeverDecaf/chromium-web-store/releases">the extension's Releases page</a>.</li>
+  </ol>
+  Please check out the <a href="https://github.com/NeverDecaf/chromium-web-store">chromium-web-store</a>
+  repo for further details and alternate installation methods for the extension.<br><br>
+  If you do not wish to install this extension, there is still a way to install other extensions albeit
+  without the ability to easily update them. In this case, please refer to the entry on the wiki for
+  <a href="https://ungoogled-software.github.io/ungoogled-chromium-wiki/faq#downloading-the-crx-file">
+   installing extensions manually</a>.</details>
+ <details><summary><b>Enable spellcheck</b></summary>
+  <ol>
+   <li>Go to <a href="https://chromium.googlesource.com/chromium/deps/hunspell_dictionaries/+/main">
+     https://chromium.googlesource.com/chromium/deps/hunspell_dictionaries/+/main</a></li>
+   <li>Find a bdic file for the language you want and click on it.
+    You will see a mostly empty page aside from "X-byte binary file".</li>
+   <li>On the bottom right corner, click "txt". The direct link for en-US-10-1.bdic is:
+    <a href="https://chromium.googlesource.com/chromium/deps/hunspell_dictionaries/+/main/en-US-10-1.bdic?format=TEXT">
+     https://chromium.googlesource.com/chromium/deps/hunspell_dictionaries/+/main/en-US-10-1.bdic?format=TEXT</a></li>
+   <li>This is a base64-encoded file that needs to be decoded. Use the button below to select the .txt file you saved
+    and save/move the resulting bdic file to your Dictionaries directory. Default locations:
+    <ul>
+     <li>Linux: <code>~/.config/chromium/Dictionaries/</code><br></li>
+     <li>Mac: <code>~/Library/Application Support/Chromium/Dictionaries/</code><br></li>
+     <li>Windows: <code>%LOCALAPPDATA%\Chromium\User Data\Dictionaries\</code><br></li>
+    </ul>
+    <input id="bdic" type="file" accept=".txt,text/plain" />
+   </li>
+   <li>Toggle spell check in <a href="chrome://settings/languages">chrome://settings/languages</a>,
+    or restart the browser for the dictionaries to take effect.</li>
+  </ol></details><p>
+  <a href="https://ungoogled-software.github.io/ungoogled-chromium-wiki/faq">Check out the FAQ on the wiki</a>
+  for information on other common topics.
+</section>
+<section>
+ <h2>Extra Features</h2><p>
+  Most features introduced by ungoogled-chromium are disabled by default and can be enabled in
+  <a href="chrome://flags">chrome://flags</a> or as command-line switches. Take a look at
+  <a href="https://github.com/ungoogled-software/ungoogled-chromium/blob/master/docs/flags.md">
+   the flags documentation</a> to see what features you may find useful to enable.
+</section>
+<section>
+ <h2>Additional Links</h2><p>
+  Privacy and security information, motivation and philosophy, rationale for changes, and more can be found in the
+  <a href="https://github.com/ungoogled-software/ungoogled-chromium/blob/master/README.md">README</a>.<p>
+  Visit our <a href="https://github.com/ungoogled-software/ungoogled-chromium/blob/master/SUPPORT.md">
+   support page</a> if you wish to report problems.<p>
+  Are you a developer? Consider
+  <a href="https://github.com/ungoogled-software/ungoogled-chromium/blob/master/docs/contributing.md">
+   contributing</a> to ungoogled-chromium!
+</section>
+<script>
+ document.getElementById("bdic").onchange = function(e){
+  var f = new FileReader;
+  f.onload = function(){
+   var a = document.createElement("a");
+   a.setAttribute("href", "data:application/octet-stream;base64, " + f.result);
+   a.setAttribute("download", e.target.files[0].name.replace(/\.[^/.]+$/, ".bdic"));
+   a.click()
+  }, f.readAsText(this.files[0])};
+</script>
+)";
+    std::move(callback).Run(base::MakeRefCounted<base::RefCountedString>(std::move(source)));
+  }
+};
+
+class UngoogledFirstRun;
+class UngoogledFirstRunUIConfig : public content::DefaultWebUIConfig<UngoogledFirstRun> {
+  public:
+   UngoogledFirstRunUIConfig() : DefaultWebUIConfig("chrome", "ungoogled-first-run") {}
+};
+
+class UngoogledFirstRun : public content::WebUIController {
+ public:
+  UngoogledFirstRun(content::WebUI* web_ui) : content::WebUIController(web_ui) {
+    content::URLDataSource::Add(Profile::FromWebUI(web_ui), std::make_unique<UFRDataSource>());
+  }
+  UngoogledFirstRun(const UngoogledFirstRun&) = delete;
+  UngoogledFirstRun& operator=(const UngoogledFirstRun&) = delete;
+};
+
+#endif  // CHROME_BROWSER_UI_WEBUI_UNGOOGLED_FIRST_RUN_H_
--- a/chrome/common/webui_url_constants.cc
+++ b/chrome/common/webui_url_constants.cc
@@ -71,6 +71,7 @@ bool IsSystemWebUIHost(std::string_view
 // These hosts will also be suggested by BuiltinProvider.
 base::span<const base::cstring_view> ChromeURLHosts() {
   static constexpr auto kChromeURLHosts = std::to_array<base::cstring_view>({
+      "ungoogled-first-run",
       kChromeUIAboutHost,
       kChromeUIAccessibilityHost,
       kChromeUIActorInternalsHost,
