# Add ability to always open dev context menu on reload button.

--- a/chrome/browser/ui/views/toolbar/reload_button.cc
+++ b/chrome/browser/ui/views/toolbar/reload_button.cc
@@ -114,7 +114,7 @@ bool ReloadButton::GetMenuEnabled() cons
 }
 
 void ReloadButton::SetMenuEnabled(bool enable) {
-  menu_enabled_ = enable;
+  menu_enabled_ = true;
   UpdateAccessibleHasPopup();
   UpdateCachedTooltipText();
 }
