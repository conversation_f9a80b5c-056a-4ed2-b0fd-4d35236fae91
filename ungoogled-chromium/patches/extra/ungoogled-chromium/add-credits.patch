--- a/components/webui/about/resources/about_credits_reciprocal.tmpl
+++ b/components/webui/about/resources/about_credits_reciprocal.tmpl
@@ -1,4 +1,4 @@
 <div class="open-sourced">
-    {{opensource_project}} software is made available as source code
-    <a href="{{opensource_link}}">here</a>.
+    {{opensource_project}} source code is available in
+    the <a href="{{opensource_link}}">repository</a>.
 </div>
--- /dev/null
+++ b/third_party/ungoogled-chromium/LICENSE
@@ -0,0 +1,29 @@
+BSD 3-Clause License
+
+Copyright (c) 2015-2025, The ungoogled-chromium Authors
+All rights reserved.
+
+Redistribution and use in source and binary forms, with or without
+modification, are permitted provided that the following conditions are met:
+
+1. Redistributions of source code must retain the above copyright notice, this
+   list of conditions and the following disclaimer.
+
+2. Redistributions in binary form must reproduce the above copyright notice,
+   this list of conditions and the following disclaimer in the documentation
+   and/or other materials provided with the distribution.
+
+3. Neither the name of the copyright holder nor the names of its
+   contributors may be used to endorse or promote products derived from
+   this software without specific prior written permission.
+
+THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
+AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
+IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
+DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
+FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
+DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
+SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
+CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
+OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
+OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
--- a/tools/licenses/licenses.py
+++ b/tools/licenses/licenses.py
@@ -1015,6 +1015,16 @@ def GenerateCredits(file_template_file,
   entries.append(
       MetadataToTemplateEntry(chromium_license_metadata, entry_template))
 
+  ugc_license_metadata = {
+      'Name': 'ungoogled-chromium',
+      'URL': 'https://github.com/ungoogled-software/ungoogled-chromium',
+      'Shipped': 'yes',
+      'License File': [os.path.join(_REPOSITORY_ROOT, 'third_party', 'ungoogled-chromium', 'LICENSE')],
+  }
+
+  entries.append(
+      MetadataToTemplateEntry(ugc_license_metadata, entry_template))
+
   entries_by_name = {}
   for path in third_party_dirs:
     try:
@@ -1059,8 +1069,8 @@ def GenerateCredits(file_template_file,
   reciprocal_template = codecs.open(reciprocal_template_file,
                                     encoding='utf-8').read()
   reciprocal_contents = EvaluateTemplate(reciprocal_template, {
-      'opensource_project': 'Chromium',
-      'opensource_link': 'https://source.chromium.org/chromium'
+      'opensource_project': 'ungoogled-chromium',
+      'opensource_link': 'https://github.com/ungoogled-software/ungoogled-chromium'
   },
                                          escape=False)
 
