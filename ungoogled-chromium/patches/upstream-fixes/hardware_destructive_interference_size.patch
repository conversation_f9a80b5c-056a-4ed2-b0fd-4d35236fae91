# hardware_destructive_interference_size was added in:
# https://chromium-review.googlesource.com/c/chromium/src/+/5767325
# then partially reverted in:
# https://chromium-review.googlesource.com/c/chromium/src/+/5841144
# This reverts the remaining change
--- a/components/media_router/common/providers/cast/channel/enum_table.h
+++ b/components/media_router/common/providers/cast/channel/enum_table.h
@@ -12,7 +12,6 @@
 
 #include <cstdint>
 #include <cstring>
-#include <new>
 #include <optional>
 #include <ostream>
 #include <string_view>
@@ -368,7 +367,8 @@ class EnumTable {
 
  private:
 #ifdef ARCH_CPU_64_BITS
-  alignas(std::hardware_destructive_interference_size)
+  // Align the data on a cache line boundary.
+  alignas(64)
 #endif
       std::initializer_list<Entry> data_;
   bool is_sorted_;
