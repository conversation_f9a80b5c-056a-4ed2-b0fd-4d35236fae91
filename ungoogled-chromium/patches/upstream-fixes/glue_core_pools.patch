# glue_core_pools was partially removed in:
# https://chromium-review.googlesource.com/c/chromium/src/+/5922801
# This removes the remaining references needed to build
--- a/base/allocator/partition_allocator/partition_alloc.gni
+++ b/base/allocator/partition_allocator/partition_alloc.gni
@@ -244,9 +244,6 @@ declare_args() {
   force_enable_raw_ptr_exclusion = false
 }
 
-assert(!enable_pointer_compression_support || glue_core_pools,
-       "Pointer compression relies on core pools being contiguous.")
-
 declare_args() {
   # We want to use RawPtrBackupRefImpl as the raw_ptr<> implementation
   # iff BRP support is enabled. However, for purpose of performance
--- a/base/allocator/partition_allocator/src/partition_alloc/compressed_pointer.h
+++ b/base/allocator/partition_allocator/src/partition_alloc/compressed_pointer.h
@@ -21,9 +21,6 @@
 
 #if PA_BUILDFLAG(ENABLE_POINTER_COMPRESSION)
 
-#if !PA_BUILDFLAG(GLUE_CORE_POOLS)
-#error "Pointer compression only works with glued pools"
-#endif
 #if PA_CONFIG(DYNAMICALLY_SELECT_POOL_SIZE)
 #error "Pointer compression currently supports constant pool size"
 #endif
