upstream-fixes/glue_core_pools.patch
upstream-fixes/hardware_destructive_interference_size.patch
upstream-fixes/missing-dependencies.patch

core/inox-patchset/0001-fix-building-without-safebrowsing.patch
core/inox-patchset/0003-disable-autofill-download-manager.patch
core/inox-patchset/0005-disable-default-extensions.patch
core/inox-patchset/0015-disable-update-pings.patch
core/inox-patchset/0021-disable-rlz.patch
core/iridium-browser/safe_browsing-disable-incident-reporting.patch
core/iridium-browser/safe_browsing-disable-reporting-of-safebrowsing-over.patch
core/iridium-browser/all-add-trk-prefixes-to-possibly-evil-connections.patch
core/ungoogled-chromium/disable-crash-reporter.patch
core/ungoogled-chromium/disable-google-host-detection.patch
core/ungoogled-chromium/replace-google-search-engine-with-nosearch.patch
core/ungoogled-chromium/toggle-translation-via-switch.patch
core/ungoogled-chromium/disable-untraceable-urls.patch
core/ungoogled-chromium/disable-profile-avatar-downloading.patch
core/ungoogled-chromium/disable-gcm.patch
core/ungoogled-chromium/disable-domain-reliability.patch
core/ungoogled-chromium/block-trk-and-subdomains.patch
core/ungoogled-chromium/disable-gaia.patch
core/ungoogled-chromium/disable-fonts-googleapis-references.patch
core/ungoogled-chromium/disable-webstore-urls.patch
core/ungoogled-chromium/fix-learn-doubleclick-hsts.patch
core/ungoogled-chromium/disable-webrtc-log-uploader.patch
core/ungoogled-chromium/fix-building-with-prunned-binaries.patch
core/ungoogled-chromium/disable-network-time-tracker.patch
core/ungoogled-chromium/disable-mei-preload.patch
core/ungoogled-chromium/fix-building-without-safebrowsing.patch
core/ungoogled-chromium/remove-unused-preferences-fields.patch
core/ungoogled-chromium/block-requests.patch
core/ungoogled-chromium/disable-privacy-sandbox.patch
core/ungoogled-chromium/doh-changes.patch
core/ungoogled-chromium/extensions-manifestv2.patch
core/ungoogled-chromium/remove-f1-shortcut.patch
core/bromite/disable-fetching-field-trials.patch

extra/ungoogled-chromium/add-ungoogled-flag-headers.patch
extra/inox-patchset/0006-modify-default-prefs.patch
extra/inox-patchset/0008-restore-classic-ntp.patch
extra/inox-patchset/0013-disable-missing-key-warning.patch
extra/inox-patchset/0016-chromium-sandbox-pie.patch
extra/inox-patchset/0019-disable-battery-status-service.patch
extra/debian/disable/google-api-warning.patch
extra/iridium-browser/mime_util-force-text-x-suse-ymp-to-be-downloaded.patch
extra/iridium-browser/prefs-always-prompt-for-download-directory-by-defaul.patch
extra/iridium-browser/updater-disable-auto-update.patch
extra/iridium-browser/Remove-EV-certificates.patch
extra/iridium-browser/browser-disable-profile-auto-import-on-first-run.patch
extra/bromite/fingerprinting-flags-client-rects-and-measuretext.patch
extra/bromite/flag-max-connections-per-host.patch
extra/bromite/flag-fingerprinting-canvas-image-data-noise.patch
extra/ungoogled-chromium/add-components-ungoogled.patch
extra/ungoogled-chromium/disable-formatting-in-omnibox.patch
extra/ungoogled-chromium/add-ipv6-probing-option.patch
extra/ungoogled-chromium/remove-disable-setuid-sandbox-as-bad-flag.patch
extra/ungoogled-chromium/disable-intranet-redirect-detector.patch
extra/ungoogled-chromium/enable-page-saving-on-more-pages.patch
extra/ungoogled-chromium/disable-download-quarantine.patch
extra/ungoogled-chromium/fix-building-without-mdns-and-service-discovery.patch
extra/ungoogled-chromium/add-flag-to-configure-extension-downloading.patch
extra/ungoogled-chromium/add-flag-for-search-engine-collection.patch
extra/ungoogled-chromium/add-flag-to-disable-beforeunload.patch
extra/ungoogled-chromium/add-flag-to-force-punycode-hostnames.patch
extra/ungoogled-chromium/add-flag-to-show-avatar-button.patch
extra/ungoogled-chromium/add-suggestions-url-field.patch
extra/ungoogled-chromium/add-flag-to-hide-crashed-bubble.patch
extra/ungoogled-chromium/add-flag-to-scroll-tabs.patch
extra/ungoogled-chromium/enable-paste-and-go-new-tab-button.patch
extra/ungoogled-chromium/add-flag-for-bookmark-bar-ntp.patch
extra/ungoogled-chromium/enable-menu-on-reload-button.patch
extra/ungoogled-chromium/add-flag-for-omnibox-autocomplete-filtering.patch
extra/ungoogled-chromium/disable-dial-repeating-discovery.patch
extra/ungoogled-chromium/remove-uneeded-ui.patch
extra/ungoogled-chromium/add-flag-to-close-window-with-last-tab.patch
extra/ungoogled-chromium/add-flag-to-convert-popups-to-tabs.patch
extra/ungoogled-chromium/add-flag-to-disable-local-history-expiration.patch
extra/ungoogled-chromium/add-extra-channel-info.patch
extra/ungoogled-chromium/prepopulated-search-engines.patch
extra/ungoogled-chromium/fix-distilled-icons.patch
extra/ungoogled-chromium/add-flag-to-clear-data-on-exit.patch
extra/ungoogled-chromium/add-flag-for-tabsearch-button.patch
extra/ungoogled-chromium/add-flag-for-qr-generator.patch
extra/ungoogled-chromium/add-flag-for-grab-handle.patch
extra/ungoogled-chromium/add-flag-for-close-confirmation.patch
extra/ungoogled-chromium/keep-expired-flags.patch
extra/ungoogled-chromium/add-flag-for-custom-ntp.patch
extra/ungoogled-chromium/add-flag-for-tab-hover-cards.patch
extra/ungoogled-chromium/add-flag-to-hide-tab-close-buttons.patch
extra/ungoogled-chromium/add-flag-to-disable-tls-grease.patch
extra/ungoogled-chromium/add-flag-to-change-http-accept-header.patch
extra/ungoogled-chromium/add-flag-to-disable-sharing-hub.patch
extra/ungoogled-chromium/add-flag-for-disabling-link-drag.patch
extra/ungoogled-chromium/add-flag-to-hide-extensions-menu.patch
extra/ungoogled-chromium/add-flag-to-hide-fullscreen-exit-ui.patch
extra/ungoogled-chromium/add-flag-for-incognito-themes.patch
extra/ungoogled-chromium/add-flags-for-referrer-customization.patch
extra/ungoogled-chromium/default-webrtc-ip-handling-policy.patch
extra/ungoogled-chromium/add-flags-for-existing-switches.patch
extra/ungoogled-chromium/first-run-page.patch
extra/ungoogled-chromium/add-flag-to-reduce-system-info.patch
extra/ungoogled-chromium/add-flag-to-remove-client-hints.patch
extra/ungoogled-chromium/disable-downloads-page-referrer-url.patch
extra/ungoogled-chromium/enable-extra-locales.patch
extra/ungoogled-chromium/disable-chromelabs.patch
extra/ungoogled-chromium/remove-pac-size-limit.patch
extra/ungoogled-chromium/enable-certificate-transparency-and-add-flag.patch
extra/ungoogled-chromium/add-flag-to-spoof-webgl-renderer-info.patch
extra/ungoogled-chromium/add-flag-to-increase-incognito-storage-quota.patch
extra/ungoogled-chromium/add-credits.patch
extra/ungoogled-chromium/disable-ai-search-shortcuts.patch
