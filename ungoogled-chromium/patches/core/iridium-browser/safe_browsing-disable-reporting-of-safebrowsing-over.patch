From c89ce946e5328ca8a7df923d421e904bb6bfe9b6 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 7 Jul 2015 18:28:46 +0200
Subject: [PATCH 63/76] safe_browsing: disable reporting of safebrowsing
 override

Disables reporting of the safebrowsing override, i.e. the report sent
if a user decides to visit a page that was flagged as "insecure".
This prevents trk:148 (phishing) and trk:149 (malware).
---
 components/safe_browsing/content/browser/client_side_detection_service.cc   | 5 +++++
 1 file changed, 5 insertions(+)

--- a/components/safe_browsing/content/browser/client_side_detection_service.cc
+++ b/components/safe_browsing/content/browser/client_side_detection_service.cc
@@ -281,6 +281,10 @@ void ClientSideDetectionService::StartCl
     return;
   }
 
+#if 1
+  if (!callback.is_null())
+    callback.Run(GURL(request->url()), false);
+#else
   std::string request_data;
   request->SerializeToString(&request_data);
 
@@ -362,6 +366,7 @@ void ClientSideDetectionService::StartCl
       base::BindOnce(&WebUIInfoSingleton::AddToClientPhishingRequestsSent,
                      base::Unretained(WebUIInfoSingleton::GetInstance()),
                      std::move(request), access_token));
+#endif
 }
 
 void ClientSideDetectionService::HandlePhishingVerdict(
