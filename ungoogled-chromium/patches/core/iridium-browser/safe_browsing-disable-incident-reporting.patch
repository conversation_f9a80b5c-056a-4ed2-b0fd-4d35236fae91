From 4dfa8ed0814040317cb82d8545502186daa0a204 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 7 Jul 2015 17:02:09 +0200
Subject: [PATCH 62/76] safe_browsing: disable incident reporting

Disables the safebrowsing incident reporting where you could upload
information about a blocked URL to Google (also added a trk prefix to
the URL so we get notified if this happens again in the future).
---
 .../incident_reporting/incident_report_uploader_impl.cc        | 2 +-
 .../incident_reporting/incident_reporting_service.cc           | 3 +++
 chrome/browser/safe_browsing/safe_browsing_blocking_page.cc    | 3 +--
 chrome/browser/safe_browsing/safe_browsing_service.cc          | 2 +-
 .../security_interstitials/core/safe_browsing_loud_error_ui.cc | 1 +
 5 files changed, 7 insertions(+), 4 deletions(-)

--- a/chrome/browser/safe_browsing/chrome_safe_browsing_blocking_page_factory.cc
+++ b/chrome/browser/safe_browsing/chrome_safe_browsing_blocking_page_factory.cc
@@ -63,8 +63,7 @@ ChromeSafeBrowsingBlockingPageFactory::C
       Profile::FromBrowserContext(web_contents->GetBrowserContext());
   // Create appropriate display options for this blocking page.
   PrefService* prefs = profile->GetPrefs();
-  bool is_extended_reporting_opt_in_allowed =
-      IsExtendedReportingOptInAllowed(*prefs);
+  bool is_extended_reporting_opt_in_allowed = false;
   bool is_proceed_anyway_disabled = IsSafeBrowsingProceedAnywayDisabled(*prefs);
 
   // Determine if any prefs need to be updated prior to showing the security
--- a/chrome/browser/safe_browsing/incident_reporting/incident_report_uploader_impl.cc
+++ b/chrome/browser/safe_browsing/incident_reporting/incident_report_uploader_impl.cc
@@ -27,7 +27,7 @@ namespace safe_browsing {
 namespace {
 
 const char kSbIncidentReportUrl[] =
-    "https://sb-ssl.google.com/safebrowsing/clientreport/incident";
+    "trk:268:https://sb-ssl.google.com/safebrowsing/clientreport/incident";
 
 constexpr net::NetworkTrafficAnnotationTag
     kSafeBrowsingIncidentTrafficAnnotation =
--- a/chrome/browser/safe_browsing/incident_reporting/incident_reporting_service.cc
+++ b/chrome/browser/safe_browsing/incident_reporting/incident_reporting_service.cc
@@ -303,11 +303,14 @@ IncidentReportingService::UploadContext:
 
 // static
 bool IncidentReportingService::IsEnabledForProfile(Profile* profile) {
+  return false;
+#if 0
   if (profile->IsOffTheRecord())
     return false;
   if (!IsSafeBrowsingEnabled(*profile->GetPrefs()))
     return false;
   return IsExtendedReportingEnabled(*profile->GetPrefs());
+#endif
 }
 
 IncidentReportingService::IncidentReportingService(
--- a/chrome/browser/safe_browsing/safe_browsing_service.cc
+++ b/chrome/browser/safe_browsing/safe_browsing_service.cc
@@ -378,7 +378,7 @@ SafeBrowsingUIManager* SafeBrowsingServi
 }
 
 void SafeBrowsingServiceImpl::RegisterAllDelayedAnalysis() {
-#if BUILDFLAG(FULL_SAFE_BROWSING)
+#if 0
   RegisterBinaryIntegrityAnalysis();
 #endif
 }
--- a/components/security_interstitials/core/safe_browsing_loud_error_ui.cc
+++ b/components/security_interstitials/core/safe_browsing_loud_error_ui.cc
@@ -27,11 +27,11 @@ namespace {
 // For malware interstitial pages, we link the problematic URL to Google's
 // diagnostic page.
 const char kSbDiagnosticUrl[] =
-    "https://transparencyreport.google.com/safe-browsing/search?url=%s";
+    "trk:227:https://transparencyreport.google.com/safe-browsing/search?url=%s";
 
 // Constants for the V4 phishing string upgrades.
 const char kReportPhishingErrorUrl[] =
-    "https://safebrowsing.google.com/safebrowsing/report_error/?url=%s";
+    "trk:228:https://safebrowsing.google.com/safebrowsing/report_error/?url=%s";
 
 void RecordExtendedReportingPrefChanged(bool report) {
   UMA_HISTOGRAM_BOOLEAN("SafeBrowsing.Pref.Extended.SecurityInterstitial",
