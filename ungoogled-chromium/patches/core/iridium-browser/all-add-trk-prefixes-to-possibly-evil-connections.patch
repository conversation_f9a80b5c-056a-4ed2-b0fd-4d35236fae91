From 06f6141610cb2aa562c94dbb9f1f4355e4b34c5d Mon Sep 17 00:00:00 2001
From: <PERSON> <jeng<PERSON><EMAIL>>
Date: Mon, 30 Sep 2019 09:37:51 +0200
Subject: [PATCH 75/76] all: add trk: prefixes to possibly evil connections

Prefix URLs to Google services with trk: so that whenever something
tries to load them, the developer will be informed via printf and
dialog (extra info bar between URLbar and content window) about this.

If you see such dialog, we know that (a) either the URL needs to be
whitelisted, or (b) the feature that triggered it needs to be disabled
by default.
---
 build/mac/tweak_info_plist.py                 |  2 +-
 .../customization/customization_document.cc   |  2 +-
 .../file_manager/private_api_drive.cc         |  2 +-
 .../file_manager/private_api_misc.cc          |  2 +-
 .../remote_commands/crd_host_delegate.cc      |  6 +++---
 .../cryptotoken_private_api.cc                |  4 ++--
 chrome/browser/extensions/install_signer.cc   |  2 +-
 .../media/webrtc/webrtc_event_log_uploader.cc |  2 +-
 .../media/webrtc/webrtc_log_uploader.cc       |  2 +-
 .../nacl_host/nacl_infobar_delegate.cc        |  2 +-
 .../profiles/profile_avatar_downloader.cc     |  2 +-
 .../default_apps/external_extensions.json     |  6 +++---
 .../client_side_detection_service.cc          |  2 +-
 .../download_protection/download_feedback.cc  |  2 +-
 .../spellcheck_hunspell_dictionary.cc         |  2 +-
 .../supervised_user_service.cc                |  2 +-
 .../browser/tracing/crash_service_uploader.cc |  2 +-
 .../ui/views/outdated_upgrade_bubble_view.cc  |  2 +-
 .../ui/webui/ntp/ntp_resource_cache.cc        |  8 ++++----
 .../components/recovery_component.cc          |  2 +-
 .../crash/crashpad_crash_reporter.cc          |  2 +-
 .../extensions/chrome_extensions_client.cc    |  4 ++--
 .../setup/google_chrome_behaviors.cc          |  2 +-
 .../browser/service/cast_service_simple.cc    |  2 +-
 chromecast/crash/linux/minidump_uploader.cc   |  2 +-
 .../simple_geolocation_provider.cc            |  2 +-
 .../common/cloud_devices_urls.cc              |  8 ++++----
 components/drive/service/drive_api_service.cc |  4 ++--
 components/feedback/feedback_uploader.cc      |  2 +-
 components/gcm_driver/gcm_account_tracker.cc  |  4 ++--
 components/google/core/common/google_util.cc  |  2 +-
 .../core/browser/web_history_service.cc       |  6 +++---
 components/metrics/url_constants.cc           |  2 +-
 .../core/browser/password_store.cc            |  8 ++++----
 .../safe_search_url_checker_client.cc         |  2 +-
 .../safe_search_api/stub_url_checker.cc       |  2 +-
 .../core/browser/translate_url_fetcher.cc     |  1 +
 .../translate/core/common/translate_util.cc   |  2 +-
 .../variations/variations_url_constants.cc    |  2 +-
 .../speech/speech_recognition_engine.cc       |  2 +-
 .../browser/webauth/authenticator_common.cc   |  4 ++--
 .../shell/browser/shell_browser_main_parts.cc |  2 +-
 google_apis/gaia/gaia_constants.cc            | 20 +++++++++----------
 google_apis/gaia/gaia_urls.cc                 |  1 +
 google_apis/gcm/engine/gservices_settings.cc  |  6 +++---
 .../notifier/base/gaia_token_pre_xmpp_auth.cc |  2 +-
 remoting/base/breakpad_mac.mm                 |  2 +-
 remoting/protocol/jingle_messages.cc          |  2 +-
 rlz/lib/lib_values.cc                         |  2 +-
 third_party/libjingle_xmpp/xmpp/constants.cc  |  6 +++---
 .../chromevox/background/prefs.js             |  4 ++--
 .../chromevoxclassic/host/chrome/host.js      |  4 ++--
 ui/views/examples/webview_example.cc          |  2 +-
 54 files changed, 89 insertions(+), 87 deletions(-)

--- a/build/apple/tweak_info_plist.py
+++ b/build/apple/tweak_info_plist.py
@@ -212,7 +212,7 @@ def _AddKeystoneKeys(plist, bundle_ident
   also requires the |bundle_identifier| argument (com.example.product)."""
   plist['KSVersion'] = plist['CFBundleShortVersionString']
   plist['KSProductID'] = bundle_identifier
-  plist['KSUpdateURL'] = 'https://tools.google.com/service/update2'
+  plist['KSUpdateURL'] = 'trk:132:https://tools.google.com/service/update2'
 
   _RemoveKeys(plist, 'KSChannelID')
   if base_tag != '':
--- a/chrome/browser/ash/customization/customization_document.h
+++ b/chrome/browser/ash/customization/customization_document.h
@@ -155,7 +155,7 @@ class ServicesCustomizationDocument : pu
 
   // Template URL where to fetch OEM services customization manifest from.
   static constexpr char kManifestUrl[] =
-      "https://ssl.gstatic.com/chrome/chromeos-customization/%s.json";
+      "trk:151:https://ssl.gstatic.com/chrome/chromeos-customization/%s.json";
 
   // Return true if the customization was applied. Customization is applied only
   // once per machine.
--- a/chrome/browser/extensions/install_signer.cc
+++ b/chrome/browser/extensions/install_signer.cc
@@ -63,7 +63,7 @@ const int kSignatureFormatVersion = 2;
 const size_t kSaltBytes = 32;
 
 const char kBackendUrl[] =
-    "https://www.googleapis.com/chromewebstore/v1.1/items/verify";
+    "trk:222:https://www.googleapis.com/chromewebstore/v1.1/items/verify";
 
 const char kPublicKeyPEM[] =
     "-----BEGIN PUBLIC KEY-----"
--- a/chrome/browser/media/webrtc/webrtc_event_log_uploader.cc
+++ b/chrome/browser/media/webrtc/webrtc_event_log_uploader.cc
@@ -110,7 +110,7 @@ void OnURLLoadUploadProgress(uint64_t cu
 }  // namespace
 
 const char WebRtcEventLogUploaderImpl::kUploadURL[] =
-    "https://clients2.google.com/cr/report";
+    "trk:300:https://clients2.google.com/cr/report";
 
 WebRtcEventLogUploaderImpl::Factory::Factory(
     scoped_refptr<base::SequencedTaskRunner> task_runner)
--- a/chrome/browser/media/webrtc/webrtc_log_uploader.cc
+++ b/chrome/browser/media/webrtc/webrtc_log_uploader.cc
@@ -531,7 +531,7 @@ void WebRtcLogUploader::UploadCompressed
           }
         })");
 
-  constexpr char kUploadURL[] = "https://clients2.google.com/cr/report";
+  constexpr char kUploadURL[] = "trk:301:https://clients2.google.com/cr/report";
   auto resource_request = std::make_unique<network::ResourceRequest>();
   resource_request->url = !upload_url_for_testing_.is_empty()
                               ? upload_url_for_testing_
--- a/chrome/browser/profiles/profile_avatar_downloader.cc
+++ b/chrome/browser/profiles/profile_avatar_downloader.cc
@@ -20,7 +20,7 @@
 
 namespace {
 const char kHighResAvatarDownloadUrlPrefix[] =
-    "https://www.gstatic.com/chrome/profile_avatars/";
+    "trk:271:https://www.gstatic.com/chrome/profile_avatars/";
 }
 
 ProfileAvatarDownloader::ProfileAvatarDownloader(size_t icon_index,
--- a/chrome/browser/resources/default_apps/external_extensions.json
+++ b/chrome/browser/resources/default_apps/external_extensions.json
@@ -3,7 +3,7 @@
 {
   // Drive extension
   "ghbmnnjooekpmoecnnnilnnbdlolhkhi" : {
-    "external_update_url": "https://clients2.google.com/service/update2/crx"
+    "external_update_url": "trk:04:https://clients2.google.com/service/update2/crx"
   }
 }
 
--- a/chrome/browser/safe_browsing/download_protection/download_feedback.cc
+++ b/chrome/browser/safe_browsing/download_protection/download_feedback.cc
@@ -178,7 +178,7 @@ const int64_t DownloadFeedback::kMaxUplo
 
 // static
 const char DownloadFeedback::kSbFeedbackURL[] =
-    "https://safebrowsing.google.com/safebrowsing/uploads/chrome";
+    "trk:164:https://safebrowsing.google.com/safebrowsing/uploads/chrome";
 
 // static
 DownloadFeedbackFactory* DownloadFeedback::factory_ = nullptr;
--- a/chrome/browser/spellchecker/spellcheck_hunspell_dictionary.cc
+++ b/chrome/browser/spellchecker/spellcheck_hunspell_dictionary.cc
@@ -277,7 +277,7 @@ GURL SpellcheckHunspellDictionary::GetDi
   DCHECK(!bdict_file.empty());
 
   static const char kDownloadServerUrl[] =
-      "https://redirector.gvt1.com/edgedl/chrome/dict/";
+      "trk:173:https://redirector.gvt1.com/edgedl/chrome/dict/";
 
   return GURL(std::string(kDownloadServerUrl) +
               base::ToLowerASCII(bdict_file));
--- a/chrome/browser/ui/dialogs/outdated_upgrade_bubble.cc
+++ b/chrome/browser/ui/dialogs/outdated_upgrade_bubble.cc
@@ -32,7 +32,7 @@
 namespace {
 
 // The URL to be used to re-install Chrome when auto-update failed for too long.
-const char* kUpdateBrowserRedirectUrl = "https://www.google.com/chrome";
+const char* kUpdateBrowserRedirectUrl = "trk:242:https://www.google.com/chrome";
 
 bool g_upgrade_bubble_is_showing = false;
 
--- a/chrome/browser/ui/webui/ntp/ntp_resource_cache.cc
+++ b/chrome/browser/ui/webui/ntp/ntp_resource_cache.cc
@@ -76,17 +76,17 @@ namespace {
 // The URL for the the Learn More page shown on incognito new tab.
 const char kLearnMoreIncognitoUrl[] =
 #if BUILDFLAG(IS_CHROMEOS)
-    "https://support.google.com/chromebook/?p=incognito";
+    "trk:246:https://support.google.com/chromebook/?p=incognito";
 #else
-    "https://support.google.com/chrome/?p=incognito";
+    "trk:247:https://support.google.com/chrome/?p=incognito";
 #endif
 
 // The URL for the Learn More page shown on guest session new tab.
 const char kLearnMoreGuestSessionUrl[] =
 #if BUILDFLAG(IS_CHROMEOS)
-    "https://support.google.com/chromebook/?p=chromebook_guest";
+    "trk:248:https://support.google.com/chromebook/?p=chromebook_guest";
 #else
-    "https://support.google.com/chrome/?p=ui_guest";
+    "trk:261:https://support.google.com/chrome/?p=ui_guest";
 #endif
 
 std::string ReplaceTemplateExpressions(
--- a/chrome/common/extensions/chrome_extensions_client.cc
+++ b/chrome/common/extensions/chrome_extensions_client.cc
@@ -45,9 +45,9 @@ namespace {
 
 // TODO(battre): Delete the HTTP URL once the blocklist is downloaded via HTTPS.
 const char kExtensionBlocklistUrlPrefix[] =
-    "http://www.gstatic.com/chrome/extensions/blocklist";
+    "trk:269:http://www.gstatic.com/chrome/extensions/blocklist";
 const char kExtensionBlocklistHttpsUrlPrefix[] =
-    "https://www.gstatic.com/chrome/extensions/blocklist";
+    "trk:270:https://www.gstatic.com/chrome/extensions/blocklist";
 
 }  // namespace
 
--- a/chrome/installer/setup/google_chrome_behaviors.cc
+++ b/chrome/installer/setup/google_chrome_behaviors.cc
@@ -37,7 +37,7 @@ namespace installer {
 namespace {
 
 constexpr std::wstring_view kUninstallSurveyUrl(
-    L"https://support.google.com/chrome?p=chrome_uninstall_survey");
+    L"trk:253:https://support.google.com/chrome?p=chrome_uninstall_survey");
 
 // Launches the url directly with the user's default handler for |url|.
 bool NavigateToUrlWithHttps(const std::wstring& url) {
--- a/chromeos/ash/components/geolocation/simple_geolocation_provider.h
+++ b/chromeos/ash/components/geolocation/simple_geolocation_provider.h
@@ -101,7 +101,7 @@ class COMPONENT_EXPORT(CHROMEOS_ASH_COMP
 
  private:
   static constexpr char kGeolocationProviderUrl[] =
-      "https://www.googleapis.com/geolocation/v1/geolocate?";
+      "trk:215:https://www.googleapis.com/geolocation/v1/geolocate?";
 
   // This class is a singleton.
   explicit SimpleGeolocationProvider(
--- a/components/drive/service/drive_api_service.cc
+++ b/components/drive/service/drive_api_service.cc
@@ -79,9 +79,9 @@ namespace drive {
 namespace {
 
 // OAuth2 scopes for Drive API.
-const char kDriveScope[] = "https://www.googleapis.com/auth/drive";
+const char kDriveScope[] = "trk:217:https://www.googleapis.com/auth/drive";
 const char kDriveAppsReadonlyScope[] =
-    "https://www.googleapis.com/auth/drive.apps.readonly";
+    "trk:218:https://www.googleapis.com/auth/drive.apps.readonly";
 const char kDriveAppsScope[] = "https://www.googleapis.com/auth/drive.apps";
 
 // Mime type to create a directory.
--- a/components/feedback/feedback_uploader.cc
+++ b/components/feedback/feedback_uploader.cc
@@ -39,7 +39,7 @@ constexpr base::FilePath::CharType kFeed
     FILE_PATH_LITERAL("Feedback Reports");
 
 constexpr char kFeedbackPostUrl[] =
-    "https://www.google.com/tools/feedback/chrome/__submit";
+    "trk:232:https://www.google.com/tools/feedback/chrome/__submit";
 
 constexpr char kProtoBufMimeType[] = "application/x-protobuf";
 
--- a/components/google/core/common/google_util.cc
+++ b/components/google/core/common/google_util.cc
@@ -152,7 +152,7 @@ BASE_FEATURE(kIsViewerGoogleSearchUrl,
 
 // Global functions -----------------------------------------------------------
 
-const char kGoogleHomepageURL[] = "https://www.google.com/";
+const char kGoogleHomepageURL[] = "trk:113:https://www.google.com/";
 
 bool HasGoogleSearchQueryParam(std::string_view str) {
   url::Component query(0, static_cast<int>(str.length())), key, value;
--- a/components/history/core/browser/web_history_service.cc
+++ b/components/history/core/browser/web_history_service.cc
@@ -42,13 +42,13 @@ namespace history {
 
 namespace {
 
-const char kHistoryOAuthScope[] = "https://www.googleapis.com/auth/chromesync";
+const char kHistoryOAuthScope[] = "trk:138:https://www.googleapis.com/auth/chromesync";
 
 const char kHistoryQueryHistoryUrl[] =
-    "https://history.google.com/history/api/lookup?client=chrome";
+    "trk:139:https://history.google.com/history/api/lookup?client=chrome";
 
 const char kHistoryDeleteHistoryUrl[] =
-    "https://history.google.com/history/api/delete?client=chrome";
+    "trk:140:https://history.google.com/history/api/delete?client=chrome";
 
 const char kHistoryAudioHistoryUrl[] =
     "https://history.google.com/history/api/lookup?client=audio";
--- a/components/safe_browsing/content/browser/client_side_detection_service.cc
+++ b/components/safe_browsing/content/browser/client_side_detection_service.cc
@@ -68,7 +68,7 @@ const int ClientSideDetectionService::kN
 const int ClientSideDetectionService::kPositiveCacheIntervalMinutes = 30;
 
 const char ClientSideDetectionService::kClientReportPhishingUrl[] =
-    "https://sb-ssl.google.com/safebrowsing/clientreport/phishing";
+    "trk:148:https://sb-ssl.google.com/safebrowsing/clientreport/phishing";
 
 struct ClientSideDetectionService::ClientPhishingReportInfo {
   std::unique_ptr<network::SimpleURLLoader> loader;
--- a/components/safe_search_api/safe_search/safe_search_url_checker_client.cc
+++ b/components/safe_search_api/safe_search/safe_search_url_checker_client.cc
@@ -27,7 +27,7 @@ namespace safe_search_api {
 namespace {
 
 const char kSafeSearchApiUrl[] =
-    "https://safesearch.googleapis.com/v1:classify";
+    "trk:238:https://safesearch.googleapis.com/v1:classify";
 const char kDataContentType[] = "application/x-www-form-urlencoded";
 const char kDataFormat[] = "key=%s&urls=%s";
 
--- a/components/safe_search_api/stub_url_checker.cc
+++ b/components/safe_search_api/stub_url_checker.cc
@@ -21,7 +21,7 @@ namespace safe_search_api {
 namespace {
 
 constexpr char kSafeSearchApiUrl[] =
-    "https://safesearch.googleapis.com/v1:classify";
+    "trk:238:https://safesearch.googleapis.com/v1:classify";
 
 std::string BuildResponse(bool is_porn) {
   base::Value::Dict dict;
--- a/components/translate/core/browser/translate_url_fetcher.cc
+++ b/components/translate/core/browser/translate_url_fetcher.cc
@@ -98,6 +98,7 @@ bool TranslateURLFetcher::Request(const
   if (!extra_request_header_.empty())
     resource_request->headers.AddHeaderFromString(extra_request_header_);
 
+  fprintf(stderr, "translator: fetching something from %s\n", url_.spec().c_str());
   simple_loader_ =
       variations::CreateSimpleURLLoaderWithVariationsHeaderUnknownSignedIn(
           std::move(resource_request),
--- a/components/translate/core/common/translate_util.cc
+++ b/components/translate/core/common/translate_util.cc
@@ -13,7 +13,7 @@
 
 namespace translate {
 
-const char kSecurityOrigin[] = "https://translate.googleapis.com/";
+const char kSecurityOrigin[] = "trk:220:https://translate.googleapis.com/";
 
 // The feature is explicitly disabled on Webview and Weblayer.
 // TODO(crbug.com/40819484): Enable the feature on Webview.
--- a/components/variations/variations_url_constants.cc
+++ b/components/variations/variations_url_constants.cc
@@ -8,7 +8,7 @@ namespace variations {
 
 // Default server of Variations seed info.
 const char kDefaultServerUrl[] =
-    "https://clientservices.googleapis.com/chrome-variations/seed";
+    "trk:142:https://clientservices.googleapis.com/chrome-variations/seed";
 
 const char kDefaultInsecureServerUrl[] =
     "http://clientservices.googleapis.com/chrome-variations/seed";
--- a/content/browser/speech/network_speech_recognition_engine_impl.cc
+++ b/content/browser/speech/network_speech_recognition_engine_impl.cc
@@ -35,7 +35,7 @@ namespace content {
 namespace {
 
 const char kWebServiceBaseUrl[] =
-    "https://www.google.com/speech-api/full-duplex/v1";
+    "trk:184:https://www.google.com/speech-api/full-duplex/v1";
 const char kDownstreamUrl[] = "/down?";
 const char kUpstreamUrl[] = "/up?";
 
--- a/content/browser/webauth/webauth_request_security_checker.h
+++ b/content/browser/webauth/webauth_request_security_checker.h
@@ -90,9 +90,9 @@ class CONTENT_EXPORT WebAuthRequestSecur
   // Legacy App IDs, which google.com origins are allowed to assert for
   // compatibility reasons.
   static constexpr char kGstaticAppId[] =
-      "https://www.gstatic.com/securitykey/origins.json";
+      "trk:276:https://www.gstatic.com/securitykey/origins.json";
   static constexpr char kGstaticCorpAppId[] =
-      "https://www.gstatic.com/securitykey/a/google.com/origins.json";
+      "trk:277:https://www.gstatic.com/securitykey/a/google.com/origins.json";
 
   explicit WebAuthRequestSecurityChecker(RenderFrameHost* host);
   WebAuthRequestSecurityChecker(const WebAuthRequestSecurityChecker&) = delete;
--- a/content/shell/browser/shell_browser_main_parts.cc
+++ b/content/shell/browser/shell_browser_main_parts.cc
@@ -85,7 +85,7 @@ GURL GetStartupURL() {
 #else
   const base::CommandLine::StringVector& args = command_line->GetArgs();
   if (args.empty())
-    return GURL("https://www.google.com/");
+    return GURL("trk:183:https://www.google.com/");
 
 #if BUILDFLAG(IS_WIN)
   GURL url(base::WideToUTF16(args[0]));
--- a/extensions/common/extension_urls.cc
+++ b/extensions/common/extension_urls.cc
@@ -38,13 +38,13 @@ const GURL* g_item_snippet_url_for_test_
 
 }  // namespace
 
-const char kChromeWebstoreBaseURL[] = "https://chrome.google.com/webstore";
-const char kNewChromeWebstoreBaseURL[] = "https://chromewebstore.google.com/";
+const char kChromeWebstoreBaseURL[] = "trk:09:https://chrome.google.com/webstore";
+const char kNewChromeWebstoreBaseURL[] = "trk:08:https://chromewebstore.google.com/";
 const char kExtensionsDocsWhatsNewURL[] =
-    "https://developer.chrome.com/docs/extensions/whats-new";
+    "trk:06:https://developer.chrome.com/docs/extensions/whats-new";
 const char kChromeWebstoreUpdateURL[] =
-    "https://clients2.google.com/service/update2/crx";
-const char kChromeWebstoreApiURL[] = "https://chromewebstore.googleapis.com/";
+    "trk:05:https://clients2.google.com/service/update2/crx";
+const char kChromeWebstoreApiURL[] = "trk:07:https://chromewebstore.googleapis.com/";
 
 const char kAppMenuUtmSource[] = "ext_app_menu";
 const char kExtensionsMenuUtmSource[] = "ext_extensions_menu";
--- a/google_apis/gaia/gaia_constants.cc
+++ b/google_apis/gaia/gaia_constants.cc
@@ -14,173 +14,173 @@ const char kChromeSource[] = "ChromiumBr
 const char kUnexpectedServiceResponse[] = "UnexpectedServiceResponse";
 
 // OAuth scopes.
-const char kOAuth1LoginScope[] = "https://www.google.com/accounts/OAuthLogin";
+const char kOAuth1LoginScope[] = "trk:069:https://www.google.com/accounts/OAuthLogin";
 
 // Service/scope names for device management (cloud-based policy) server.
 const char kDeviceManagementServiceOAuth[] =
-    "https://www.googleapis.com/auth/chromeosdevicemanagement";
+    "trk:070:https://www.googleapis.com/auth/chromeosdevicemanagement";
 
 // OAuth2 scope for access to all Google APIs.
-const char kAnyApiOAuth2Scope[] = "https://www.googleapis.com/auth/any-api";
+const char kAnyApiOAuth2Scope[] = "trk:071:https://www.googleapis.com/auth/any-api";
 
 // OAuth2 scope for access to Chrome sync APIs
 const char kChromeSyncOAuth2Scope[] =
-    "https://www.googleapis.com/auth/chromesync";
+    "trk:072:https://www.googleapis.com/auth/chromesync";
 // OAuth2 scope for access to the Chrome Sync APIs for managed profiles.
 const char kChromeSyncSupervisedOAuth2Scope[] =
-    "https://www.googleapis.com/auth/chromesync_playpen";
+    "trk:073:https://www.googleapis.com/auth/chromesync_playpen";
 
 // OAuth2 scope for parental consent logging for secondary account addition.
 const char kKidManagementPrivilegedOAuth2Scope[] =
-    "https://www.googleapis.com/auth/kid.management.privileged";
+    "trk:075:https://www.googleapis.com/auth/kid.management.privileged";
 
 // OAuth2 scope for access to Google Family Link Supervision Setup.
 const char kKidsSupervisionSetupChildOAuth2Scope[] =
-    "https://www.googleapis.com/auth/kids.supervision.setup.child";
+    "trk:076:https://www.googleapis.com/auth/kids.supervision.setup.child";
 
 // OAuth2 scope for access to Google Talk APIs (XMPP).
 const char kGoogleTalkOAuth2Scope[] =
-    "https://www.googleapis.com/auth/googletalk";
+    "trk:077:https://www.googleapis.com/auth/googletalk";
 
 // OAuth2 scope for access to Google account information.
 const char kGoogleUserInfoEmail[] =
-    "https://www.googleapis.com/auth/userinfo.email";
+    "trk:078:https://www.googleapis.com/auth/userinfo.email";
 const char kGoogleUserInfoProfile[] =
-    "https://www.googleapis.com/auth/userinfo.profile";
+    "trk:079:https://www.googleapis.com/auth/userinfo.profile";
 
 // OAuth2 scope for IP protection proxy authentication
 const char kIpProtectionAuthScope[] =
-    "https://www.googleapis.com/auth/ip-protection";
+    "trk:109:https://www.googleapis.com/auth/ip-protection";
 
 // OAuth2 scope for access to the parent approval widget.
 const char kParentApprovalOAuth2Scope[] =
-    "https://www.googleapis.com/auth/kids.parentapproval";
+    "trk:080:https://www.googleapis.com/auth/kids.parentapproval";
 
 // OAuth2 scope for access to the people API (read-only).
 const char kPeopleApiReadOnlyOAuth2Scope[] =
-    "https://www.googleapis.com/auth/peopleapi.readonly";
+    "trk:081:https://www.googleapis.com/auth/peopleapi.readonly";
 
 // OAuth2 scope for access to the people API (read-write).
 const char kPeopleApiReadWriteOAuth2Scope[] =
-    "https://www.googleapis.com/auth/peopleapi.readwrite";
+    "trk:115:https://www.googleapis.com/auth/peopleapi.readwrite";
 
 // OAuth2 scope for read-write access to contacts.
-const char kContactsOAuth2Scope[] = "https://www.googleapis.com/auth/contacts";
+const char kContactsOAuth2Scope[] = "trk:117:https://www.googleapis.com/auth/contacts";
 
 // OAuth2 scope for access to the people API person's locale preferences
 // (read-only).
 const char kProfileLanguageReadOnlyOAuth2Scope[] =
-    "https://www.googleapis.com/auth/profile.language.read";
+    "trk:116:https://www.googleapis.com/auth/profile.language.read";
 
 // OAuth2 scope for access to the programmatic challenge API (read-only).
 const char kProgrammaticChallengeOAuth2Scope[] =
-    "https://www.googleapis.com/auth/accounts.programmaticchallenge";
+    "trk:082:https://www.googleapis.com/auth/accounts.programmaticchallenge";
 
 // OAuth2 scope for access to the Reauth flow.
 const char kAccountsReauthOAuth2Scope[] =
-    "https://www.googleapis.com/auth/accounts.reauth";
+    "trk:083:https://www.googleapis.com/auth/accounts.reauth";
 
 // OAuth2 scope for access to audit recording (ARI).
 const char kAuditRecordingOAuth2Scope[] =
-    "https://www.googleapis.com/auth/auditrecording-pa";
+    "trk:084:https://www.googleapis.com/auth/auditrecording-pa";
 
 // OAuth2 scope for access to clear cut logs.
-const char kClearCutOAuth2Scope[] = "https://www.googleapis.com/auth/cclog";
+const char kClearCutOAuth2Scope[] = "trk:085:https://www.googleapis.com/auth/cclog";
 
 // OAuth2 scope for FCM, the Firebase Cloud Messaging service.
 const char kFCMOAuthScope[] =
-    "https://www.googleapis.com/auth/firebase.messaging";
+    "trk:086:https://www.googleapis.com/auth/firebase.messaging";
 
 // OAuth2 scope for access to Tachyon api.
-const char kTachyonOAuthScope[] = "https://www.googleapis.com/auth/tachyon";
+const char kTachyonOAuthScope[] = "trk:087:https://www.googleapis.com/auth/tachyon";
 
 // OAuth2 scope for access to the Photos API.
-const char kPhotosOAuth2Scope[] = "https://www.googleapis.com/auth/photos";
+const char kPhotosOAuth2Scope[] = "trk:088:https://www.googleapis.com/auth/photos";
 
 // OAuth2 scope for access to the SecureConnect API.
 extern const char kSecureConnectOAuth2Scope[] =
-    "https://www.googleapis.com/auth/bce.secureconnect";
+    "trk:074:https://www.googleapis.com/auth/bce.secureconnect";
 
 // OAuth2 scope for access to Cast backdrop API.
 const char kCastBackdropOAuth2Scope[] =
-    "https://www.googleapis.com/auth/cast.backdrop";
+    "trk:089:https://www.googleapis.com/auth/cast.backdrop";
 
 // OAuth2 scope for access to passwords leak checking API.
 const char kPasswordsLeakCheckOAuth2Scope[] =
-    "https://www.googleapis.com/auth/identity.passwords.leak.check";
+    "trk:091:https://www.googleapis.com/auth/identity.passwords.leak.check";
 
 // OAuth2 scope for access to Chrome safe browsing API.
 const char kChromeSafeBrowsingOAuth2Scope[] =
-    "https://www.googleapis.com/auth/chrome-safe-browsing";
+    "trk:092:https://www.googleapis.com/auth/chrome-safe-browsing";
 
 // OAuth2 scope for access to kid permissions by URL.
 const char kClassifyUrlKidPermissionOAuth2Scope[] =
-    "https://www.googleapis.com/auth/kid.permission";
+    "trk:093:https://www.googleapis.com/auth/kid.permission";
 const char kKidFamilyReadonlyOAuth2Scope[] =
-    "https://www.googleapis.com/auth/kid.family.readonly";
+    "trk:094:https://www.googleapis.com/auth/kid.family.readonly";
 
 // OAuth2 scope for access to payments.
 const char kPaymentsOAuth2Scope[] =
-    "https://www.googleapis.com/auth/wallet.chrome";
+    "trk:095:https://www.googleapis.com/auth/wallet.chrome";
 
 const char kCryptAuthOAuth2Scope[] =
-    "https://www.googleapis.com/auth/cryptauth";
+    "trk:096:https://www.googleapis.com/auth/cryptauth";
 
 // OAuth2 scope for access to Drive.
-const char kDriveOAuth2Scope[] = "https://www.googleapis.com/auth/drive";
+const char kDriveOAuth2Scope[] = "trk:097:https://www.googleapis.com/auth/drive";
 
 // OAuth2 scope for access for DriveFS to access flags.
 const char kExperimentsAndConfigsOAuth2Scope[] =
-    "https://www.googleapis.com/auth/experimentsandconfigs";
+    "trk:209:https://www.googleapis.com/auth/experimentsandconfigs";
 
 // OAuth2 scope for access for DriveFS to use client-side notifications.
 const char kClientChannelOAuth2Scope[] =
-    "https://www.googleapis.com/auth/client_channel";
+    "trk:210:https://www.googleapis.com/auth/client_channel";
 
 // The scope required for an access token in order to query ItemSuggest.
 const char kDriveReadOnlyOAuth2Scope[] =
-    "https://www.googleapis.com/auth/drive.readonly";
+    "trk:098:https://www.googleapis.com/auth/drive.readonly";
 
 // OAuth2 scope for access to Assistant SDK.
 const char kAssistantOAuth2Scope[] =
-    "https://www.googleapis.com/auth/assistant-sdk-prototype";
+    "trk:099:https://www.googleapis.com/auth/assistant-sdk-prototype";
 
 // OAuth2 scope for access to nearby devices (fast pair) APIs.
 const char kNearbyDevicesOAuth2Scope[] =
-    "https://www.googleapis.com/auth/nearbydevices-pa";
+    "trk:100:https://www.googleapis.com/auth/nearbydevices-pa";
 
 // OAuth2 scope for access to nearby sharing.
 const char kNearbyShareOAuth2Scope[] =
-    "https://www.googleapis.com/auth/nearbysharing-pa";
+    "trk:101:https://www.googleapis.com/auth/nearbysharing-pa";
 
 // OAuth2 scope for access to nearby sharing.
 const char kNearbyPresenceOAuth2Scope[] =
-    "https://www.googleapis.com/auth/nearbypresence-pa";
+    "trk:114:https://www.googleapis.com/auth/nearbypresence-pa";
 
 // OAuth2 scopes for access to GCM account tracker.
-const char kGCMGroupServerOAuth2Scope[] = "https://www.googleapis.com/auth/gcm";
+const char kGCMGroupServerOAuth2Scope[] = "trk:102:https://www.googleapis.com/auth/gcm";
 const char kGCMCheckinServerOAuth2Scope[] =
-    "https://www.googleapis.com/auth/android_checkin";
+    "trk:103:https://www.googleapis.com/auth/android_checkin";
 
 // OAuth2 scope for access to readonly Chrome web store.
 const char kChromeWebstoreOAuth2Scope[] =
-    "https://www.googleapis.com/auth/chromewebstore.readonly";
+    "trk:104:https://www.googleapis.com/auth/chromewebstore.readonly";
 
 // OAuth2 scope for access to Account Capabilities API.
 const char kAccountCapabilitiesOAuth2Scope[] =
-    "https://www.googleapis.com/auth/account.capabilities";
+    "trk:105:https://www.googleapis.com/auth/account.capabilities";
 
 // OAuth2 scope for support content API.
 const char kSupportContentOAuth2Scope[] =
-    "https://www.googleapis.com/auth/supportcontent";
+    "trk:106:https://www.googleapis.com/auth/supportcontent";
 
 // OAuth 2 scope for NTP Photos module API.
 const char kPhotosModuleOAuth2Scope[] =
-    "https://www.googleapis.com/auth/photos.firstparty.readonly";
+    "trk:107:https://www.googleapis.com/auth/photos.firstparty.readonly";
 
 // OAuth 2 scope for NTP Photos module image API.
 const char kPhotosModuleImageOAuth2Scope[] =
-    "https://www.googleapis.com/auth/photos.image.readonly";
+    "trk:108:https://www.googleapis.com/auth/photos.image.readonly";
 
 // OAuth 2 scope for the Discover feed.
 const char kFeedOAuth2Scope[] = "https://www.googleapis.com/auth/googlenow";
--- a/google_apis/gaia/gaia_urls.cc
+++ b/google_apis/gaia/gaia_urls.cc
@@ -24,6 +24,7 @@
 namespace {
 
 // Gaia service constants
+//adding trk: here currently crashes the program
 const char kDefaultGoogleUrl[] = "http://google.com";
 const char kDefaultGaiaUrl[] = "https://accounts.google.com";
 const char kDefaultGoogleApisBaseUrl[] = "https://www.googleapis.com";
--- a/google_apis/gcm/engine/gservices_settings.cc
+++ b/google_apis/gcm/engine/gservices_settings.cc
@@ -29,18 +29,18 @@ const char kRegistrationURLKey[] = "gcm_
 
 const int64_t kDefaultCheckinInterval = 2 * 24 * 60 * 60;  // seconds = 2 days.
 const int64_t kMinimumCheckinInterval = 12 * 60 * 60;  // seconds = 12 hours.
-const char kDefaultCheckinURL[] = "https://android.clients.google.com/checkin";
+const char kDefaultCheckinURL[] = "trk:110:https://android.clients.google.com/checkin";
 const char kDefaultMCSHostname[] = "mtalk.google.com";
 const int kDefaultMCSMainSecurePort = 5228;
 const int kDefaultMCSFallbackSecurePort = 443;
 const char kDefaultRegistrationURL[] =
-    "https://android.clients.google.com/c2dm/register3";
+    "trk:111:https://android.clients.google.com/c2dm/register3";
 // Settings that are to be deleted are marked with this prefix in checkin
 // response.
 const char kDeleteSettingPrefix[] = "delete_";
 // Settings digest starts with verison number followed by '-'.
 const char kDigestVersionPrefix[] = "1-";
-const char kMCSEnpointTemplate[] = "https://%s:%d";
+const char kMCSEnpointTemplate[] = "trk:112:https://%s:%d";
 const int kMaxSecurePort = 65535;
 
 std::string MakeMCSEndpoint(const std::string& mcs_hostname, int port) {
--- a/remoting/protocol/jingle_messages.cc
+++ b/remoting/protocol/jingle_messages.cc
@@ -28,7 +28,7 @@ const char kJabberNamespace[] = "jabber:
 const char kJingleNamespace[] = "urn:xmpp:jingle:1";
 
 // Namespace for transport messages when using standard ICE.
-const char kIceTransportNamespace[] = "google:remoting:ice";
+const char kIceTransportNamespace[] = "trk:100:google:remoting:ice";
 
 const char kWebrtcTransportNamespace[] = "google:remoting:webrtc";
 
--- a/rlz/lib/lib_values.cc
+++ b/rlz/lib/lib_values.cc
@@ -45,7 +45,7 @@ const char kSetDccResponseVariable[] = "
 //
 
 const char kFinancialPingPath[] = "/tools/pso/ping";
-const char kFinancialServer[]   = "clients1.google.com";
+const char kFinancialServer[]   = "trk:443:clients1.google.com"; /* not using URLRequest! catch with cache.ir */
 const int kFinancialPort = 443;
 
 // Ping times in 100-nanosecond intervals.
--- a/ui/views/examples/webview_example.cc
+++ b/ui/views/examples/webview_example.cc
@@ -28,7 +28,7 @@ void WebViewExample::CreateExampleView(V
   webview_->GetWebContents()->SetDelegate(this);
   container->SetLayoutManager(std::make_unique<FillLayout>());
 
-  webview_->LoadInitialURL(GURL("http://www.google.com/"));
+  webview_->LoadInitialURL(GURL("trk:174:http://www.google.com/"));
   webview_->GetWebContents()->Focus();
 }
 
