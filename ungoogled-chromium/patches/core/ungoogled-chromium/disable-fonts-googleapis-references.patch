# Disables references to fonts.googleapis.com

--- a/components/dom_distiller/content/browser/dom_distiller_viewer_source.cc
+++ b/components/dom_distiller/content/browser/dom_distiller_viewer_source.cc
@@ -295,7 +295,7 @@ bool DomDistillerViewerSource::ShouldSer
 std::string DomDistillerViewerSource::GetContentSecurityPolicy(
     network::mojom::CSPDirectiveName directive) {
   if (directive == network::mojom::CSPDirectiveName::StyleSrc) {
-    return "style-src 'self' https://fonts.googleapis.com;";
+    return "style-src 'self';";
   } else if (directive == network::mojom::CSPDirectiveName::ChildSrc) {
     return "child-src *;";
   } else if (directive ==
--- a/components/dom_distiller/core/html/preview.html
+++ b/components/dom_distiller/core/html/preview.html
@@ -11,7 +11,7 @@ found in the LICENSE file.
   <meta name="theme-color" id="theme-color">
   <title>Title goes here and it could be kind of lengthy - Publisher name</title>
   <link href="../css/distilledpage.css" rel="stylesheet" type="text/css">
-  <link href='https://fonts.googleapis.com/css?family=Roboto' rel='stylesheet' type='text/css'>
+  <link href='chrome://resources/css/roboto.css' rel='stylesheet' type='text/css'>
   <style>
     .english :lang(th) {display: none}
     .english :lang(zh) {display: none}
--- a/components/dom_distiller/core/javascript/dom_distiller_viewer.js
+++ b/components/dom_distiller/core/javascript/dom_distiller_viewer.js
@@ -153,7 +153,7 @@ function maybeSetWebFont() {
   }
 
   const e = document.createElement('link');
-  e.href = 'https://fonts.googleapis.com/css?family=Roboto';
+  e.href = 'chrome://resources/css/roboto.css';
   e.rel = 'stylesheet';
   e.type = 'text/css';
   document.head.appendChild(e);
--- a/third_party/crashpad/crashpad/doc/support/crashpad_doxygen.css
+++ b/third_party/crashpad/crashpad/doc/support/crashpad_doxygen.css
@@ -12,11 +12,11 @@
  * See the License for the specific language governing permissions and
  * limitations under the License. */
 
-@import url('https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&display=swap');
-@import url('https://fonts.googleapis.com/css2?family=Source+Code+Pro:ital,wght@0,200..900;1,200..900&display=swap');
+@import "chrome://resources/css/roboto.css";
 
 :root {
   --font-family-normal: 'Noto Sans',
+    'Roboto',
     'Lucida Grande',
     'Lucida Sans Unicode',
     Helvetica,
--- a/tools/md_browser/base.css
+++ b/tools/md_browser/base.css
@@ -16,8 +16,7 @@
 
 /* Common styles and definitions. */
 
-@import "//fonts.googleapis.com/css?family=Open+Sans:300,400,700&subset=latin,cyrillic-ext,greek-ext,cyrillic,greek,vietnamese,latin-ext";
-@import "//fonts.googleapis.com/css?family=Source+Code+Pro";
+@import "chrome://resources/css/roboto.css";
 *,
 *::after,
 *::before {
@@ -72,7 +71,7 @@ ul, ol {
   user-select: none;
 }
 .u-monospace {
-  font-family: 'Source Code Pro', monospace;
+  font-family: monospace;
 }
 
 /* Common.soy */
@@ -82,7 +81,7 @@ ul, ol {
   color: #000;
   display: -ms-flexbox;
   display: flex;
-  font: 14px/1.54 'Open Sans', sans-serif;
+  font: 14px/1.54 'Roboto', sans-serif;
   min-height: 100vh;
   -ms-flex-direction: column;
   flex-direction: column;
