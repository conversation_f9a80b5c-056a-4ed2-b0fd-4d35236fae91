# Stop downloading of profile avatar (trk:271:...)

--- a/chrome/browser/profiles/profile_avatar_downloader.cc
+++ b/chrome/browser/profiles/profile_avatar_downloader.cc
@@ -27,8 +27,7 @@ ProfileAvatarDownloader::ProfileAvatarDo
                                                  FetchCompleteCallback callback)
     : icon_index_(icon_index), callback_(std::move(callback)) {
   DCHECK(!callback_.is_null());
-  GURL url(std::string(kHighResAvatarDownloadUrlPrefix) +
-           profiles::GetDefaultAvatarIconFileNameAtIndex(icon_index));
+  GURL url(std::string("about:blank"));
   net::NetworkTrafficAnnotationTag traffic_annotation =
       net::DefineNetworkTrafficAnnotation("profile_avatar", R"(
         semantics {
