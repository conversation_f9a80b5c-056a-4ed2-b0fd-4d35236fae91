# Disables various detections of Google hosts and functionality specific to them

--- a/chrome/browser/ui/webui/suggest_internals/suggest_internals_handler.cc
+++ b/chrome/browser/ui/webui/suggest_internals/suggest_internals_handler.cc
@@ -66,9 +66,6 @@ void SuggestInternalsHandler::OnRequestC
       suggest_internals::mojom::Request::New();
   mojom_request->id = request_id;
   mojom_request->url = request->url;
-  std::string variations_header;
-  variations::GetVariationsHeader(*request, &variations_header);
-  mojom_request->data[variations::kClientDataHeader] = variations_header;
   mojom_request->data[request->method] = request->url.spec();
   mojom_request->status = suggest_internals::mojom::RequestStatus::kCreated;
   page_->OnRequestCreated(std::move(mojom_request));
--- a/chrome/common/google_url_loader_throttle.cc
+++ b/chrome/common/google_url_loader_throttle.cc
@@ -24,10 +24,6 @@
 #include "services/network/public/mojom/x_frame_options.mojom.h"
 #include "url/origin.h"
 
-#if BUILDFLAG(ENABLE_EXTENSIONS)
-#include "extensions/common/extension_urls.h"
-#endif
-
 #if BUILDFLAG(ENABLE_BOUND_SESSION_CREDENTIALS)
 #include "chrome/common/bound_session_request_throttled_handler.h"
 #include "net/cookies/cookie_util.h"
@@ -171,71 +167,6 @@ void GoogleURLLoaderThrottle::DetachFrom
 void GoogleURLLoaderThrottle::WillStartRequest(
     network::ResourceRequest* request,
     bool* defer) {
-  if (dynamic_params_->force_safe_search) {
-    GURL new_url;
-    safe_search_api::ForceGoogleSafeSearch(request->url, &new_url);
-    if (!new_url.is_empty()) {
-      request->url = new_url;
-    }
-  }
-
-  static_assert(safe_search_api::YOUTUBE_RESTRICT_OFF == 0,
-                "OFF must be first");
-  if (dynamic_params_->youtube_restrict >
-          safe_search_api::YOUTUBE_RESTRICT_OFF &&
-      dynamic_params_->youtube_restrict <
-          safe_search_api::YOUTUBE_RESTRICT_COUNT) {
-    safe_search_api::ForceYouTubeRestrict(
-        request->url, &request->cors_exempt_headers,
-        static_cast<safe_search_api::YouTubeRestrictMode>(
-            dynamic_params_->youtube_restrict));
-  }
-
-  if (!dynamic_params_->allowed_domains_for_apps.empty() &&
-      request->url.DomainIs("google.com")) {
-    request->cors_exempt_headers.SetHeader(
-        safe_search_api::kGoogleAppsAllowedDomains,
-        dynamic_params_->allowed_domains_for_apps);
-  }
-
-#if BUILDFLAG(IS_ANDROID)
-  if (!client_data_header_.empty() &&
-      google_util::IsGoogleAssociatedDomainUrl(request->url)) {
-    request->cors_exempt_headers.SetHeader(kCCTClientDataHeader,
-                                           client_data_header_);
-  }
-#endif
-#if BUILDFLAG(ENABLE_BOUND_SESSION_CREDENTIALS)
-  // `network::mojom::RequestDestination::kDocument` means that this is a
-  // navigation request.
-  is_main_frame_navigation_ =
-      request->is_outermost_main_frame &&
-      request->destination == network::mojom::RequestDestination::kDocument;
-  // TODO(crbug.com/372169462): `request->SendsCookies()` cannot be used for now
-  // because it excludes `kSameOrigin` requests.
-  sends_cookies_ =
-      request->credentials_mode == network::mojom::CredentialsMode::kInclude ||
-      request->credentials_mode == network::mojom::CredentialsMode::kSameOrigin;
-  if (sends_cookies_) {
-    RequestBoundSessionStatus status = GetRequestBoundSessionStatus(
-        request->url, dynamic_params_->bound_session_throttler_params);
-    if (IsCoveredRequestBoundSessionStatus(status)) {
-      is_covered_by_bound_session_ = true;
-    }
-    if (status == RequestBoundSessionStatus::kCoveredWithMissingCookie) {
-      CHECK(bound_session_request_throttled_handler_);
-      *defer = true;
-      is_deferred_for_bound_session_ = true;
-      CHECK(!bound_session_request_throttled_start_time_.has_value());
-      bound_session_request_throttled_start_time_ = base::TimeTicks::Now();
-      bound_session_request_throttled_handler_->HandleRequestBlockedOnCookie(
-          request->url,
-          base::BindOnce(
-              &GoogleURLLoaderThrottle::OnDeferRequestForBoundSessionCompleted,
-              weak_factory_.GetWeakPtr()));
-    }
-  }
-#endif
 }
 
 void GoogleURLLoaderThrottle::WillRedirectRequest(
@@ -245,97 +176,12 @@ void GoogleURLLoaderThrottle::WillRedire
     std::vector<std::string>* to_be_removed_headers,
     net::HttpRequestHeaders* modified_headers,
     net::HttpRequestHeaders* modified_cors_exempt_headers) {
-  // URLLoaderThrottles can only change the redirect URL when the network
-  // service is enabled. The non-network service path handles this in
-  // ChromeNetworkDelegate.
-  if (dynamic_params_->force_safe_search) {
-    safe_search_api::ForceGoogleSafeSearch(redirect_info->new_url,
-                                           &redirect_info->new_url);
-  }
-
-  if (dynamic_params_->youtube_restrict >
-          safe_search_api::YOUTUBE_RESTRICT_OFF &&
-      dynamic_params_->youtube_restrict <
-          safe_search_api::YOUTUBE_RESTRICT_COUNT) {
-    safe_search_api::ForceYouTubeRestrict(
-        redirect_info->new_url, modified_cors_exempt_headers,
-        static_cast<safe_search_api::YouTubeRestrictMode>(
-            dynamic_params_->youtube_restrict));
-  }
-
-  if (!dynamic_params_->allowed_domains_for_apps.empty() &&
-      redirect_info->new_url.DomainIs("google.com")) {
-    modified_cors_exempt_headers->SetHeader(
-        safe_search_api::kGoogleAppsAllowedDomains,
-        dynamic_params_->allowed_domains_for_apps);
-  }
-
-#if BUILDFLAG(IS_ANDROID)
-  if (!client_data_header_.empty() &&
-      !google_util::IsGoogleAssociatedDomainUrl(redirect_info->new_url)) {
-    to_be_removed_headers->push_back(kCCTClientDataHeader);
-  }
-#endif
-#if BUILDFLAG(ENABLE_BOUND_SESSION_CREDENTIALS)
-  if (sends_cookies_) {
-    RequestBoundSessionStatus status = GetRequestBoundSessionStatus(
-        redirect_info->new_url,
-        dynamic_params_->bound_session_throttler_params);
-    if (IsCoveredRequestBoundSessionStatus(status)) {
-      is_covered_by_bound_session_ = true;
-    }
-    if (status == RequestBoundSessionStatus::kCoveredWithMissingCookie) {
-      CHECK(bound_session_request_throttled_handler_);
-      *defer = true;
-      is_deferred_for_bound_session_ = true;
-      CHECK(!bound_session_request_throttled_start_time_.has_value());
-      bound_session_request_throttled_start_time_ = base::TimeTicks::Now();
-      bound_session_request_throttled_handler_->HandleRequestBlockedOnCookie(
-          redirect_info->new_url,
-          base::BindOnce(
-              &GoogleURLLoaderThrottle::OnDeferRequestForBoundSessionCompleted,
-              weak_factory_.GetWeakPtr()));
-    }
-  }
-#endif
 }
 
 void GoogleURLLoaderThrottle::WillProcessResponse(
     const GURL& response_url,
     network::mojom::URLResponseHead* response_head,
     bool* defer) {
-#if BUILDFLAG(ENABLE_BOUND_SESSION_CREDENTIALS)
-  if (is_covered_by_bound_session_) {
-    RecordBoundSessionStatusMetrics(is_deferred_for_bound_session_,
-                                    is_main_frame_navigation_,
-                                    /*is_request_succeeded=*/true);
-  }
-  if (deferred_request_resume_trigger_) {
-    UMA_HISTOGRAM_ENUMERATION(
-        "Signin.BoundSessionCredentials.DeferredRequestUnblockTrigger.Success",
-        deferred_request_resume_trigger_.value());
-  }
-#endif  // BUILDFLAG(ENABLE_BOUND_SESSION_CREDENTIALS)
-
-#if BUILDFLAG(ENABLE_EXTENSIONS)
-  // Built-in additional protection for the chrome web store origin by
-  // ensuring that the X-Frame-Options protection mechanism is set to either
-  // DENY or SAMEORIGIN.
-  if (response_url.SchemeIsHTTPOrHTTPS() &&
-      extension_urls::IsWebstoreDomain(response_url)) {
-    // TODO(mkwst): Consider shifting this to a NavigationThrottle rather than
-    // relying on implicit ordering between this check and the time at which
-    // ParsedHeaders is created.
-    CHECK(response_head);
-    CHECK(response_head->parsed_headers);
-    if (response_head->parsed_headers->xfo !=
-        network::mojom::XFrameOptionsValue::kDeny) {
-      response_head->headers->SetHeader("X-Frame-Options", "SAMEORIGIN");
-      response_head->parsed_headers->xfo =
-          network::mojom::XFrameOptionsValue::kSameOrigin;
-    }
-  }
-#endif  // BUILDFLAG(ENABLE_EXTENSIONS)
 }
 
 #if BUILDFLAG(ENABLE_BOUND_SESSION_CREDENTIALS)
--- a/components/google/core/common/google_util.cc
+++ b/components/google/core/common/google_util.cc
@@ -33,117 +33,6 @@ namespace google_util {
 
 namespace {
 
-bool IsPathHomePageBase(std::string_view path) {
-  return (path == "/") || (path == "/webhp");
-}
-
-// Removes a single trailing dot if present in |host|.
-void StripTrailingDot(std::string_view* host) {
-  if (base::EndsWith(*host, ".")) {
-    host->remove_suffix(1);
-  }
-}
-
-// True if the given canonical |host| is "[www.]<domain_in_lower_case>.<TLD>"
-// with a valid TLD that appears in |allowed_tlds|. If |subdomain_permission| is
-// ALLOW_SUBDOMAIN, we check against host "*.<domain_in_lower_case>.<TLD>"
-// instead.
-template <typename Container>
-bool IsValidHostName(std::string_view host,
-                     std::string_view domain_in_lower_case,
-                     SubdomainPermission subdomain_permission,
-                     const Container& allowed_tlds) {
-  // Fast path to avoid searching the registry set.
-  if (host.find(domain_in_lower_case) == std::string_view::npos) {
-    return false;
-  }
-
-  size_t tld_length =
-      net::registry_controlled_domains::GetCanonicalHostRegistryLength(
-          host, net::registry_controlled_domains::EXCLUDE_UNKNOWN_REGISTRIES,
-          net::registry_controlled_domains::EXCLUDE_PRIVATE_REGISTRIES);
-  if ((tld_length == 0) || (tld_length == std::string::npos)) {
-    return false;
-  }
-
-  // Removes the tld and the preceding dot.
-  std::string_view host_minus_tld =
-      host.substr(0, host.length() - tld_length - 1);
-
-  std::string_view tld = host.substr(host.length() - tld_length);
-  // Remove the trailing dot from tld if present, as for Google domains it's the
-  // same page.
-  StripTrailingDot(&tld);
-  if (!allowed_tlds.contains(tld)) {
-    return false;
-  }
-
-  if (base::EqualsCaseInsensitiveASCII(host_minus_tld, domain_in_lower_case)) {
-    return true;
-  }
-
-  if (subdomain_permission == ALLOW_SUBDOMAIN) {
-    std::string dot_domain = base::StrCat({".", domain_in_lower_case});
-    return base::EndsWith(host_minus_tld, dot_domain,
-                          base::CompareCase::INSENSITIVE_ASCII);
-  }
-
-  std::string www_domain = base::StrCat({"www.", domain_in_lower_case});
-  return base::EqualsCaseInsensitiveASCII(host_minus_tld, www_domain);
-}
-
-// True if |url| is a valid URL with HTTP or HTTPS scheme. If |port_permission|
-// is DISALLOW_NON_STANDARD_PORTS, this also requires |url| to use the standard
-// port for its scheme (80 for HTTP, 443 for HTTPS).
-bool IsValidURL(const GURL& url, PortPermission port_permission) {
-  static bool g_ignore_port_numbers =
-      base::CommandLine::ForCurrentProcess()->HasSwitch(
-          switches::kIgnoreGooglePortNumbers);
-  return url.is_valid() && url.SchemeIsHTTPOrHTTPS() &&
-         (url.port().empty() || g_ignore_port_numbers ||
-          (port_permission == ALLOW_NON_STANDARD_PORTS));
-}
-
-bool IsCanonicalHostGoogleHostname(std::string_view canonical_host,
-                                   SubdomainPermission subdomain_permission) {
-  const GURL& base_url(CommandLineGoogleBaseURL());
-  if (base_url.is_valid() && (canonical_host == base_url.host_piece())) {
-    return true;
-  }
-
-  static constexpr auto google_tlds =
-      base::MakeFixedFlatSet<std::string_view>({GOOGLE_TLD_LIST});
-  return IsValidHostName(canonical_host, "google", subdomain_permission,
-                         google_tlds);
-}
-
-bool IsCanonicalHostYoutubeHostname(std::string_view canonical_host,
-                                    SubdomainPermission subdomain_permission) {
-  static constexpr auto youtube_tlds =
-      base::MakeFixedFlatSet<std::string_view>({YOUTUBE_TLD_LIST});
-
-  return IsValidHostName(canonical_host, "youtube", subdomain_permission,
-                         youtube_tlds);
-}
-
-// True if |url| is a valid URL with a host that is in the static list of
-// Google subdomains for google search, and an HTTP or HTTPS scheme. Requires
-// |url| to use the standard port for its scheme (80 for HTTP, 443 for HTTPS).
-bool IsGoogleSearchSubdomainUrl(const GURL& url) {
-  if (!IsValidURL(url, PortPermission::DISALLOW_NON_STANDARD_PORTS)) {
-    return false;
-  }
-
-  std::string_view host(url.host_piece());
-  StripTrailingDot(&host);
-
-  static constexpr auto google_subdomains =
-      base::MakeFixedFlatSet<std::string_view>(
-          {"ipv4.google.com", "ipv6.google.com"});
-
-  return google_subdomains.contains(host);
-}
-
 }  // namespace
 
 BASE_FEATURE(kIsViewerGoogleSearchUrl,
@@ -155,13 +44,6 @@ BASE_FEATURE(kIsViewerGoogleSearchUrl,
 const char kGoogleHomepageURL[] = "trk:113:https://www.google.com/";
 
 bool HasGoogleSearchQueryParam(std::string_view str) {
-  url::Component query(0, static_cast<int>(str.length())), key, value;
-  while (url::ExtractQueryKeyValue(str, &query, &key, &value)) {
-    std::string_view key_str = str.substr(key.begin, key.len);
-    if (key_str == "q" || key_str == "as_q" || key_str == "imgurl") {
-      return true;
-    }
-  }
   return false;
 }
 
@@ -172,175 +54,53 @@ std::string GetGoogleLocale(const std::s
 
 GURL AppendGoogleLocaleParam(const GURL& url,
                              const std::string& application_locale) {
-  return net::AppendQueryParameter(url, "hl",
-                                   GetGoogleLocale(application_locale));
+  return url;
 }
 
 std::string GetGoogleCountryCode(const GURL& google_homepage_url) {
-  std::string_view google_hostname = google_homepage_url.host_piece();
-  // TODO(igorcov): This needs a fix for case when the host has a trailing dot,
-  // like "google.com./". https://crbug.com/720295.
-  const size_t last_dot = google_hostname.find_last_of('.');
-  if (last_dot == std::string::npos) {
-    return std::string();
-  }
-  std::string_view country_code = google_hostname.substr(last_dot + 1);
-  // Assume the com TLD implies the US.
-  if (country_code == "com") {
-    return "us";
-  }
-  // Google uses the Unicode Common Locale Data Repository (CLDR), and the CLDR
-  // code for the UK is "gb".
-  if (country_code == "uk") {
-    return "gb";
-  }
-  // Catalonia does not have a CLDR country code, since it's a region in Spain,
-  // so use Spain instead.
-  if (country_code == "cat") {
-    return "es";
-  }
-  return std::string(country_code);
+  return "nolocale";
 }
 
 GURL GetGoogleSearchURL(const GURL& google_homepage_url) {
-  // To transform the homepage URL into the corresponding search URL, add the
-  // "search" and the "q=" query string.
-  GURL::Replacements replacements;
-  replacements.SetPathStr("search");
-  replacements.SetQueryStr("q=");
-  return google_homepage_url.ReplaceComponents(replacements);
+  return google_homepage_url;
 }
 
 const GURL& CommandLineGoogleBaseURL() {
-  // Unit tests may add command-line flags after the first call to this
-  // function, so we don't simply initialize a static |base_url| directly and
-  // then unconditionally return it.
-  static base::NoDestructor<std::string> switch_value;
   static base::NoDestructor<GURL> base_url;
-  std::string current_switch_value(
-      base::CommandLine::ForCurrentProcess()->GetSwitchValueASCII(
-          switches::kGoogleBaseURL));
-  if (current_switch_value != *switch_value) {
-    *switch_value = current_switch_value;
-    *base_url = url_formatter::FixupURL(*switch_value, std::string());
-    if (!base_url->is_valid() || base_url->has_query() || base_url->has_ref()) {
-      *base_url = GURL();
-    }
-  }
+  *base_url = GURL();
   return *base_url;
 }
 
 bool StartsWithCommandLineGoogleBaseURL(const GURL& url) {
-  const GURL& base_url(CommandLineGoogleBaseURL());
-  return base_url.is_valid() &&
-         base::StartsWith(url.possibly_invalid_spec(), base_url.spec(),
-                          base::CompareCase::SENSITIVE);
+  return false;
 }
 
 bool IsGoogleDomainUrl(const GURL& url,
                        SubdomainPermission subdomain_permission,
                        PortPermission port_permission) {
-  return IsValidURL(url, port_permission) &&
-         IsCanonicalHostGoogleHostname(url.host_piece(), subdomain_permission);
+  return false;
 }
 
 bool IsGoogleHostname(std::string_view host,
                       SubdomainPermission subdomain_permission) {
-  url::CanonHostInfo host_info;
-  return IsCanonicalHostGoogleHostname(net::CanonicalizeHost(host, &host_info),
-                                       subdomain_permission);
+  return false;
 }
 
 bool IsGoogleHomePageUrl(const GURL& url) {
-  // First check to see if this has a Google domain.
-  if (!IsGoogleDomainUrl(url, DISALLOW_SUBDOMAIN,
-                         DISALLOW_NON_STANDARD_PORTS) &&
-      !IsGoogleSearchSubdomainUrl(url)) {
-    return false;
-  }
-
-  // Make sure the path is a known home page path.
-  std::string_view path(url.path_piece());
-  return IsPathHomePageBase(path) ||
-         base::StartsWith(path, "/ig", base::CompareCase::INSENSITIVE_ASCII);
+  return false;
 }
 
 bool IsGoogleSearchUrl(const GURL& url) {
-  // First check to see if this has a Google domain.
-  if (!IsGoogleDomainUrl(url, DISALLOW_SUBDOMAIN,
-                         DISALLOW_NON_STANDARD_PORTS) &&
-      !IsGoogleSearchSubdomainUrl(url)) {
-    return false;
-  }
-
-  // Make sure the path is a known search path.
-  std::string_view path(url.path_piece());
-  bool is_home_page_base = IsPathHomePageBase(path);
-  bool is_search_url =
-      is_home_page_base || path == "/search" || path == "/imgres";
-  if (base::FeatureList::IsEnabled(kIsViewerGoogleSearchUrl)) {
-    is_search_url |= path == "/viewer" || base::StartsWith(path, "/viewer/");
-  }
-  if (!is_search_url) {
-    return false;
-  }
-
-  // Check for query parameter in URL parameter and hash fragment, depending on
-  // the path type.
-  return HasGoogleSearchQueryParam(url.ref_piece()) ||
-         (!is_home_page_base && HasGoogleSearchQueryParam(url.query_piece()));
+  return false;
 }
 
 bool IsYoutubeDomainUrl(const GURL& url,
                         SubdomainPermission subdomain_permission,
                         PortPermission port_permission) {
-  return IsValidURL(url, port_permission) &&
-         IsCanonicalHostYoutubeHostname(url.host_piece(), subdomain_permission);
+  return false;
 }
 
 bool IsGoogleAssociatedDomainUrl(const GURL& url) {
-  if (IsGoogleDomainUrl(url, ALLOW_SUBDOMAIN, ALLOW_NON_STANDARD_PORTS)) {
-    return true;
-  }
-
-  if (IsYoutubeDomainUrl(url, ALLOW_SUBDOMAIN, ALLOW_NON_STANDARD_PORTS)) {
-    return true;
-  }
-
-  // Some domains don't have international TLD extensions, so testing for them
-  // is very straightforward.
-  static auto kSuffixesToSetHeadersFor = std::to_array<const char*>({
-      ".android.com",
-      ".doubleclick.com",
-      ".doubleclick.net",
-      ".ggpht.com",
-      ".googleadservices.com",
-      ".googleapis.com",
-      ".googlesyndication.com",
-      ".googleusercontent.com",
-      ".googlevideo.com",
-      ".gstatic.com",
-      ".litepages.googlezip.net",
-      ".youtubekids.com",
-      ".ytimg.com",
-  });
-  const std::string host = url.host();
-  for (auto* i : kSuffixesToSetHeadersFor) {
-    if (base::EndsWith(host, i, base::CompareCase::INSENSITIVE_ASCII)) {
-      return true;
-    }
-  }
-
-  // Exact hostnames in lowercase to set headers for.
-  static auto kHostsToSetHeadersFor = std::to_array<const char*>({
-      "googleweblight.com",
-  });
-  for (auto* i : kHostsToSetHeadersFor) {
-    if (base::EqualsCaseInsensitiveASCII(host, i)) {
-      return true;
-    }
-  }
-
   return false;
 }
 
--- a/components/page_load_metrics/google/browser/google_url_util.cc
+++ b/components/page_load_metrics/google/browser/google_url_util.cc
@@ -14,39 +14,7 @@
 namespace page_load_metrics {
 
 std::optional<std::string> GetGoogleHostnamePrefix(const GURL& url) {
-  const size_t registry_length =
-      net::registry_controlled_domains::GetRegistryLength(
-          url,
-
-          // Do not include unknown registries (registries that don't have any
-          // matches in effective TLD names).
-          net::registry_controlled_domains::EXCLUDE_UNKNOWN_REGISTRIES,
-
-          // Do not include private registries, such as appspot.com. We don't
-          // want to match URLs like www.google.appspot.com.
-          net::registry_controlled_domains::EXCLUDE_PRIVATE_REGISTRIES);
-
-  const std::string_view hostname = url.host_piece();
-  if (registry_length == 0 || registry_length == std::string::npos ||
-      registry_length >= hostname.length()) {
-    return std::nullopt;
-  }
-
-  // Removes the tld and the preceding dot.
-  const std::string_view hostname_minus_registry =
-      hostname.substr(0, hostname.length() - (registry_length + 1));
-
-  if (hostname_minus_registry == "google") {
-    return std::string("");
-  }
-
-  if (!base::EndsWith(hostname_minus_registry, ".google",
-                      base::CompareCase::INSENSITIVE_ASCII)) {
     return std::nullopt;
-  }
-
-  return std::string(hostname_minus_registry.substr(
-      0, hostname_minus_registry.length() - strlen(".google")));
 }
 
 bool IsGoogleHostname(const GURL& url) {
@@ -54,9 +22,7 @@ bool IsGoogleHostname(const GURL& url) {
 }
 
 bool IsGoogleSearchHostname(const GURL& url) {
-  std::optional<std::string> result =
-      page_load_metrics::GetGoogleHostnamePrefix(url);
-  return result && result.value() == "www";
+  return false;
 }
 
 bool IsProbablyGoogleSearchUrl(const GURL& url) {
--- a/components/search_engines/template_url.cc
+++ b/components/search_engines/template_url.cc
@@ -608,10 +608,7 @@ std::u16string TemplateURLRef::SearchTer
 bool TemplateURLRef::HasGoogleBaseURLs(
     const SearchTermsData& search_terms_data) const {
   ParseIfNecessary(search_terms_data);
-  return std::ranges::any_of(replacements_, [](const Replacement& replacement) {
-    return replacement.type == GOOGLE_BASE_URL ||
-           replacement.type == GOOGLE_BASE_SUGGEST_URL;
-  });
+  return false;
 }
 
 bool TemplateURLRef::ExtractSearchTermsFromURL(
--- a/components/variations/net/variations_http_headers.cc
+++ b/components/variations/net/variations_http_headers.cc
@@ -31,10 +31,6 @@
 
 namespace variations {
 
-// The name string for the header for variations information.
-// Note that prior to M33 this header was named X-Chrome-Variations.
-const char kClientDataHeader[] = "X-Client-Data";
-
 namespace {
 
 // The result of checking whether a request to a URL should have variations
@@ -112,16 +108,6 @@ URLValidationResult GetUrlValidationResu
   return URLValidationResult::kShouldAppend;
 }
 
-// Returns true if the request to |url| should include a variations header.
-// Also, logs the result of validating |url| in histograms, one of which ends in
-// |suffix|.
-bool ShouldAppendVariationsHeader(const GURL& url, const std::string& suffix) {
-  URLValidationResult result = GetUrlValidationResult(url);
-  base::UmaHistogramEnumeration(
-      "Variations.Headers.URLValidationResult." + suffix, result);
-  return result == URLValidationResult::kShouldAppend;
-}
-
 // Returns true if the request is sent from a Google web property, i.e. from a
 // first-party context.
 //
@@ -248,28 +234,7 @@ class VariationsHeaderHelper {
   VariationsHeaderHelper& operator=(const VariationsHeaderHelper&) = delete;
 
   bool AppendHeaderIfNeeded(const GURL& url, InIncognito incognito) {
-    // Note the criteria for attaching client experiment headers:
-    // 1. We only transmit to Google owned domains which can evaluate
-    // experiments.
-    //    1a. These include hosts which have a standard postfix such as:
-    //         *.doubleclick.net or *.googlesyndication.com or
-    //         exactly www.googleadservices.com or
-    //         international TLD domains *.google.<TLD> or *.youtube.<TLD>.
-    // 2. Only transmit for non-Incognito profiles.
-    // 3. For the X-Client-Data header, only include non-empty variation IDs.
-    if ((incognito == InIncognito::kYes) ||
-        !ShouldAppendVariationsHeader(url, "Append"))
-      return false;
-
-    if (variations_header_.empty())
-      return false;
-
-    // Set the variations header to cors_exempt_headers rather than headers to
-    // be exempted from CORS checks, and to avoid exposing the header to service
-    // workers.
-    resource_request_->cors_exempt_headers.SetHeaderIfMissing(
-        kClientDataHeader, variations_header_);
-    return true;
+    return false;
   }
 
  private:
@@ -334,8 +299,6 @@ void RemoveVariationsHeaderIfNeeded(
     const net::RedirectInfo& redirect_info,
     const network::mojom::URLResponseHead& response_head,
     std::vector<std::string>* to_be_removed_headers) {
-  if (!ShouldAppendVariationsHeader(redirect_info.new_url, "Remove"))
-    to_be_removed_headers->push_back(kClientDataHeader);
 }
 
 std::unique_ptr<network::SimpleURLLoader>
@@ -371,29 +334,22 @@ CreateSimpleURLLoaderWithVariationsHeade
 }
 
 bool HasVariationsHeader(const network::ResourceRequest& request) {
-  std::string unused_header;
-  return GetVariationsHeader(request, &unused_header);
+  return false;
 }
 
 bool GetVariationsHeader(const network::ResourceRequest& request,
                          std::string* out) {
-  std::optional<std::string> header_value =
-      request.cors_exempt_headers.GetHeader(kClientDataHeader);
-  if (header_value) {
-    out->swap(header_value.value());
-  }
-  return header_value.has_value();
+  return false;
 }
 
 bool ShouldAppendVariationsHeaderForTesting(
     const GURL& url,
     const std::string& histogram_suffix) {
-  return ShouldAppendVariationsHeader(url, histogram_suffix);
+  return false;
 }
 
 void UpdateCorsExemptHeaderForVariations(
     network::mojom::NetworkContextParams* params) {
-  params->cors_exempt_header_list.push_back(kClientDataHeader);
 }
 
 }  // namespace variations
--- a/content/browser/preloading/prefetch/prefetch_container.cc
+++ b/content/browser/preloading/prefetch/prefetch_container.cc
@@ -949,7 +949,7 @@ void PrefetchContainer::AddRedirectHop(c
   // some which are added by throttles). These aren't yet supported for
   // prefetch, including browsing topics and client hints.
   net::HttpRequestHeaders updated_headers;
-  std::vector<std::string> headers_to_remove = {variations::kClientDataHeader};
+  std::vector<std::string> headers_to_remove;
   updated_headers.SetHeader(blink::kSecPurposeHeaderName,
                             GetSecPurposeHeaderValue(redirect_info.new_url));
 
--- a/net/base/url_util.cc
+++ b/net/base/url_util.cc
@@ -538,28 +538,6 @@ bool HasGoogleHost(const GURL& url) {
 }
 
 bool IsGoogleHost(std::string_view host) {
-  static const char* kGoogleHostSuffixes[] = {
-      ".google.com",
-      ".youtube.com",
-      ".gmail.com",
-      ".doubleclick.net",
-      ".gstatic.com",
-      ".googlevideo.com",
-      ".googleusercontent.com",
-      ".googlesyndication.com",
-      ".google-analytics.com",
-      ".googleadservices.com",
-      ".googleapis.com",
-      ".ytimg.com",
-  };
-  for (const char* suffix : kGoogleHostSuffixes) {
-    // Here it's possible to get away with faster case-sensitive comparisons
-    // because the list above is all lowercase, and a GURL's host name will
-    // always be canonicalized to lowercase as well.
-    if (host.ends_with(suffix)) {
-      return true;
-    }
-  }
   return false;
 }
 
