# Remove unused Safe Browsing and Sign-in fields from Preferences file
# TODO: This patch should probably be split up and merged into
# disable-signin.patch and fix-building-without-safebrowsing.patch

--- a/chrome/browser/ash/login/signin/device_id_browsertest.cc
+++ b/chrome/browser/ash/login/signin/device_id_browsertest.cc
@@ -32,7 +32,6 @@
 #include "chrome/test/base/in_process_browser_test.h"
 #include "chromeos/dbus/constants/dbus_paths.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/user_manager/known_user.h"
 #include "components/user_manager/user_manager.h"
 #include "content/public/test/browser_test.h"
@@ -301,15 +300,6 @@ IN_PROC_BROWSER_TEST_F(DeviceIDTest, PRE
   SignInOnline(FakeGaiaMixin::kFakeUserEmail, FakeGaiaMixin::kFakeUserPassword,
                kRefreshToken1, FakeGaiaMixin::kFakeUserGaiaId);
 
-  // Simulate user that has device ID saved only in preferences (pre-M44).
-  PrefService* prefs =
-      ProfileHelper::Get()
-          ->GetProfileByUser(user_manager::UserManager::Get()->GetActiveUser())
-          ->GetPrefs();
-  prefs->SetString(
-      prefs::kGoogleServicesSigninScopedDeviceId,
-      GetDeviceId(AccountId::FromUserEmail(FakeGaiaMixin::kFakeUserEmail)));
-
   // Can't use SetKnownUserDeviceId here, because it forbids changing a device
   // ID.
   user_manager::KnownUser known_user(g_browser_process->local_state());
@@ -338,13 +328,6 @@ IN_PROC_BROWSER_TEST_F(DeviceIDTest, PRE
   SignInOnline(FakeGaiaMixin::kFakeUserEmail, FakeGaiaMixin::kFakeUserPassword,
                kRefreshToken1, FakeGaiaMixin::kFakeUserGaiaId);
 
-  PrefService* prefs =
-      ProfileHelper::Get()
-          ->GetProfileByUser(user_manager::UserManager::Get()->GetActiveUser())
-          ->GetPrefs();
-  EXPECT_TRUE(
-      prefs->GetString(prefs::kGoogleServicesSigninScopedDeviceId).empty());
-
   // Can't use SetKnownUserDeviceId here, because it forbids changing a device
   // ID.
   user_manager::KnownUser known_user(g_browser_process->local_state());
--- a/chrome/browser/browsing_data/chrome_browsing_data_lifetime_manager.cc
+++ b/chrome/browser/browsing_data/chrome_browsing_data_lifetime_manager.cc
@@ -33,7 +33,6 @@
 #include "components/keep_alive_registry/keep_alive_types.h"
 #include "components/keep_alive_registry/scoped_keep_alive.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/sync/service/sync_service.h"
 #include "components/sync/service/sync_user_settings.h"
 #include "content/public/browser/browser_context.h"
@@ -440,40 +439,5 @@ bool ChromeBrowsingDataLifetimeManager::
     return sync_disabled;
   }
 
-#if !BUILDFLAG(IS_CHROMEOS)
-  // Allow clearing data if browser signin is disabled.
-  if (!profile_->GetPrefs()->GetBoolean(prefs::kSigninAllowed)) {
-    return true;
-  }
-  // If signin will be disabled on next startup, delay the browsing data
-  // clearing until then.
-  if (!profile_->GetPrefs()->GetBoolean(prefs::kSigninAllowedOnNextStartup)) {
-    profile_->GetPrefs()->SetBoolean(
-        browsing_data::prefs::kClearBrowsingDataOnExitDeletionPending, true);
-    return false;
-  }
-#endif
-
-  // Check that sync types have been disabled if neither sync nor browser sign
-  // in is disabled.
-  syncer::SyncService* sync_service =
-      SyncServiceFactory::GetForProfile(profile_);
-
-  // If the sync service is not available, data can be safely cleared as it is
-  // not synced.
-  if (!sync_service) {
-    return true;
-  }
-
-  for (syncer::UserSelectableType type : sync_types) {
-    if (!sync_service->GetUserSettings()->IsTypeManagedByPolicy(type)) {
-      return false;
-    } else if (sync_service->GetActiveDataTypes().HasAny(
-                   syncer::UserSelectableTypeToAllDataTypes(type))) {
-      // If the sync type is disabled by policy, but the sync service has not
-      // deactivated the type yet, then data can not be safely cleared yet.
-      return false;
-    }
-  }
   return true;
 }
--- a/chrome/browser/chrome_content_browser_client.cc
+++ b/chrome/browser/chrome_content_browser_client.cc
@@ -280,7 +280,6 @@
 #include "components/safe_browsing/core/browser/url_checker_delegate.h"
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/hashprefix_realtime/hash_realtime_utils.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/search_engines/template_url_service.h"
 #include "components/security_state/core/security_state.h"
 #include "components/services/on_device_translation/buildflags/buildflags.h"
--- a/chrome/browser/download/download_browsertest.cc
+++ b/chrome/browser/download/download_browsertest.cc
@@ -108,7 +108,6 @@
 #include "components/safe_browsing/content/common/file_type_policies_test_util.h"
 #include "components/safe_browsing/content/common/proto/download_file_types.pb.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_state/core/security_state.h"
 #include "components/services/quarantine/test_support.h"
 #include "content/public/browser/browser_task_traits.h"
--- a/chrome/browser/download/download_ui_model.cc
+++ b/chrome/browser/download/download_ui_model.cc
@@ -31,7 +31,6 @@
 #include "components/enterprise/common/proto/connectors.pb.h"
 #include "components/google/core/common/google_util.h"
 #include "components/safe_browsing/buildflags.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_referral_methods.h"
 #include "components/strings/grit/components_strings.h"
 #include "components/vector_icons/vector_icons.h"
--- a/chrome/browser/download/download_ui_safe_browsing_util.cc
+++ b/chrome/browser/download/download_ui_safe_browsing_util.cc
@@ -11,7 +11,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/buildflags.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 #if BUILDFLAG(SAFE_BROWSING_DOWNLOAD_PROTECTION)
 #include "chrome/browser/browser_process.h"
--- a/chrome/browser/enterprise/connectors/analysis/content_analysis_delegate.cc
+++ b/chrome/browser/enterprise/connectors/analysis/content_analysis_delegate.cc
@@ -58,7 +58,6 @@
 #include "components/safe_browsing/content/browser/web_ui/safe_browsing_ui.h"
 #include "components/safe_browsing/core/browser/realtime/url_lookup_service_base.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/sessions/content/session_tab_helper.h"
 #include "components/url_matcher/url_matcher.h"
 #include "content/public/browser/web_contents.h"
--- a/chrome/browser/enterprise/connectors/connectors_service.cc
+++ b/chrome/browser/enterprise/connectors/connectors_service.cc
@@ -330,8 +330,7 @@ std::string ConnectorsService::GetManage
 
   std::optional<policy::PolicyScope> scope = std::nullopt;
   for (const char* scope_pref :
-       {enterprise_connectors::kEnterpriseRealTimeUrlCheckScope,
-        AnalysisConnectorScopePref(AnalysisConnector::FILE_ATTACHED),
+       {AnalysisConnectorScopePref(AnalysisConnector::FILE_ATTACHED),
         AnalysisConnectorScopePref(AnalysisConnector::FILE_DOWNLOADED),
         AnalysisConnectorScopePref(AnalysisConnector::BULK_DATA_ENTRY),
         AnalysisConnectorScopePref(AnalysisConnector::PRINT),
--- a/chrome/browser/enterprise/connectors/connectors_service.h
+++ b/chrome/browser/enterprise/connectors/connectors_service.h
@@ -17,7 +17,6 @@
 #include "components/enterprise/connectors/core/connectors_service_base.h"
 #include "components/keyed_service/content/browser_context_keyed_service_factory.h"
 #include "components/keyed_service/core/keyed_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/browser_context.h"
 
 namespace base {
--- a/chrome/browser/enterprise/profile_management/profile_management_navigation_throttle.cc
+++ b/chrome/browser/enterprise/profile_management/profile_management_navigation_throttle.cc
@@ -26,7 +26,6 @@
 #include "chrome/common/chrome_switches.h"
 #include "components/account_id/account_id.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "content/public/browser/browser_context.h"
 #include "content/public/browser/navigation_handle.h"
 #include "content/public/browser/navigation_throttle.h"
@@ -318,12 +317,6 @@ void ProfileManagementNavigationThrottle
   std::optional<std::string> management_domain =
       GetDomainFromAttributeValue(domain);
   if (management_domain) {
-    auto* prefs =
-        Profile::FromBrowserContext(
-            navigation_handle()->GetWebContents()->GetBrowserContext())
-            ->GetPrefs();
-    prefs->SetString(prefs::kSigninInterceptionIDPCookiesUrl,
-                     navigation_handle()->GetURL().spec());
     PostNavigateTo(GURL(base::StringPrintf(kGoogleServiceLoginUrl,
                                            management_domain.value().c_str())));
     return;
@@ -345,11 +338,6 @@ void ProfileManagementNavigationThrottle
     PostNavigateTo(GURL(token_url_for_testing_));
     return;
   }
-  auto* prefs = Profile::FromBrowserContext(
-                    navigation_handle()->GetWebContents()->GetBrowserContext())
-                    ->GetPrefs();
-  prefs->SetString(prefs::kSigninInterceptionIDPCookiesUrl,
-                   navigation_handle()->GetURL().spec());
 
   auto* interceptor = ProfileTokenWebSigninInterceptorFactory::GetForProfile(
       Profile::FromBrowserContext(
--- a/chrome/browser/enterprise/signals/context_info_fetcher.cc
+++ b/chrome/browser/enterprise/signals/context_info_fetcher.cc
@@ -124,12 +124,6 @@ void ContextInfoFetcher::Fetch(ContextIn
       device_signals::GetChromeRemoteDesktopAppBlocked(
           PolicyBlocklistFactory::GetForBrowserContext(browser_context_));
 
-  Profile* profile = Profile::FromBrowserContext(browser_context_);
-  info.safe_browsing_protection_level =
-      device_signals::GetSafeBrowsingProtectionLevel(profile->GetPrefs());
-  info.password_protection_warning_trigger =
-      device_signals::GetPasswordProtectionWarningTrigger(profile->GetPrefs());
-  info.enterprise_profile_id = GetEnterpriseProfileId(profile);
 
 #if BUILDFLAG(IS_WIN)
   base::ThreadPool::CreateCOMSTATaskRunner({base::MayBlock()})
--- a/chrome/browser/enterprise/signals/context_info_fetcher.h
+++ b/chrome/browser/enterprise/signals/context_info_fetcher.h
@@ -15,7 +15,6 @@
 #include "components/device_signals/core/common/common_types.h"
 #include "components/enterprise/buildflags/buildflags.h"
 #include "components/enterprise/connectors/core/common.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 namespace content {
 class BrowserContext;
@@ -43,11 +42,8 @@ struct ContextInfo {
   std::vector<std::string> on_print_providers;
   std::vector<std::string> on_security_event_providers;
   std::string browser_version;
-  safe_browsing::SafeBrowsingState safe_browsing_protection_level;
   bool site_isolation_enabled;
   bool built_in_dns_client_enabled;
-  std::optional<safe_browsing::PasswordProtectionTrigger>
-      password_protection_warning_trigger;
   bool chrome_remote_desktop_app_blocked;
   device_signals::SettingValue os_firewall;
   std::vector<std::string> system_dns_servers;
--- a/chrome/browser/enterprise/signin/managed_profile_creation_controller.cc
+++ b/chrome/browser/enterprise/signin/managed_profile_creation_controller.cc
@@ -32,7 +32,6 @@
 #include "components/policy/core/browser/signin/user_cloud_signin_restriction_policy_fetcher.h"
 #include "components/policy/core/common/policy_utils.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/accounts_mutator.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 
@@ -140,55 +139,6 @@ void ManagedProfileCreationController::O
 }
 
 void ManagedProfileCreationController::FetchProfileSeparationPolicies() {
-  // We should not fetch the policies twice.
-  CHECK(!policies_received_);
-  CHECK(!account_level_signin_restriction_policy_fetcher_);
-
-  auto policy_fetch_callback = base::BindOnce(
-      &ManagedProfileCreationController::OnProfileSeparationPoliciesReceived,
-      weak_ptr_factory_.GetWeakPtr());
-
-  if (profile_separation_policies_for_testing_.has_value()) {
-    CHECK_IS_TEST();
-    policy::ProfileSeparationPolicies profile_separation_policies =
-        std::exchange(profile_separation_policies_for_testing_, std::nullopt)
-            .value();
-    std::move(policy_fetch_callback)
-        .Run(std::move(profile_separation_policies));
-    return;
-  }
-
-  // If we cannot make network calls, we will not be able to fetch the
-  // account level policies.
-  if (!g_browser_process->system_network_context_manager()) {
-    std::move(policy_fetch_callback).Run(policy::ProfileSeparationPolicies());
-    return;
-  }
-
-  CHECK(source_profile_);
-  account_level_signin_restriction_policy_fetcher_ =
-      std::make_unique<policy::UserCloudSigninRestrictionPolicyFetcher>(
-          g_browser_process->browser_policy_connector(),
-          g_browser_process->system_network_context_manager()
-              ->GetSharedURLLoaderFactory());
-  account_level_signin_restriction_policy_fetcher_
-      ->GetManagedAccountsSigninRestriction(
-          GetIdentityManager(), account_info_.account_id,
-          std::move(policy_fetch_callback),
-          policy::utils::IsPolicyTestingEnabled(source_profile_->GetPrefs(),
-                                                chrome::GetChannel())
-              ? source_profile_->GetPrefs()
-                    ->GetDefaultPrefValue(
-                        prefs::kUserCloudSigninPolicyResponseFromPolicyTestPage)
-                    ->GetString()
-              : std::string());
-
-  policy_fetch_timeout_.Start(
-      FROM_HERE, base::Seconds(5),
-      base::BindOnce(&ManagedProfileCreationController::
-                         OnProfileSeparationPoliciesReceived,
-                     weak_ptr_factory_.GetWeakPtr(),
-                     policy::ProfileSeparationPolicies()));
 }
 
 void ManagedProfileCreationController::OnProfileSeparationPoliciesReceived(
--- a/chrome/browser/enterprise/signin/oidc_authentication_signin_interceptor.cc
+++ b/chrome/browser/enterprise/signin/oidc_authentication_signin_interceptor.cc
@@ -49,7 +49,6 @@
 #include "components/policy/core/common/cloud/user_cloud_policy_manager.h"
 #include "components/policy/core/common/policy_logger.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/primary_account_mutator.h"
 #include "content/public/browser/storage_partition.h"
 #include "content/public/browser/web_contents.h"
@@ -436,8 +435,7 @@ void OidcAuthenticationSigninInterceptor
 
   // TODO(b/*********): The interaction between OIDC profiles and BrowserSignin
   // policy should be finalized, this check only prevents Chrome from crashing.
-  if (dasher_based_ &&
-      !profile_->GetPrefs()->GetBoolean(prefs::kSigninAllowedOnNextStartup)) {
+  if (dasher_based_) {
     LOG_POLICY(ERROR, OIDC_ENROLLMENT)
         << "Google-synced OIDC profile can't be created because browser sign"
            "in is disabled.";
--- a/chrome/browser/enterprise/signin/oidc_managed_profile_creation_delegate.cc
+++ b/chrome/browser/enterprise/signin/oidc_managed_profile_creation_delegate.cc
@@ -9,7 +9,6 @@
 #include "chrome/browser/enterprise/signin/enterprise_signin_prefs.h"
 #include "chrome/browser/profiles/profile_attributes_storage.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 
 OidcManagedProfileCreationDelegate::OidcManagedProfileCreationDelegate() =
     default;
@@ -55,8 +54,6 @@ void OidcManagedProfileCreationDelegate:
 
 void OidcManagedProfileCreationDelegate::CheckManagedProfileStatus(
     Profile* new_profile) {
-  CHECK_EQ(new_profile->GetPrefs()->GetBoolean(prefs::kSigninAllowed),
-           dasher_based_);
 }
 
 void OidcManagedProfileCreationDelegate::OnManagedProfileInitialized(
--- a/chrome/browser/enterprise/signin/profile_management_disclaimer_service.cc
+++ b/chrome/browser/enterprise/signin/profile_management_disclaimer_service.cc
@@ -39,7 +39,6 @@
 #include "components/policy/core/browser/signin/user_cloud_signin_restriction_policy_fetcher.h"
 #include "components/policy/core/common/policy_utils.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
--- a/chrome/browser/enterprise/signin/token_managed_profile_creation_delegate.cc
+++ b/chrome/browser/enterprise/signin/token_managed_profile_creation_delegate.cc
@@ -7,7 +7,6 @@
 #include "chrome/browser/profiles/profile_attributes_storage.h"
 #include "chrome/browser/signin/signin_util.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 
 TokenManagedProfileCreationDelegate::TokenManagedProfileCreationDelegate() =
     default;
@@ -29,7 +28,6 @@ void TokenManagedProfileCreationDelegate
 
 void TokenManagedProfileCreationDelegate::CheckManagedProfileStatus(
     Profile* new_profile) {
-  DCHECK(!new_profile->GetPrefs()->GetBoolean(prefs::kSigninAllowed));
 }
 
 void TokenManagedProfileCreationDelegate::OnManagedProfileInitialized(
--- a/chrome/browser/extensions/api/enterprise_reporting_private/enterprise_reporting_private_api.cc
+++ b/chrome/browser/extensions/api/enterprise_reporting_private/enterprise_reporting_private_api.cc
@@ -105,44 +105,10 @@ api::enterprise_reporting_private::Conte
   info.built_in_dns_client_enabled = signals.built_in_dns_client_enabled;
   info.enterprise_profile_id = signals.enterprise_profile_id;
 
-  switch (signals.safe_browsing_protection_level) {
-    case safe_browsing::SafeBrowsingState::NO_SAFE_BROWSING:
       info.safe_browsing_protection_level = extensions::api::
           enterprise_reporting_private::SafeBrowsingLevel::kDisabled;
-      break;
-    case safe_browsing::SafeBrowsingState::STANDARD_PROTECTION:
-      info.safe_browsing_protection_level = extensions::api::
-          enterprise_reporting_private::SafeBrowsingLevel::kStandard;
-      break;
-    case safe_browsing::SafeBrowsingState::ENHANCED_PROTECTION:
-      info.safe_browsing_protection_level = extensions::api::
-          enterprise_reporting_private::SafeBrowsingLevel::kEnhanced;
-      break;
-  }
-  if (!signals.password_protection_warning_trigger.has_value()) {
     info.password_protection_warning_trigger = extensions::api::
         enterprise_reporting_private::PasswordProtectionTrigger::kPolicyUnset;
-  } else {
-    switch (signals.password_protection_warning_trigger.value()) {
-      case safe_browsing::PASSWORD_PROTECTION_OFF:
-        info.password_protection_warning_trigger =
-            extensions::api::enterprise_reporting_private::
-                PasswordProtectionTrigger::kPasswordProtectionOff;
-        break;
-      case safe_browsing::PASSWORD_REUSE:
-        info.password_protection_warning_trigger =
-            extensions::api::enterprise_reporting_private::
-                PasswordProtectionTrigger::kPasswordReuse;
-        break;
-      case safe_browsing::PHISHING_REUSE:
-        info.password_protection_warning_trigger =
-            extensions::api::enterprise_reporting_private::
-                PasswordProtectionTrigger::kPhishingReuse;
-        break;
-      case safe_browsing::PASSWORD_PROTECTION_TRIGGER_MAX:
-        NOTREACHED();
-    }
-  }
 
   return info;
 }
--- a/chrome/browser/extensions/api/identity/identity_apitest.cc
+++ b/chrome/browser/extensions/api/identity/identity_apitest.cc
@@ -74,7 +74,6 @@
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/list_accounts_test_utils.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/accounts_mutator.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
--- a/chrome/browser/extensions/api/identity/identity_get_auth_token_function.cc
+++ b/chrome/browser/extensions/api/identity/identity_get_auth_token_function.cc
@@ -30,7 +30,6 @@
 #include "chrome/common/extensions/api/identity.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/access_token_info.h"
 #include "components/signin/public/identity_manager/account_info.h"
@@ -73,7 +72,7 @@ const char* const kExtensionsIdentityAPI
     "extensions_identity_api";
 
 bool IsBrowserSigninAllowed(Profile* profile) {
-  return profile->GetPrefs()->GetBoolean(prefs::kSigninAllowed);
+  return false;
 }
 
 std::string_view GetOAuth2MintTokenFlowVersion() {
--- a/chrome/browser/extensions/api/preference/preference_api.cc
+++ b/chrome/browser/extensions/api/preference/preference_api.cc
@@ -25,7 +25,6 @@
 #include "components/content_settings/core/common/pref_names.h"
 #include "components/prefs/pref_service.h"
 #include "components/privacy_sandbox/privacy_sandbox_features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "extensions/browser/api/content_settings/content_settings_service.h"
 #include "extensions/browser/extension_function_registry.h"
 #include "extensions/browser/extension_pref_value_map.h"
@@ -449,17 +448,6 @@ ExtensionFunction::ResponseAction SetPre
         base::Value(browser_pref_value->GetBool()));
   }
 
-  // Whenever an extension takes control of the |kSafeBrowsingEnabled|
-  // preference, it must also set |kSafeBrowsingEnhanced| to false.
-  // See crbug.com/1064722 for more background.
-  //
-  // TODO(crbug.com/40681445): Consider extending
-  // chrome.privacy.services.safeBrowsingEnabled to a three-state enum.
-  if (prefs::kSafeBrowsingEnabled == browser_pref) {
-    prefs_helper->SetExtensionControlledPref(extension_id(),
-                                             prefs::kSafeBrowsingEnhanced,
-                                             scope, base::Value(false));
-  }
 
   prefs_helper->SetExtensionControlledPref(extension_id(), browser_pref, scope,
                                            browser_pref_value->Clone());
@@ -509,16 +497,6 @@ ExtensionFunction::ResponseAction ClearP
   prefs_helper->RemoveExtensionControlledPref(extension_id(), browser_pref,
                                               scope);
 
-  // Whenever an extension clears the |kSafeBrowsingEnabled| preference,
-  // it must also clear |kSafeBrowsingEnhanced|. See crbug.com/1064722 for
-  // more background.
-  //
-  // TODO(crbug.com/40681445): Consider extending
-  // chrome.privacy.services.safeBrowsingEnabled to a three-state enum.
-  if (prefs::kSafeBrowsingEnabled == browser_pref) {
-    prefs_helper->RemoveExtensionControlledPref(
-        extension_id(), prefs::kSafeBrowsingEnhanced, scope);
-  }
   return RespondNow(NoArguments());
 }
 
--- a/chrome/browser/extensions/api/preference/preference_apitest.cc
+++ b/chrome/browser/extensions/api/preference/preference_apitest.cc
@@ -31,7 +31,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/privacy_sandbox/privacy_sandbox_features.h"
 #include "components/privacy_sandbox/privacy_sandbox_prefs.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/translate/core/browser/translate_pref_names.h"
 #include "content/public/test/browser_test.h"
 #include "content/public/test/test_devtools_protocol_client.h"
@@ -91,7 +90,6 @@ class ExtensionPreferenceApiTest
               prefs->GetInteger(prefetch::prefs::kNetworkPredictionOptions));
     EXPECT_TRUE(
         prefs->GetBoolean(password_manager::prefs::kCredentialsEnableService));
-    EXPECT_TRUE(prefs->GetBoolean(prefs::kSafeBrowsingEnabled));
     EXPECT_TRUE(prefs->GetBoolean(prefs::kSearchSuggestEnabled));
     VerifyPrefValueAndControlledState(prefs::kPrivacySandboxM1TopicsEnabled,
                                       base::Value(false),
@@ -129,7 +127,6 @@ class ExtensionPreferenceApiTest
               prefs->GetInteger(prefetch::prefs::kNetworkPredictionOptions));
     EXPECT_FALSE(
         prefs->GetBoolean(password_manager::prefs::kCredentialsEnableService));
-    EXPECT_FALSE(prefs->GetBoolean(prefs::kSafeBrowsingEnabled));
     EXPECT_FALSE(prefs->GetBoolean(prefs::kSearchSuggestEnabled));
     VerifyPrefValueAndControlledState(prefs::kPrivacySandboxM1TopicsEnabled,
                                       base::Value(true),
@@ -225,7 +222,6 @@ IN_PROC_BROWSER_TEST_P(ExtensionPreferen
       prefetch::prefs::kNetworkPredictionOptions,
       static_cast<int>(prefetch::NetworkPredictionOptions::kDisabled));
   prefs->SetBoolean(password_manager::prefs::kCredentialsEnableService, false);
-  prefs->SetBoolean(prefs::kSafeBrowsingEnabled, false);
   prefs->SetBoolean(prefs::kSearchSuggestEnabled, false);
   prefs->SetString(prefs::kWebRTCIPHandlingPolicy,
                    blink::kWebRTCIPHandlingDefaultPublicInterfaceOnly);
--- a/chrome/browser/extensions/api/safe_browsing_private/safe_browsing_private_event_router.cc
+++ b/chrome/browser/extensions/api/safe_browsing_private/safe_browsing_private_event_router.cc
@@ -26,7 +26,6 @@
 #include "components/enterprise/connectors/core/reporting_service_settings.h"
 #include "components/enterprise/connectors/core/reporting_utils.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/url_matcher/url_matcher.h"
 #include "components/url_matcher/url_util.h"
 #include "content/public/browser/browser_context.h"
--- a/chrome/browser/extensions/api/safe_browsing_private/safe_browsing_private_event_router_unittest.cc
+++ b/chrome/browser/extensions/api/safe_browsing_private/safe_browsing_private_event_router_unittest.cc
@@ -47,7 +47,6 @@
 #include "components/policy/core/common/cloud/mock_cloud_policy_client.h"
 #include "components/policy/core/common/cloud/realtime_reporting_job_configuration.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/signin/public/identity_manager/identity_test_environment.h"
 #include "content/public/test/browser_task_environment.h"
 #include "extensions/browser/test_event_router.h"
--- a/chrome/browser/extensions/api/settings_private/prefs_util.cc
+++ b/chrome/browser/extensions/api/settings_private/prefs_util.cc
@@ -59,10 +59,8 @@
 #include "components/privacy_sandbox/privacy_sandbox_prefs.h"
 #include "components/privacy_sandbox/tracking_protection_prefs.h"
 #include "components/proxy_config/proxy_config_pref_names.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/saved_tab_groups/public/pref_names.h"
 #include "components/search_engines/default_search_manager.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/spellcheck/browser/pref_names.h"
 #include "components/supervised_user/core/common/pref_names.h"
 #include "components/translate/core/browser/translate_pref_names.h"
@@ -339,8 +337,6 @@ const PrefsUtil::TypedPrefMap& PrefsUtil
       settings_api::PrefType::kNumber;
 
   // Privacy page
-  (*s_allowlist)[::prefs::kSigninAllowedOnNextStartup] =
-      settings_api::PrefType::kBoolean;
   (*s_allowlist)[::prefs::kDnsOverHttpsMode] = settings_api::PrefType::kString;
   (*s_allowlist)[::prefs::kDnsOverHttpsTemplates] =
       settings_api::PrefType::kString;
@@ -363,16 +359,6 @@ const PrefsUtil::TypedPrefMap& PrefsUtil
       settings_api::PrefType::kBoolean;
 
   // Security page
-  (*s_allowlist)[::kGeneratedPasswordLeakDetectionPref] =
-      settings_api::PrefType::kBoolean;
-  (*s_allowlist)[::prefs::kSafeBrowsingEnabled] =
-      settings_api::PrefType::kBoolean;
-  (*s_allowlist)[::prefs::kSafeBrowsingEnhanced] =
-      settings_api::PrefType::kBoolean;
-  (*s_allowlist)[::prefs::kSafeBrowsingScoutReportingEnabled] =
-      settings_api::PrefType::kBoolean;
-  (*s_allowlist)[::safe_browsing::kGeneratedSafeBrowsingPref] =
-      settings_api::PrefType::kNumber;
   (*s_allowlist)[::prefs::kHttpsOnlyModeEnabled] =
       settings_api::PrefType::kBoolean;
   (*s_allowlist)[::kGeneratedHttpsFirstModePref] =
--- a/chrome/browser/extensions/api/webstore_private/webstore_private_apitest.cc
+++ b/chrome/browser/extensions/api/webstore_private/webstore_private_apitest.cc
@@ -726,9 +726,6 @@ IN_PROC_BROWSER_TEST_F(ExtensionWebstore
 IN_PROC_BROWSER_TEST_F(ExtensionWebstorePrivateGetReferrerChainApiTest,
                        GetReferrerChainForNonSafeBrowsingUser) {
   PrefService* pref_service = profile()->GetPrefs();
-  EXPECT_TRUE(pref_service->GetBoolean(prefs::kSafeBrowsingEnabled));
-  // Disable SafeBrowsing.
-  pref_service->SetBoolean(prefs::kSafeBrowsingEnabled, false);
 
   GURL page_url = GetTestServerURLWithReferrers("empty_referrer_chain.html");
   ASSERT_TRUE(OpenTestURL(page_url));
--- a/chrome/browser/extensions/extension_allowlist.cc
+++ b/chrome/browser/extensions/extension_allowlist.cc
@@ -11,7 +11,6 @@
 #include "chrome/browser/profiles/profile.h"
 #include "chrome/browser/safe_browsing/safe_browsing_metrics_collector_factory.h"
 #include "components/safe_browsing/core/browser/safe_browsing_metrics_collector.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "extensions/browser/allowlist_state.h"
 #include "extensions/browser/extension_registrar.h"
 #include "extensions/browser/extension_registry.h"
@@ -74,10 +73,6 @@ ExtensionAllowlist::ExtensionAllowlist(P
   // Register to Enhanced Safe Browsing setting changes for allowlist
   // enforcements.
   pref_change_registrar_.Init(profile_->GetPrefs());
-  pref_change_registrar_.Add(
-      prefs::kSafeBrowsingEnhanced,
-      base::BindRepeating(&ExtensionAllowlist::OnSafeBrowsingEnhancedChanged,
-                          base::Unretained(this)));
 }
 
 ExtensionAllowlist::~ExtensionAllowlist() = default;
@@ -247,15 +242,8 @@ void ExtensionAllowlist::OnExtensionInst
 }
 
 void ExtensionAllowlist::SetAllowlistEnforcementFields() {
-  if (safe_browsing::IsEnhancedProtectionEnabled(*profile_->GetPrefs())) {
-    warnings_enabled_ = base::FeatureList::IsEnabled(
-        extensions_features::kSafeBrowsingCrxAllowlistShowWarnings);
-    should_auto_disable_extensions_ = base::FeatureList::IsEnabled(
-        extensions_features::kSafeBrowsingCrxAllowlistAutoDisable);
-  } else {
     warnings_enabled_ = false;
     should_auto_disable_extensions_ = false;
-  }
 }
 
 // `ApplyEnforcement` can be called when an extension becomes not allowlisted or
@@ -406,15 +394,6 @@ void ExtensionAllowlist::NotifyExtension
 }
 
 void ExtensionAllowlist::ReportExtensionReEnabledEvent() {
-  auto* metrics_collector =
-      safe_browsing::SafeBrowsingMetricsCollectorFactory::GetForProfile(
-          profile_);
-  DCHECK(metrics_collector);
-  if (metrics_collector) {
-    metrics_collector->AddSafeBrowsingEventToPref(
-        safe_browsing::SafeBrowsingMetricsCollector::EventType::
-            NON_ALLOWLISTED_EXTENSION_RE_ENABLED);
-  }
 }
 
 }  // namespace extensions
--- a/chrome/browser/extensions/extension_service.cc
+++ b/chrome/browser/extensions/extension_service.cc
@@ -73,7 +73,6 @@
 #include "chrome/common/url_constants.h"
 #include "components/crx_file/id_util.h"
 #include "components/policy/core/common/policy_pref_names.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/supervised_user/core/browser/supervised_user_preferences.h"
 #include "content/public/browser/browser_thread.h"
 #include "content/public/browser/render_process_host.h"
--- a/chrome/browser/extensions/installed_loader.cc
+++ b/chrome/browser/extensions/installed_loader.cc
@@ -35,7 +35,6 @@
 #include "chrome/common/extensions/manifest_handlers/settings_overrides_handler.h"
 #include "chrome/common/pref_names.h"
 #include "chrome/common/webui_url_constants.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/browser_thread.h"
 #include "content/public/common/url_constants.h"
 #include "extensions/browser/allowlist_state.h"
@@ -986,12 +985,6 @@ void InstalledLoader::RecordExtensionsMe
                               enabled_not_allowlisted_count);
   base::UmaHistogramCounts100("Extensions.NotAllowlistedDisabled2",
                               disabled_not_allowlisted_count);
-  if (safe_browsing::IsEnhancedProtectionEnabled(*profile->GetPrefs())) {
-    base::UmaHistogramCounts100("Extensions.NotAllowlistedEnabledAndEsbUser2",
-                                enabled_not_allowlisted_count);
-    base::UmaHistogramCounts100("Extensions.NotAllowlistedDisabledAndEsbUser2",
-                                disabled_not_allowlisted_count);
-  }
 }
 
 int InstalledLoader::GetCreationFlags(const ExtensionInfo* info) {
--- a/chrome/browser/extensions/pref_mapping.cc
+++ b/chrome/browser/extensions/pref_mapping.cc
@@ -21,7 +21,6 @@
 #include "components/privacy_sandbox/privacy_sandbox_prefs.h"
 #include "components/privacy_sandbox/tracking_protection_prefs.h"
 #include "components/proxy_config/proxy_config_pref_names.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/spellcheck/browser/pref_names.h"
 #include "components/translate/core/browser/translate_pref_names.h"
 
@@ -64,11 +63,6 @@ const PrefMappingEntry kMappings[] = {
      APIPermissionID::kPrivacy},
     {"doNotTrackEnabled", prefs::kEnableDoNotTrack, APIPermissionID::kPrivacy,
      APIPermissionID::kPrivacy},
-    {"safeBrowsingEnabled", prefs::kSafeBrowsingEnabled,
-     APIPermissionID::kPrivacy, APIPermissionID::kPrivacy},
-    {"safeBrowsingExtendedReportingEnabled",
-     prefs::kSafeBrowsingScoutReportingEnabled, APIPermissionID::kPrivacy,
-     APIPermissionID::kPrivacy},
     {"searchSuggestEnabled", prefs::kSearchSuggestEnabled,
      APIPermissionID::kPrivacy, APIPermissionID::kPrivacy},
     {"spellingServiceEnabled", spellcheck::prefs::kSpellCheckUseSpellingService,
--- a/chrome/browser/net/profile_network_context_service.cc
+++ b/chrome/browser/net/profile_network_context_service.cc
@@ -73,7 +73,6 @@
 #include "components/prefs/pref_registry_simple.h"
 #include "components/prefs/pref_service.h"
 #include "components/privacy_sandbox/privacy_sandbox_prefs.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/browser_context.h"
 #include "content/public/browser/browser_thread.h"
 #include "content/public/browser/first_party_sets_handler.h"
@@ -1439,15 +1438,8 @@ void ProfileNetworkContextService::Confi
 
   network_context_params->enable_certificate_reporting = true;
 
-  SCTReportingService* sct_reporting_service =
-      SCTReportingServiceFactory::GetForBrowserContext(profile_);
-  if (sct_reporting_service) {
-    network_context_params->sct_auditing_mode =
-        sct_reporting_service->GetReportingMode();
-  } else {
     network_context_params->sct_auditing_mode =
         network::mojom::SCTAuditingMode::kDisabled;
-  }
 
   network_context_params->ct_policy = GetCTPolicy();
   cert_verifier_creation_params->ct_policy = GetCTPolicy();
--- a/chrome/browser/notifications/platform_notification_service_impl.cc
+++ b/chrome/browser/notifications/platform_notification_service_impl.cc
@@ -44,7 +44,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/buildflags.h"
 #include "components/safe_browsing/content/browser/notification_content_detection/notification_content_detection_constants.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/browser_thread.h"
 #include "content/public/browser/platform_notification_context.h"
 #include "content/public/browser/storage_partition.h"
@@ -803,25 +802,7 @@ void PlatformNotificationServiceImpl::Lo
 
 bool PlatformNotificationServiceImpl::
     AreSuspiciousNotificationsAllowlistedByUser(const GURL& origin) {
-  auto* hcsm = HostContentSettingsMapFactory::GetForProfile(profile_);
-  if (!hcsm || !origin.is_valid()) {
     return false;
-  }
-  content_settings::SettingInfo info;
-  base::Value stored_value(hcsm->GetWebsiteSetting(
-      origin, origin,
-      ContentSettingsType::ARE_SUSPICIOUS_NOTIFICATIONS_ALLOWLISTED_BY_USER,
-      &info));
-  if (stored_value.is_none()) {
-    return false;
-  }
-  if (!stored_value.is_dict() || !stored_value.GetDict().contains(
-                                     safe_browsing::kIsAllowlistedByUserKey)) {
-    return false;
-  }
-  return stored_value.GetDict()
-      .FindBool(safe_browsing::kIsAllowlistedByUserKey)
-      .value_or(false);
 }
 
 void PlatformNotificationServiceImpl::DidUpdatePersistentMetadata(
--- a/chrome/browser/password_manager/generated_password_leak_detection_pref.cc
+++ b/chrome/browser/password_manager/generated_password_leak_detection_pref.cc
@@ -15,7 +15,6 @@
 #include "components/password_manager/core/common/password_manager_pref_names.h"
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 
 namespace {
@@ -42,16 +41,6 @@ GeneratedPasswordLeakDetectionPref::Gene
       base::BindRepeating(
           &GeneratedPasswordLeakDetectionPref::OnSourcePreferencesChanged,
           base::Unretained(this)));
-  user_prefs_registrar_.Add(
-      prefs::kSafeBrowsingEnabled,
-      base::BindRepeating(
-          &GeneratedPasswordLeakDetectionPref::OnSourcePreferencesChanged,
-          base::Unretained(this)));
-  user_prefs_registrar_.Add(
-      prefs::kSafeBrowsingEnhanced,
-      base::BindRepeating(
-          &GeneratedPasswordLeakDetectionPref::OnSourcePreferencesChanged,
-          base::Unretained(this)));
 
   if (auto* identity_manager = IdentityManagerFactory::GetForProfile(profile)) {
     identity_manager_observer_.Observe(identity_manager);
--- a/chrome/browser/permissions/permission_revocation_request.cc
+++ b/chrome/browser/permissions/permission_revocation_request.cc
@@ -18,7 +18,6 @@
 #include "components/permissions/permissions_client.h"
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/core/browser/db/database_manager.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 #if BUILDFLAG(SAFE_BROWSING_AVAILABLE)
 #include "chrome/browser/safe_browsing/safe_browsing_service.h"
--- a/chrome/browser/policy/browser_signin_policy_handler.cc
+++ b/chrome/browser/policy/browser_signin_policy_handler.cc
@@ -15,7 +15,6 @@
 #include "components/policy/core/common/policy_map.h"
 #include "components/policy/policy_constants.h"
 #include "components/prefs/pref_value_map.h"
-#include "components/signin/public/base/signin_pref_names.h"
 
 namespace policy {
 
@@ -30,50 +29,6 @@ BrowserSigninPolicyHandler::~BrowserSign
 
 void BrowserSigninPolicyHandler::ApplyPolicySettings(const PolicyMap& policies,
                                                      PrefValueMap* prefs) {
-#if BUILDFLAG(IS_WIN)
-  // Browser sign in policies shouldn't be enforced on gcpw signin
-  // mode as gcpw is invoked in windows login UI screen.
-  // Also note that GCPW launches chrome in incognito mode using a
-  // special user's logon_token. So the end user won't have access
-  // to this session after user logs in via GCPW.
-  if (base::CommandLine::ForCurrentProcess()->HasSwitch(
-          ::credential_provider::kGcpwSigninSwitch))
-    return;
-#endif
-
-  const base::Value* value =
-      policies.GetValue(policy_name(), base::Value::Type::INTEGER);
-  switch (static_cast<BrowserSigninMode>(value->GetInt())) {
-    case BrowserSigninMode::kForced:
-#if !BUILDFLAG(IS_LINUX) && !BUILDFLAG(IS_CHROMEOS)
-      prefs->SetValue(prefs::kForceBrowserSignin, base::Value(true));
-#endif
-      [[fallthrough]];
-    case BrowserSigninMode::kEnabled:
-      prefs->SetValue(
-#if BUILDFLAG(IS_ANDROID)
-          // The new kSigninAllowedOnNextStartup pref is only used on Desktop.
-          // Keep the old kSigninAllowed pref for Android until the policy is
-          // fully deprecated in M71 and can be removed.
-          prefs::kSigninAllowed,
-#else
-          prefs::kSigninAllowedOnNextStartup,
-#endif
-          base::Value(true));
-      break;
-    case BrowserSigninMode::kDisabled:
-      prefs->SetValue(
-#if BUILDFLAG(IS_ANDROID)
-          // The new kSigninAllowedOnNextStartup pref is only used on Desktop.
-          // Keep the old kSigninAllowed pref for Android until the policy is
-          // fully deprecated in M71 and can be removed.
-          prefs::kSigninAllowed,
-#else
-          prefs::kSigninAllowedOnNextStartup,
-#endif
-          base::Value(false));
-      break;
-  }
 }
 
 }  // namespace policy
--- a/chrome/browser/policy/configuration_policy_handler_list_factory.cc
+++ b/chrome/browser/policy/configuration_policy_handler_list_factory.cc
@@ -115,8 +115,6 @@
 #include "components/privacy_sandbox/tracking_protection_prefs.h"
 #include "components/proxy_config/proxy_policy_handler.h"
 #include "components/safe_browsing/buildflags.h"
-#include "components/safe_browsing/core/common/safe_browsing_policy_handler.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/search_engines/enterprise/default_search_policy_handler.h"
 #include "components/search_engines/search_engines_pref_names.h"
 #include "components/security_interstitials/core/https_only_mode_policy_handler.h"
@@ -125,7 +123,6 @@
 #include "components/sharing_message/buildflags.h"
 #include "components/sharing_message/pref_names.h"
 #include "components/signin/public/base/signin_buildflags.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/spellcheck/spellcheck_buildflags.h"
 #include "components/sync/base/pref_names.h"
 #include "components/sync/service/sync_policy_handler.h"
@@ -290,9 +287,6 @@ const PolicyToPreferenceMapEntry kSimple
   { key::kForcePermissionPolicyUnloadDefaultEnabled,
     policy_prefs::kForcePermissionPolicyUnloadDefaultEnabled,
     base::Value::Type::BOOLEAN},
-  { key::kDisableSafeBrowsingProceedAnyway,
-    prefs::kSafeBrowsingProceedAnywayDisabled,
-    base::Value::Type::BOOLEAN },
   { key::kDomainReliabilityAllowed,
     domain_reliability::prefs::kDomainReliabilityAllowedByPolicy,
     base::Value::Type::BOOLEAN },
@@ -326,15 +320,6 @@ const PolicyToPreferenceMapEntry kSimple
     prefs::kOopPrintDriversAllowedByPolicy,
     base::Value::Type::BOOLEAN },
 #endif
-  { key::kSafeBrowsingAllowlistDomains,
-    prefs::kSafeBrowsingAllowlistDomains,
-    base::Value::Type::LIST },
-  { key::kSafeBrowsingEnabled,
-    prefs::kSafeBrowsingEnabled,
-    base::Value::Type::BOOLEAN },
-  { key::kSafeBrowsingProxiedRealTimeChecksAllowed,
-    prefs::kHashPrefixRealTimeChecksAllowedByPolicy,
-    base::Value::Type::BOOLEAN },
   { key::kSavingBrowserHistoryDisabled,
     prefs::kSavingBrowserHistoryDisabled,
     base::Value::Type::BOOLEAN },
@@ -473,9 +458,6 @@ const PolicyToPreferenceMapEntry kSimple
   { key::kAdsSettingForIntrusiveAdsSites,
     prefs::kManagedDefaultAdsSetting,
     base::Value::Type::INTEGER },
-  { key::kAdvancedProtectionAllowed,
-    prefs::kAdvancedProtectionAllowed,
-    base::Value::Type::BOOLEAN },
   { key::kAllowCrossOriginAuthPrompt,
     prefs::kAllowCrossOriginAuthPrompt,
     base::Value::Type::BOOLEAN },
@@ -721,15 +703,6 @@ const PolicyToPreferenceMapEntry kSimple
   { key::kPasswordDismissCompromisedAlertEnabled,
     password_manager::prefs::kPasswordDismissCompromisedAlertEnabled,
     base::Value::Type::BOOLEAN },
-  { key::kPasswordProtectionChangePasswordURL,
-    prefs::kPasswordProtectionChangePasswordURL,
-    base::Value::Type::STRING },
-  { key::kPasswordProtectionLoginURLs,
-    prefs::kPasswordProtectionLoginURLs,
-    base::Value::Type::LIST },
-  { key::kPasswordProtectionWarningTrigger,
-    prefs::kPasswordProtectionWarningTrigger,
-    base::Value::Type::INTEGER },
 #if BUILDFLAG(ENABLE_PDF)
   { key::kPdfLocalFileAccessAllowedForDomains,
     prefs::kPdfLocalFileAccessAllowedForDomains,
@@ -895,9 +868,6 @@ const PolicyToPreferenceMapEntry kSimple
   { key::kAdditionalDnsQueryTypesEnabled,
     prefs::kAdditionalDnsQueryTypesEnabled,
     base::Value::Type::BOOLEAN },
-  { key::kSafeBrowsingExtendedReportingEnabled,
-    prefs::kSafeBrowsingScoutReportingEnabled,
-    base::Value::Type::BOOLEAN },
   { key::kForceGoogleSafeSearch,
     policy_prefs::kForceGoogleSafeSearch,
     base::Value::Type::BOOLEAN },
@@ -961,9 +931,6 @@ const PolicyToPreferenceMapEntry kSimple
   { key::kRequireOnlineRevocationChecksForLocalAnchors,
     prefs::kCertRevocationCheckingRequiredLocalAnchors,
     base::Value::Type::BOOLEAN },
-  { key::kSafeBrowsingSurveysEnabled,
-    prefs::kSafeBrowsingSurveysEnabled,
-    base::Value::Type::BOOLEAN },
   { key::kPasswordManagerBlocklist,
     policy_prefs::kPasswordManagerBlocklist,
     base::Value::Type::LIST },
@@ -1933,9 +1900,6 @@ const PolicyToPreferenceMapEntry kSimple
   { key::kUnmanagedDeviceSignalsConsentFlowEnabled,
     device_signals::prefs::kUnmanagedDeviceSignalsConsentFlowEnabled,
     base::Value::Type::BOOLEAN },
-  { key::kProfileSeparationDomainExceptionList,
-    prefs::kProfileSeparationDomainExceptionList,
-    base::Value::Type::LIST },
   { key::kLiveCaptionEnabled,
     prefs::kLiveCaptionEnabled,
     base::Value::Type::BOOLEAN },
@@ -2003,9 +1967,6 @@ const PolicyToPreferenceMapEntry kSimple
     prefs::kImportDialogAutofillFormData,
     base::Value::Type::BOOLEAN },
 
-  { key::kRestrictSigninToPattern,
-    prefs::kGoogleServicesUsernamePattern,
-    base::Value::Type::STRING },
   { key::kHardwareAccelerationModeEnabled,
     prefs::kHardwareAccelerationModeEnabled,
     base::Value::Type::BOOLEAN },
@@ -2282,9 +2243,6 @@ const PolicyToPreferenceMapEntry kSimple
   { key::kGoogleSearchSidePanelEnabled,
     prefs::kGoogleSearchSidePanelEnabled,
     base::Value::Type::BOOLEAN },
-  { key::kSafeBrowsingDeepScanningEnabled,
-    prefs::kSafeBrowsingDeepScanningEnabled,
-    base::Value::Type::BOOLEAN },
 #endif  // BUILDFLAG(IS_ANDROID)
   { key::kAllowBackForwardCacheForCacheControlNoStorePageEnabled,
     policy_prefs::kAllowBackForwardCacheForCacheControlNoStorePageEnabled,
@@ -2824,29 +2782,6 @@ std::unique_ptr<ConfigurationPolicyHandl
           key::kBrowserContextAwareAccessSignalsAllowlist,
           enterprise_connectors::kBrowserContextAwareAccessSignalsAllowlistPref,
           chrome_schema));
-  handlers->AddHandler(
-      std::make_unique<SingleDeprecatedPolicyToMultipleNewPolicyHandler>(
-          std::make_unique<ManagedAccountRestrictionsPolicyHandler>(
-              chrome_schema),
-          std::vector<std::string>{
-              key::kProfileSeparationSettings,
-              key::kProfileSeparationDataMigrationSettings,
-              key::kProfileSeparationDomainExceptionList}));
-  handlers->AddHandler(std::make_unique<CloudUserOnlyPolicyHandler>(
-      std::make_unique<SimplePolicyHandler>(key::kProfileSeparationSettings,
-                                            prefs::kProfileSeparationSettings,
-                                            base::Value::Type::INTEGER)));
-
-
-  handlers->AddHandler(std::make_unique<SimpleDeprecatingPolicyHandler>(
-      std::make_unique<SimplePolicyHandler>(
-          key::kEnterpriseProfileCreationKeepBrowsingData,
-          prefs::kEnterpriseProfileCreationKeepBrowsingData,
-          base::Value::Type::BOOLEAN),
-      std::make_unique<SimplePolicyHandler>(
-          key::kProfileSeparationDataMigrationSettings,
-          prefs::kProfileSeparationDataMigrationSettings,
-          base::Value::Type::INTEGER)));
   handlers->AddHandler(std::make_unique<IntRangePolicyHandler>(
       key::kProfileReauthPrompt, enterprise_signin::prefs::kProfileReauthPrompt,
       static_cast<int>(enterprise_signin::ProfileReauthPrompt::kDoNotPrompt),
@@ -2907,11 +2842,6 @@ std::unique_ptr<ConfigurationPolicyHandl
 #else   // BUILDFLAG(IS_CHROMEOS)
   std::vector<std::unique_ptr<ConfigurationPolicyHandler>>
       signin_legacy_policies;
-#if BUILDFLAG(IS_ANDROID) || BUILDFLAG(IS_MAC) || BUILDFLAG(IS_WIN) || \
-    BUILDFLAG(IS_LINUX)
-  signin_legacy_policies.push_back(std::make_unique<SimplePolicyHandler>(
-      key::kForceBrowserSignin, prefs::kForceBrowserSignin,
-      base::Value::Type::BOOLEAN));
 
   handlers->AddHandler(std::make_unique<CloudUserOnlyPolicyHandler>(
       std::make_unique<SimplePolicyHandler>(
@@ -2924,19 +2854,6 @@ std::unique_ptr<ConfigurationPolicyHandl
           enterprise_reporting::kUserSecurityAuthenticatedReporting,
           base::Value::Type::BOOLEAN)));
 
-#endif  // BUILDFLAG(IS_ANDROID) || BUILDFLAG(IS_MAC) || BUILDFLAG(IS_WIN) ||
-        // BUILDFLAG(IS_LINUX)
-  signin_legacy_policies.push_back(std::make_unique<SimplePolicyHandler>(
-      key::kSigninAllowed,
-#if BUILDFLAG(IS_ANDROID)
-      // The new kSigninAllowedOnNextStartup pref is only used on Desktop.
-      // Keep the old kSigninAllowed pref for Android until the policy is
-      // fully deprecated in M71 and can be removed.
-      prefs::kSigninAllowed,
-#else   // BUILDFLAG(IS_ANDROID)
-      prefs::kSigninAllowedOnNextStartup,
-#endif  // BUILDFLAG(IS_ANDROID)
-      base::Value::Type::BOOLEAN));
   handlers->AddHandler(std::make_unique<LegacyPoliciesDeprecatingPolicyHandler>(
       std::move(signin_legacy_policies),
       std::make_unique<BrowserSigninPolicyHandler>(chrome_schema)));
--- a/chrome/browser/prefs/browser_prefs.cc
+++ b/chrome/browser/prefs/browser_prefs.cc
@@ -162,7 +162,6 @@
 #include "components/proxy_config/pref_proxy_config_tracker_impl.h"
 #include "components/regional_capabilities/regional_capabilities_prefs.h"
 #include "components/safe_browsing/buildflags.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/saved_tab_groups/public/pref_names.h"
 #include "components/search_engines/search_engine_choice/search_engine_choice_service.h"
 #include "components/search_engines/template_url_prepopulate_data.h"
@@ -174,7 +173,6 @@
 #include "components/sessions/core/session_id_generator.h"
 #include "components/sharing_message/sharing_sync_preference.h"
 #include "components/signin/core/browser/active_primary_accounts_metrics_recorder.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/site_engagement/content/site_engagement_service.h"
@@ -1632,7 +1630,6 @@ void RegisterLocalState(PrefRegistrySimp
   PushMessagingServiceImpl::RegisterPrefs(registry);
 #endif
   RegisterScreenshotPrefs(registry);
-  safe_browsing::RegisterLocalStatePrefs(registry);
   search_engines::SearchEngineChoiceService::RegisterLocalStatePrefs(registry);
   secure_origin_allowlist::RegisterPrefs(registry);
   segmentation_platform::SegmentationPlatformService::RegisterLocalStatePrefs(
@@ -1971,7 +1968,6 @@ void RegisterProfilePrefs(user_prefs::Pr
 #if BUILDFLAG(SAFE_BROWSING_AVAILABLE)
   safe_browsing::file_type::RegisterProfilePrefs(registry);
 #endif
-  safe_browsing::RegisterProfilePrefs(registry);
   SearchPrefetchService::RegisterProfilePrefs(registry);
   blocked_content::SafeBrowsingTriggeredPopupBlocker::RegisterProfilePrefs(
       registry);
--- a/chrome/browser/prefs/chrome_command_line_pref_store.cc
+++ b/chrome/browser/prefs/chrome_command_line_pref_store.cc
@@ -24,7 +24,6 @@
 #include "components/language/core/browser/pref_names.h"
 #include "components/proxy_config/proxy_config_dictionary.h"
 #include "components/proxy_config/proxy_config_pref_names.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_switches.h"
 #include "components/sync/base/pref_names.h"
 #include "content/public/common/content_switches.h"
@@ -84,8 +83,6 @@ const CommandLinePrefStore::BooleanSwitc
         {switches::kAllowCrossOriginAuthPrompt,
          prefs::kAllowCrossOriginAuthPrompt, true},
         {switches::kDisablePrintPreview, prefs::kPrintPreviewDisabled, true},
-        {safe_browsing::switches::kSbEnableEnhancedProtection,
-         prefs::kSafeBrowsingEnhanced, true},
 #if BUILDFLAG(IS_CHROMEOS)
         {ash::switches::kEnableTouchpadThreeFingerClick,
          ash::prefs::kEnableTouchpadThreeFingerClick, true},
--- a/chrome/browser/prefs/chrome_pref_service_factory.cc
+++ b/chrome/browser/prefs/chrome_pref_service_factory.cc
@@ -56,10 +56,8 @@
 #include "components/prefs/pref_store.h"
 #include "components/prefs/pref_value_store.h"
 #include "components/prefs/wrap_with_prefix_pref_store.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/search_engines/default_search_manager.h"
 #include "components/search_engines/search_engines_pref_names.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/supervised_user/core/browser/supervised_user_content_filters_service.h"
 #include "components/supervised_user/core/browser/supervised_user_pref_store.h"
@@ -131,9 +129,6 @@ const auto kTrackedPrefs = std::to_array
     {5, extensions::pref_names::kExtensions, EnforcementLevel::NO_ENFORCEMENT,
      PrefTrackingStrategy::SPLIT, ValueType::IMPERSONAL},
 #endif
-    {6, prefs::kGoogleServicesLastSyncingUsername,
-     EnforcementLevel::ENFORCE_ON_LOAD, PrefTrackingStrategy::ATOMIC,
-     ValueType::PERSONAL},
     {7, prefs::kSearchProviderOverrides, EnforcementLevel::ENFORCE_ON_LOAD,
      PrefTrackingStrategy::ATOMIC, ValueType::IMPERSONAL},
 #if !BUILDFLAG(IS_ANDROID)
@@ -157,19 +152,12 @@ const auto kTrackedPrefs = std::to_array
      PrefTrackingStrategy::ATOMIC, ValueType::IMPERSONAL},
     // kSyncRemainingRollbackTries is deprecated and will be removed a few
     // releases after M50.
-    {18, prefs::kSafeBrowsingIncidentsSent, EnforcementLevel::ENFORCE_ON_LOAD,
-     PrefTrackingStrategy::ATOMIC, ValueType::IMPERSONAL},
-    {23, prefs::kGoogleServicesAccountId, EnforcementLevel::ENFORCE_ON_LOAD,
-     PrefTrackingStrategy::ATOMIC, ValueType::PERSONAL},
     {29, prefs::kMediaStorageIdSalt, EnforcementLevel::ENFORCE_ON_LOAD,
      PrefTrackingStrategy::ATOMIC, ValueType::IMPERSONAL},
 #if BUILDFLAG(IS_WIN)
     {32, prefs::kMediaCdmOriginData, EnforcementLevel::ENFORCE_ON_LOAD,
      PrefTrackingStrategy::ATOMIC, ValueType::IMPERSONAL},
 #endif  // BUILDFLAG(IS_WIN)
-    {33, prefs::kGoogleServicesLastSignedInUsername,
-     EnforcementLevel::ENFORCE_ON_LOAD, PrefTrackingStrategy::ATOMIC,
-     ValueType::PERSONAL},
     {34, enterprise_signin::prefs::kPolicyRecoveryToken,
      EnforcementLevel::ENFORCE_ON_LOAD, PrefTrackingStrategy::ATOMIC,
      ValueType::IMPERSONAL},
--- a/chrome/browser/prefs/pref_functional_browsertest.cc
+++ b/chrome/browser/prefs/pref_functional_browsertest.cc
@@ -26,7 +26,6 @@
 #include "components/content_settings/core/common/content_settings_types.h"
 #include "components/content_settings/core/common/pref_names.h"
 #include "components/embedder_support/pref_names.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/sync_preferences/pref_service_syncable.h"
 #include "content/public/browser/web_contents.h"
 #include "content/public/test/browser_test.h"
@@ -219,9 +218,6 @@ IN_PROC_BROWSER_TEST_F(PrefsFunctionalTe
   prefetch::SetPreloadPagesState(prefs,
                                  prefetch::PreloadPagesState::kNoPreloading);
 
-  EXPECT_TRUE(prefs->GetBoolean(prefs::kSafeBrowsingEnabled));
-  prefs->SetBoolean(prefs::kSafeBrowsingEnabled, false);
-
   EXPECT_TRUE(prefs->GetBoolean(embedder_support::kAlternateErrorPagesEnabled));
   prefs->SetBoolean(embedder_support::kAlternateErrorPagesEnabled, false);
 
@@ -235,7 +231,6 @@ IN_PROC_BROWSER_TEST_F(PrefsFunctionalTe
 
   EXPECT_EQ(prefetch::PreloadPagesState::kNoPreloading,
             prefetch::GetPreloadPagesState(*prefs));
-  EXPECT_FALSE(prefs->GetBoolean(prefs::kSafeBrowsingEnabled));
   EXPECT_FALSE(
       prefs->GetBoolean(embedder_support::kAlternateErrorPagesEnabled));
   EXPECT_FALSE(prefs->GetBoolean(prefs::kSearchSuggestEnabled));
--- a/chrome/browser/profiles/gaia_info_update_service.cc
+++ b/chrome/browser/profiles/gaia_info_update_service.cc
@@ -22,7 +22,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/avatar_icon_util.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
--- a/chrome/browser/profiles/gaia_info_update_service_unittest.cc
+++ b/chrome/browser/profiles/gaia_info_update_service_unittest.cc
@@ -36,7 +36,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/profile_metrics/state.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/identity_manager/account_capabilities_test_mutator.h"
 #include "components/signin/public/identity_manager/account_info.h"
--- a/chrome/browser/profiles/profile_attributes_entry.cc
+++ b/chrome/browser/profiles/profile_attributes_entry.cc
@@ -28,7 +28,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/prefs/scoped_user_pref_update.h"
 #include "components/profile_metrics/state.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/signin_constants.h"
@@ -519,7 +518,7 @@ bool ProfileAttributesEntry::IsUsingDefa
 }
 
 bool ProfileAttributesEntry::IsSignedInWithCredentialProvider() const {
-  return GetBool(prefs::kSignedInWithCredentialProvider);
+  return false;
 }
 
 bool ProfileAttributesEntry::IsDasherlessManagement() const {
@@ -764,7 +763,6 @@ void ProfileAttributesEntry::SetLastDown
 }
 
 void ProfileAttributesEntry::SetSignedInWithCredentialProvider(bool value) {
-  SetBool(prefs::kSignedInWithCredentialProvider, value);
 }
 
 void ProfileAttributesEntry::SetDasherlessManagement(bool value) {
--- a/chrome/browser/profiles/profile_attributes_storage.cc
+++ b/chrome/browser/profiles/profile_attributes_storage.cc
@@ -43,7 +43,6 @@
 #include "components/prefs/scoped_user_pref_update.h"
 #include "components/profile_metrics/state.h"
 #include "components/signin/public/base/persistent_repeating_timer.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_managed_status_finder.h"
 #include "content/public/browser/browser_task_traits.h"
@@ -392,9 +391,7 @@ void ProfileAttributesStorage::AddProfil
                    params.profile_name,
                    /*include_check_for_legacy_profile_name*/ false))
           // Assume newly created profiles use a default avatar.
-          .Set(ProfileAttributesEntry::kIsUsingDefaultAvatarKey, true)
-          .Set(prefs::kSignedInWithCredentialProvider,
-               params.is_signed_in_with_credential_provider);
+          .Set(ProfileAttributesEntry::kIsUsingDefaultAvatarKey, true);
 
   if (params.account_id.HasAccountIdKey()) {
     info.Set(ProfileAttributesEntry::kAccountIdKey,
--- a/chrome/browser/profiles/profile_impl.cc
+++ b/chrome/browser/profiles/profile_impl.cc
@@ -159,7 +159,6 @@
 #include "components/profile_metrics/browser_profile_type.h"
 #include "components/safe_search_api/safe_search_util.h"
 #include "components/security_interstitials/content/stateful_ssl_host_state_delegate.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/site_isolation/site_isolation_policy.h"
@@ -1117,7 +1116,6 @@ void ProfileImpl::OnLocaleReady(CreateMo
   CHECK(!ProfilePasswordStoreFactory::HasStore(this));
   CHECK(!AccountPasswordStoreFactory::HasStore(this));
   CHECK(!ReadingListModelFactory::HasModel(this));
-  browser_sync::MaybeMigrateSyncingUserToSignedIn(GetPath(), GetPrefs());
 
 #if BUILDFLAG(IS_ANDROID)
   // On Android StartupData creates proto database provider for the profile
--- a/chrome/browser/profiles/profile_manager.cc
+++ b/chrome/browser/profiles/profile_manager.cc
@@ -94,7 +94,6 @@
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_buildflags.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/signin/public/identity_manager/primary_account_mutator.h"
 #include "components/signin/public/identity_manager/tribool.h"
@@ -1900,8 +1899,7 @@ void ProfileManager::AddProfileToStorage
       entry->SetAuthInfo(account_info.gaia, username,
                          is_consented_primary_account);
 
-      entry->SetSignedInWithCredentialProvider(profile->GetPrefs()->GetBoolean(
-          prefs::kSignedInWithCredentialProvider));
+      entry->SetSignedInWithCredentialProvider(false);
 
 #if !BUILDFLAG(IS_ANDROID) && !BUILDFLAG(IS_CHROMEOS)
       // Sign out if force-sign-in policy is enabled and profile is not signed
@@ -1950,14 +1948,13 @@ void ProfileManager::AddProfileToStorage
 
   init_params.is_ephemeral = IsForceEphemeralProfilesEnabled(profile);
   init_params.is_signed_in_with_credential_provider =
-      profile->GetPrefs()->GetBoolean(prefs::kSignedInWithCredentialProvider);
+      false;
 
   storage.AddProfile(std::move(init_params));
 }
 
 void ProfileManager::SetNonPersonalProfilePrefs(Profile* profile) {
   PrefService* prefs = profile->GetPrefs();
-  prefs->SetBoolean(prefs::kSigninAllowed, false);
   prefs->SetBoolean(bookmarks::prefs::kEditBookmarksEnabled, false);
   prefs->SetBoolean(bookmarks::prefs::kShowBookmarkBar, false);
   prefs->ClearPref(DefaultSearchManager::kDefaultSearchProviderDataPrefName);
--- a/chrome/browser/profiles/profile_window.cc
+++ b/chrome/browser/profiles/profile_window.cc
@@ -38,7 +38,6 @@
 #include "chrome/common/pref_names.h"
 #include "chrome/common/url_constants.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/webui/flags/pref_service_flags_storage.h"
--- a/chrome/browser/profiles/profiles_state.cc
+++ b/chrome/browser/profiles/profiles_state.cc
@@ -48,7 +48,6 @@
 #include <algorithm>
 #include "chrome/browser/profiles/gaia_info_update_service.h"
 #include "chrome/browser/profiles/gaia_info_update_service_factory.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #endif
 
 namespace profiles {
--- a/chrome/browser/resources/settings/privacy_page/privacy_page.html
+++ b/chrome/browser/resources/settings/privacy_page/privacy_page.html
@@ -665,7 +665,7 @@
           </category-setting-exceptions>
         </settings-subpage>
       </template>
-      <template is="dom-if" if="[[enableSafeBrowsingSubresourceFilter_]]"
+      <template is="dom-if" if="[[false]]"
           no-search>
         <template is="dom-if" route-path="/content/ads" no-search>
           <settings-subpage page-title="$i18n{siteSettingsAds}"
--- a/chrome/browser/resources/settings/privacy_page/privacy_page.ts
+++ b/chrome/browser/resources/settings/privacy_page/privacy_page.ts
@@ -97,7 +97,7 @@ export class SettingsPrivacyPageElement
       enableSafeBrowsingSubresourceFilter_: {
         type: Boolean,
         value() {
-          return loadTimeData.getBoolean('enableSafeBrowsingSubresourceFilter');
+          return false;
         },
       },
 
--- a/chrome/browser/resources/settings/privacy_page/security_page.html
+++ b/chrome/browser/resources/settings/privacy_page/security_page.html
@@ -71,126 +71,6 @@
         pointer-events: auto;
       }
     </style>
-    <div id="safeBrowsingSection">
-      <h2 class="cr-title-text">$i18n{safeBrowsingSectionLabel}</h2>
-      <settings-radio-group id="safeBrowsingRadioGroup" no-set-pref
-          pref="{{prefs.generated.safe_browsing}}"
-          selectable-elements="cr-radio-button, settings-collapse-radio-button"
-          on-change="onSafeBrowsingRadioChange_">
-        <settings-collapse-radio-button id="safeBrowsingEnhanced"
-            name="[[safeBrowsingSettingEnum_.ENHANCED]]"
-            pref="[[prefs.generated.safe_browsing]]"
-            label="$i18n{safeBrowsingEnhanced}"
-            sub-label="$i18n{safeBrowsingEnhancedDescUpdated}"
-            indicator-aria-label="$i18n{controlledSettingPolicy}"
-            expand-aria-label="$i18n{safeBrowsingEnhancedExpandA11yLabel}"
-            on-expand-clicked="onEnhancedProtectionExpandButtonClicked_"
-            no-automatic-collapse>
-          <div slot="collapse">
-            <div id="enhancedProtectionDescContainer"
-                class="settings-columned-section">
-              <div class="column">
-                <h3 class="description-header">
-                  $i18n{columnHeadingWhenOn}
-                </h3>
-                <ul class="icon-bulleted-list">
-                  <li>
-                    <cr-icon icon="settings20:data" aria-hidden="true">
-                    </cr-icon>
-                    <div class="secondary">
-                      $i18n{safeBrowsingEnhancedWhenOnBulOne}
-                    </div>
-                  </li>
-                  <li>
-                    <cr-icon icon="settings20:download" aria-hidden="true">
-                    </cr-icon>
-                    <div class="secondary">
-                      $i18n{safeBrowsingEnhancedWhenOnBulTwo}
-                    </div>
-                  </li>
-                  <li>
-                    <cr-icon icon="settings20:gshield" aria-hidden="true">
-                    </cr-icon>
-                    <div class="secondary">
-                      $i18n{safeBrowsingEnhancedWhenOnBulThree}
-                    </div>
-                  </li>
-                  <li>
-                    <cr-icon icon="settings:language" aria-hidden="true">
-                    </cr-icon>
-                    <div class="secondary">
-                      $i18n{safeBrowsingEnhancedWhenOnBulFour}
-                    </div>
-                  </li>
-                </ul>
-              </div>
-              <div class="column">
-                <h3 class="description-header">
-                  $i18n{columnHeadingConsider}
-                </h3>
-                <ul class="icon-bulleted-list">
-                  <li>
-                    <cr-icon icon="settings20:link"></cr-icon>
-                    <div class="cr-secondary-text">
-                      $i18n{safeBrowsingEnhancedThingsToConsiderBulOne}
-                    </div>
-                  </li>
-                  <li>
-                    <cr-icon icon="settings20:account-circle"></cr-icon>
-                    <div class="cr-secondary-text">
-                      $i18n{safeBrowsingEnhancedThingsToConsiderBulTwo}
-                    </div>
-                  </li>
-                  <li>
-                    <cr-icon icon="settings:performance"></cr-icon>
-                    <div class="cr-secondary-text">
-                      $i18n{safeBrowsingEnhancedThingsToConsiderBulThree}
-                    </div>
-                  </li>
-                </ul>
-              </div>
-            </div>
-            <div id="learnMoreLabelContainer">
-              <div class="cr-secondary-text">
-                $i18nRaw{safeBrowsingEnhancedLearnMoreLabel}
-              </div>
-            </div>
-          </div>
-        </settings-collapse-radio-button>
-        <settings-collapse-radio-button id="safeBrowsingStandard"
-            no-collapse="[[hideExtendedReportingRadioButton_]]"
-            name="[[safeBrowsingSettingEnum_.STANDARD]]"
-            pref="[[prefs.generated.safe_browsing]]"
-            label="$i18n{safeBrowsingStandard}"
-            sub-label="[[getSafeBrowsingStandardSubLabel_(
-                        enableHashPrefixRealTimeLookups_)]]"
-            indicator-aria-label="$i18n{controlledSettingPolicy}"
-            expand-aria-label="$i18n{safeBrowsingStandardExpandA11yLabel}"
-            info-opened="{{infoOpened_}}"
-            on-expand-clicked="onStandardProtectionExpandButtonClicked_"
-            no-automatic-collapse>
-          <div slot="noSelectionCollapse">
-            <template is="dom-if" if="[[!hideExtendedReportingRadioButton_]]">
-              <settings-toggle-button id="safeBrowsingReportingToggle"
-                pref="{{prefs.safebrowsing.scout_reporting_enabled}}"
-                label="$i18n{safeBrowsingStandardReportingLabel}"
-                sub-label="$i18n{safeBrowsingEnableExtendedReportingDesc}"
-                on-change="onSafeBrowsingExtendedReportingChange_"
-                disabled="[[getDisabledExtendedSafeBrowsing_(
-                              prefs.generated.safe_browsing.*)]]">
-              </settings-toggle-button>
-            </template>
-          </div>
-        </settings-collapse-radio-button>
-        <settings-collapse-radio-button id="safeBrowsingDisabled" no-collapse
-            name="[[safeBrowsingSettingEnum_.DISABLED]]"
-            pref="[[prefs.generated.safe_browsing]]"
-            label="$i18n{safeBrowsingNone}"
-            sub-label="$i18n{safeBrowsingNoneDesc}"
-            indicator-aria-label="$i18n{controlledSettingPolicy}">
-        </settings-collapse-radio-button>
-      </settings-radio-group>
-    </div>
     <template is="dom-if" if="[[enableHttpsFirstModeNewSettings_]]" restamp>
       <div id="secureConnectionsSection">
         <h2 class="cr-title-text">$i18n{secureConnectionsSectionTitle}</h2>
@@ -296,11 +176,3 @@
         on-click="onAdvancedProtectionProgramLinkClick_"
         external>
     </cr-link-row>
-    <template is="dom-if" if="[[showDisableSafebrowsingDialog_]]" restamp>
-      <settings-simple-confirmation-dialog
-          title-text="$i18n{safeBrowsingDisableDialog}"
-          body-text="$i18n{safeBrowsingDisableDialogDesc}"
-          confirm-text="$i18n{safeBrowsingDisableDialogConfirm}"
-          on-close="onDisableSafebrowsingDialogClose_">
-      </settings-simple-confirmation-dialog>
-    </template>
--- a/chrome/browser/resources/settings/privacy_page/security_page.ts
+++ b/chrome/browser/resources/settings/privacy_page/security_page.ts
@@ -270,17 +270,6 @@ export class SettingsSecurityPageElement
     super.ready();
 
     CrSettingsPrefs.initialized.then(() => {
-      // Expand initial pref value manually because automatic
-      // expanding is disabled.
-      const prefValue = this.getPref('generated.safe_browsing').value;
-      if (prefValue === SafeBrowsingSetting.ENHANCED) {
-        this.$.safeBrowsingEnhanced.expanded = true;
-      } else if (prefValue === SafeBrowsingSetting.STANDARD) {
-        this.$.safeBrowsingStandard.expanded = true;
-      }
-
-      this.safeBrowsingStateOnOpen_ = prefValue;
-
       // The HTTPS-First Mode generated pref should never be set to
       // ENABLED_BALANCED if the feature flag is not enabled.
       if (!loadTimeData.getBoolean('enableHttpsFirstModeNewSettings')) {
@@ -290,10 +279,6 @@ export class SettingsSecurityPageElement
       }
     });
 
-    this.registerHelpBubble(
-        'kEnhancedProtectionSettingElementId',
-        this.$.safeBrowsingEnhanced.getBubbleAnchor(), {anchorPaddingTop: 10});
-
     // Initialize the last focus time on page load.
     this.lastFocusTime_ = HatsBrowserProxyImpl.getInstance().now();
 
@@ -431,8 +416,7 @@ export class SettingsSecurityPageElement
   }
 
   private getDisabledExtendedSafeBrowsing_(): boolean {
-    return this.getPref('generated.safe_browsing').value !==
-        SafeBrowsingSetting.STANDARD;
+    return true;
   }
 
   private getSafeBrowsingStandardSubLabel_(): string {
@@ -444,19 +428,6 @@ export class SettingsSecurityPageElement
 
   private getPasswordsLeakToggleSubLabel_(): string {
     let subLabel = this.i18n('passwordsLeakDetectionGeneralDescription');
-    // If the backing password leak detection preference is enabled, but the
-    // generated preference is off and user control is disabled, then additional
-    // text explaining that the feature will be enabled if the user signs in is
-    // added.
-    if (this.prefs !== undefined) {
-      const generatedPref = this.getPref('generated.password_leak_detection');
-      if (this.getPref('profile.password_manager_leak_detection').value &&
-          !generatedPref.value && generatedPref.userControlDisabled) {
-        subLabel +=
-            ' ' +  // Whitespace is a valid sentence separator w.r.t. i18n.
-            this.i18n('passwordsLeakDetectionSignedOutEnabledDescription');
-      }
-    }
     return subLabel;
   }
 
--- a/chrome/browser/resources/settings/site_settings_page/site_settings_page.ts
+++ b/chrome/browser/resources/settings/site_settings_page/site_settings_page.ts
@@ -56,7 +56,7 @@ function getCategoryItemMap(): Map<Conte
       enabledLabel: 'siteSettingsAdsAllowed',
       disabledLabel: 'siteSettingsAdsBlocked',
       shouldShow: () =>
-          loadTimeData.getBoolean('enableSafeBrowsingSubresourceFilter'),
+          false,
     },
     {
       route: routes.SITE_SETTINGS_AUTO_VERIFY,
--- a/chrome/browser/safe_browsing/advanced_protection_status_manager_desktop.cc
+++ b/chrome/browser/safe_browsing/advanced_protection_status_manager_desktop.cc
@@ -15,7 +15,6 @@
 #include "chrome/browser/signin/identity_manager_factory.h"
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_switches.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/identity_manager/account_info.h"
@@ -72,9 +71,7 @@ void AdvancedProtectionStatusManagerDesk
   RecordStartupUma(is_under_advanced_protection_);
   NotifyObserversStatusChanged();
 
-  if (pref_service_->HasPrefPath(prefs::kAdvancedProtectionLastRefreshInUs)) {
-    last_refreshed_ = base::Time::FromDeltaSinceWindowsEpoch(base::Microseconds(
-        pref_service_->GetInt64(prefs::kAdvancedProtectionLastRefreshInUs)));
+  if (false) {
     if (is_under_advanced_protection_) {
       ScheduleNextRefresh();
     }
@@ -96,16 +93,7 @@ AdvancedProtectionStatusManagerDesktop::
     ~AdvancedProtectionStatusManagerDesktop() = default;
 
 bool AdvancedProtectionStatusManagerDesktop::IsUnderAdvancedProtection() const {
-  if (!pref_service_->GetBoolean(prefs::kAdvancedProtectionAllowed)) {
     return false;
-  }
-
-  if (base::CommandLine::ForCurrentProcess()->HasSwitch(
-          switches::kForceTreatUserAsAdvancedProtection)) {
-    return true;
-  }
-
-  return is_under_advanced_protection_;
 }
 
 void AdvancedProtectionStatusManagerDesktop::
@@ -263,10 +251,6 @@ void AdvancedProtectionStatusManagerDesk
 }
 
 void AdvancedProtectionStatusManagerDesktop::UpdateLastRefreshTime() {
-  last_refreshed_ = base::Time::Now();
-  pref_service_->SetInt64(
-      prefs::kAdvancedProtectionLastRefreshInUs,
-      last_refreshed_.ToDeltaSinceWindowsEpoch().InMicroseconds());
 }
 
 bool AdvancedProtectionStatusManagerDesktop::IsUnconsentedPrimaryAccount(
--- a/chrome/browser/safe_browsing/advanced_protection_status_manager_unittest.cc
+++ b/chrome/browser/safe_browsing/advanced_protection_status_manager_unittest.cc
@@ -8,7 +8,6 @@
 #include "base/test/task_environment.h"
 #include "build/build_config.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/identity_test_environment.h"
--- a/chrome/browser/safe_browsing/android/services_delegate_android.h
+++ b/chrome/browser/safe_browsing/android/services_delegate_android.h
@@ -6,7 +6,6 @@
 #define CHROME_BROWSER_SAFE_BROWSING_ANDROID_SERVICES_DELEGATE_ANDROID_H_
 
 #include "chrome/browser/safe_browsing/services_delegate.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 namespace safe_browsing {
 
--- a/chrome/browser/safe_browsing/chrome_password_protection_service.cc
+++ b/chrome/browser/safe_browsing/chrome_password_protection_service.cc
@@ -72,7 +72,6 @@
 #include "components/safe_browsing/core/browser/verdict_cache_manager.h"
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_constants.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "components/security_interstitials/core/unsafe_resource.h"
@@ -1373,14 +1372,6 @@ bool ChromePasswordProtectionService::Ha
 }
 
 void ChromePasswordProtectionService::OnWarningTriggerChanged() {
-  const base::Value& pref_value = pref_change_registrar_->prefs()->GetValue(
-      prefs::kPasswordProtectionWarningTrigger);
-  // If password protection is not turned off, do nothing.
-  if (static_cast<PasswordProtectionTrigger>(pref_value.GetInt()) !=
-      PASSWORD_PROTECTION_OFF) {
-    return;
-  }
-
   // Clears captured enterprise password hashes or GSuite sync password hashes.
   password_manager::PasswordReuseManager* reuse_manager =
       GetPasswordReuseManager();
@@ -1553,7 +1544,7 @@ PrefService* ChromePasswordProtectionSer
 }
 
 bool ChromePasswordProtectionService::IsSafeBrowsingEnabled() {
-  return ::safe_browsing::IsSafeBrowsingEnabled(*GetPrefs());
+  return false;
 }
 
 bool ChromePasswordProtectionService::IsExtendedReporting() {
@@ -1573,39 +1564,7 @@ bool ChromePasswordProtectionService::Is
 bool ChromePasswordProtectionService::IsPingingEnabled(
     LoginReputationClientRequest::TriggerType trigger_type,
     ReusedPasswordAccountType password_type) {
-  if (!IsSafeBrowsingEnabled()) {
-    return false;
-  }
-  bool extended_reporting_enabled = IsExtendedReporting();
-  if (trigger_type == LoginReputationClientRequest::PASSWORD_REUSE_EVENT) {
-    // Don't send a ping if the password protection setting is off
-    if (GetPasswordProtectionWarningTriggerPref(password_type) ==
-        PASSWORD_PROTECTION_OFF) {
-      return false;
-    }
-    // If the account type is UNKNOWN (i.e. AccountInfo fields could not be
-    // retrieved from server), pings should be gated by SBER.
-    if (password_type.account_type() == ReusedPasswordAccountType::UNKNOWN) {
-      return extended_reporting_enabled;
-    }
-
-// Only saved password and GAIA password reuse warnings are shown to users on
-// Android, so other types of password reuse events should be gated by Safe
-// Browsing extended reporting.
-#if BUILDFLAG(IS_ANDROID)
-    if (password_type.account_type() ==
-            ReusedPasswordAccountType::SAVED_PASSWORD ||
-        password_type.account_type() == ReusedPasswordAccountType::GMAIL) {
-      return true;
-    }
-
-    return extended_reporting_enabled;
-#else
-    return true;
-#endif
-  }
-
-  return !IsIncognito() && extended_reporting_enabled;
+  return false;
 }
 
 RequestOutcome ChromePasswordProtectionService::GetPingNotSentReason(
@@ -1847,23 +1806,6 @@ MaybeCreateCommitDeferringCondition(
              : nullptr;
 }
 
-PasswordProtectionTrigger
-ChromePasswordProtectionService::GetPasswordProtectionWarningTriggerPref(
-    ReusedPasswordAccountType password_type) const {
-  bool is_policy_managed = profile_->GetPrefs()->HasPrefPath(
-      prefs::kPasswordProtectionWarningTrigger);
-  PasswordProtectionTrigger trigger_level =
-      static_cast<PasswordProtectionTrigger>(profile_->GetPrefs()->GetInteger(
-          prefs::kPasswordProtectionWarningTrigger));
-  if (is_policy_managed && trigger_level == PASSWORD_PROTECTION_OFF) {
-    return PASSWORD_PROTECTION_OFF;
-  }
-  if (password_type.account_type() == ReusedPasswordAccountType::GMAIL) {
-    return PHISHING_REUSE;
-  }
-  return is_policy_managed ? trigger_level : PHISHING_REUSE;
-}
-
 bool ChromePasswordProtectionService::IsURLAllowlistedForPasswordEntry(
     const GURL& url) const {
   if (!profile_)
--- a/chrome/browser/safe_browsing/chrome_password_protection_service.h
+++ b/chrome/browser/safe_browsing/chrome_password_protection_service.h
@@ -219,13 +219,6 @@ class ChromePasswordProtectionService :
   bool UserClickedThroughSBInterstitial(
       PasswordProtectionRequest* request) override;
 
-  // If |prefs::kPasswordProtectionWarningTrigger| is not managed by enterprise
-  // policy, this function should always return PHISHING_REUSE. Otherwise,
-  // returns the specified pref value adjusted for the given username's account
-  // type.
-  PasswordProtectionTrigger GetPasswordProtectionWarningTriggerPref(
-      ReusedPasswordAccountType password_type) const override;
-
   // If |url| matches Safe Browsing allowlist domains, password protection
   // change password URL, or password protection login URLs in the enterprise
   // policy.
--- a/chrome/browser/safe_browsing/chrome_password_protection_service_browsertest.cc
+++ b/chrome/browser/safe_browsing/chrome_password_protection_service_browsertest.cc
@@ -40,7 +40,6 @@
 #include "components/safe_browsing/content/browser/password_protection/password_protection_request_content.h"
 #include "components/safe_browsing/content/browser/password_protection/password_protection_test_util.h"
 #include "components/safe_browsing/core/browser/password_protection/metrics_util.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_state/content/security_state_tab_helper.h"
 #include "components/security_state/core/security_state.h"
 #include "components/signin/public/identity_manager/account_info.h"
--- a/chrome/browser/safe_browsing/chrome_password_protection_service_sync_browsertest.cc
+++ b/chrome/browser/safe_browsing/chrome_password_protection_service_sync_browsertest.cc
@@ -27,10 +27,8 @@
 #include "components/prefs/pref_service.h"
 #include "components/prefs/scoped_user_pref_update.h"
 #include "components/safe_browsing/content/browser/password_protection/password_protection_request_content.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_state/content/security_state_tab_helper.h"
 #include "components/security_state/core/security_state.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/signin/public/identity_manager/identity_test_utils.h"
 #include "components/sync/service/sync_service.h"
--- a/chrome/browser/safe_browsing/download_protection/check_client_download_request.cc
+++ b/chrome/browser/safe_browsing/download_protection/check_client_download_request.cc
@@ -36,7 +36,6 @@
 #include "components/safe_browsing/content/common/file_type_policies.h"
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "components/url_matcher/url_matcher.h"
 #include "content/public/browser/browser_context.h"
--- a/chrome/browser/safe_browsing/download_protection/check_client_download_request_base.cc
+++ b/chrome/browser/safe_browsing/download_protection/check_client_download_request_base.cc
@@ -24,7 +24,6 @@
 #include "components/safe_browsing/content/browser/web_ui/safe_browsing_ui.h"
 #include "components/safe_browsing/content/common/file_type_policies.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "content/public/browser/browser_context.h"
 #include "content/public/browser/browser_task_traits.h"
--- a/chrome/browser/safe_browsing/download_protection/download_protection_service_unittest.cc
+++ b/chrome/browser/safe_browsing/download_protection/download_protection_service_unittest.cc
@@ -93,7 +93,6 @@
 #include "components/safe_browsing/core/browser/db/v4_protocol_manager_util.h"
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_switches.h"
 #include "components/signin/public/identity_manager/identity_test_environment.h"
 #include "content/public/browser/browser_task_traits.h"
--- a/chrome/browser/safe_browsing/generated_safe_browsing_pref.cc
+++ b/chrome/browser/safe_browsing/generated_safe_browsing_pref.cc
@@ -8,7 +8,6 @@
 #include "chrome/browser/profiles/profile.h"
 #include "chrome/common/extensions/api/settings_private.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 namespace settings_api = extensions::api::settings_private;
 
@@ -19,21 +18,6 @@ const char kGeneratedSafeBrowsingPref[]
 GeneratedSafeBrowsingPref::GeneratedSafeBrowsingPref(Profile* profile)
     : profile_(profile) {
   user_prefs_registrar_.Init(profile->GetPrefs());
-  user_prefs_registrar_.Add(
-      prefs::kSafeBrowsingEnabled,
-      base::BindRepeating(
-          &GeneratedSafeBrowsingPref::OnSafeBrowsingPreferencesChanged,
-          base::Unretained(this)));
-  user_prefs_registrar_.Add(
-      prefs::kSafeBrowsingEnhanced,
-      base::BindRepeating(
-          &GeneratedSafeBrowsingPref::OnSafeBrowsingPreferencesChanged,
-          base::Unretained(this)));
-  user_prefs_registrar_.Add(
-      prefs::kSafeBrowsingScoutReportingEnabled,
-      base::BindRepeating(
-          &GeneratedSafeBrowsingPref::OnSafeBrowsingPreferencesChanged,
-          base::Unretained(this)));
 }
 
 extensions::settings_private::SetPrefResult GeneratedSafeBrowsingPref::SetPref(
@@ -49,37 +33,14 @@ extensions::settings_private::SetPrefRes
     return extensions::settings_private::SetPrefResult::PREF_TYPE_MISMATCH;
 
   // If SBER is forcefully disabled, Enhanced cannot be selected by the user.
-  const PrefService::Preference* reporting_pref =
-      profile_->GetPrefs()->FindPreference(
-          prefs::kSafeBrowsingScoutReportingEnabled);
-  const bool reporting_on = reporting_pref->GetValue()->GetBool();
-  const bool reporting_enforced = !reporting_pref->IsUserModifiable();
+  const bool reporting_on = false;
+  const bool reporting_enforced = false;
 
   if (reporting_enforced && !reporting_on &&
       selection == SafeBrowsingSetting::ENHANCED) {
     return extensions::settings_private::SetPrefResult::PREF_NOT_MODIFIABLE;
   }
 
-  // kSafeBrowsingEnabled is considered the canonical source for Safe Browsing
-  // management.
-  const PrefService::Preference* enabled_pref =
-      profile_->GetPrefs()->FindPreference(prefs::kSafeBrowsingEnabled);
-  if (!enabled_pref->IsUserModifiable()) {
-    return extensions::settings_private::SetPrefResult::PREF_NOT_MODIFIABLE;
-  }
-
-  // Update both Safe Browsing preferences to match selection.
-  profile_->GetPrefs()->SetBoolean(prefs::kSafeBrowsingEnabled,
-                                   selection != SafeBrowsingSetting::DISABLED);
-  profile_->GetPrefs()->SetBoolean(prefs::kSafeBrowsingEnhanced,
-                                   selection == SafeBrowsingSetting::ENHANCED);
-
-  // Set ESB not set in sync with Account ESB through TailoredSecurity.
-  if (selection == SafeBrowsingSetting::ENHANCED) {
-    profile_->GetPrefs()->SetBoolean(
-        prefs::kEnhancedProtectionEnabledViaTailoredSecurity, false);
-  }
-
   return extensions::settings_private::SetPrefResult::SUCCESS;
 }
 
@@ -89,10 +50,8 @@ GeneratedSafeBrowsingPref::GetPrefObject
   pref_object.key = kGeneratedSafeBrowsingPref;
   pref_object.type = extensions::api::settings_private::PrefType::kNumber;
 
-  auto safe_browsing_enabled =
-      profile_->GetPrefs()->GetBoolean(prefs::kSafeBrowsingEnabled);
-  auto safe_browsing_enhanced_enabled =
-      profile_->GetPrefs()->GetBoolean(prefs::kSafeBrowsingEnhanced);
+  auto safe_browsing_enabled = false;
+  auto safe_browsing_enhanced_enabled = false;
 
   if (safe_browsing_enhanced_enabled && safe_browsing_enabled) {
     pref_object.value =
@@ -122,29 +81,18 @@ void GeneratedSafeBrowsingPref::ApplySaf
   // three different preferences. It is possible that these may be in
   // temporarily conflicting managed states. The enabled preference is always
   // taken as the canonical source of management.
-  const PrefService::Preference* enabled_pref =
-      profile.GetPrefs()->FindPreference(prefs::kSafeBrowsingEnabled);
-  const bool enabled_enforced = !enabled_pref->IsUserModifiable();
-  const bool enabled_recommended =
-      (enabled_pref && enabled_pref->GetRecommendedValue());
-  const bool enabled_recommended_on =
-      enabled_recommended && enabled_pref->GetRecommendedValue()->GetBool();
+  const bool enabled_enforced = false;
+  const bool enabled_recommended = false;
+  const bool enabled_recommended_on = false;
 
   // The enhanced preference may have a recommended setting. This only takes
   // effect if the enabled preference also has a recommended setting.
-  const PrefService::Preference* enhanced_pref =
-      profile.GetPrefs()->FindPreference(prefs::kSafeBrowsingEnhanced);
-  const bool enhanced_recommended_on =
-      enhanced_pref->GetRecommendedValue() &&
-      enhanced_pref->GetRecommendedValue()->GetBool();
+  const bool enhanced_recommended_on = false;
 
   // A forcefully disabled reporting preference will disallow enhanced from
   // being selected and thus it must also be considered.
-  const PrefService::Preference* reporting_pref =
-      profile.GetPrefs()->FindPreference(
-          prefs::kSafeBrowsingScoutReportingEnabled);
-  const bool reporting_on = reporting_pref->GetValue()->GetBool();
-  const bool reporting_enforced = !reporting_pref->IsUserModifiable();
+  const bool reporting_on = false;
+  const bool reporting_enforced = false;
 
   if (!enabled_enforced && !enabled_recommended && !reporting_enforced) {
     // No relevant policies are applied.
@@ -154,8 +102,6 @@ void GeneratedSafeBrowsingPref::ApplySaf
   if (enabled_enforced) {
     // Preference is fully controlled.
     pref_object.enforcement = settings_api::Enforcement::kEnforced;
-    extensions::settings_private::GeneratedPref::ApplyControlledByFromPref(
-        &pref_object, enabled_pref);
     return;
   }
 
@@ -179,8 +125,6 @@ void GeneratedSafeBrowsingPref::ApplySaf
     // Reporting has been forcefully disabled by policy. Enhanced protection is
     // thus also implicitly disabled by the same policy.
     pref_object.enforcement = settings_api::Enforcement::kEnforced;
-    extensions::settings_private::GeneratedPref::ApplyControlledByFromPref(
-        &pref_object, reporting_pref);
 
     pref_object.user_selectable_values.emplace();
     pref_object.user_selectable_values->Append(
--- a/chrome/browser/safe_browsing/incident_reporting/extension_data_collection_unittest.cc
+++ b/chrome/browser/safe_browsing/incident_reporting/extension_data_collection_unittest.cc
@@ -21,7 +21,6 @@
 #include "chrome/test/base/testing_profile.h"
 #include "chrome/test/base/testing_profile_manager.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/sync_preferences/testing_pref_service_syncable.h"
 #include "content/public/test/browser_task_environment.h"
 #include "content/public/test/test_utils.h"
--- a/chrome/browser/safe_browsing/incident_reporting/incident_reporting_service.cc
+++ b/chrome/browser/safe_browsing/incident_reporting/incident_reporting_service.cc
@@ -40,7 +40,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/browser_thread.h"
 #include "content/public/browser/download_item_utils.h"
 #include "services/network/public/cpp/shared_url_loader_factory.h"
--- a/chrome/browser/safe_browsing/incident_reporting/incident_reporting_service_unittest.cc
+++ b/chrome/browser/safe_browsing/incident_reporting/incident_reporting_service_unittest.cc
@@ -34,7 +34,6 @@
 #include "chrome/test/base/testing_profile.h"
 #include "chrome/test/base/testing_profile_manager.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/sync_preferences/testing_pref_service_syncable.h"
 #include "content/public/test/browser_task_environment.h"
 #include "extensions/browser/quota_service.h"
--- a/chrome/browser/safe_browsing/incident_reporting/last_download_finder_unittest.cc
+++ b/chrome/browser/safe_browsing/incident_reporting/last_download_finder_unittest.cc
@@ -42,7 +42,6 @@
 #include "components/history/core/browser/history_database_params.h"
 #include "components/history/core/browser/history_service.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/sync_preferences/testing_pref_service_syncable.h"
 #include "content/public/browser/download_manager.h"
 #include "content/public/test/browser_task_environment.h"
--- a/chrome/browser/safe_browsing/incident_reporting/state_store.cc
+++ b/chrome/browser/safe_browsing/incident_reporting/state_store.cc
@@ -14,7 +14,6 @@
 #include "chrome/browser/safe_browsing/incident_reporting/incident.h"
 #include "chrome/browser/safe_browsing/incident_reporting/platform_state_store.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 namespace safe_browsing {
 
--- a/chrome/browser/safe_browsing/incident_reporting/state_store_unittest.cc
+++ b/chrome/browser/safe_browsing/incident_reporting/state_store_unittest.cc
@@ -22,7 +22,6 @@
 #include "chrome/test/base/testing_profile_manager.h"
 #include "components/pref_registry/pref_registry_syncable.h"
 #include "components/prefs/in_memory_pref_store.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/sync_preferences/pref_service_syncable.h"
 #include "components/sync_preferences/pref_service_syncable_factory.h"
 #include "content/public/test/browser_task_environment.h"
--- a/chrome/browser/safe_browsing/metrics/safe_browsing_metrics_provider.cc
+++ b/chrome/browser/safe_browsing/metrics/safe_browsing_metrics_provider.cc
@@ -7,7 +7,6 @@
 #include "base/metrics/histogram_functions.h"
 #include "chrome/browser/profiles/profile.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 namespace safe_browsing {
 
--- a/chrome/browser/safe_browsing/safe_browsing_blocking_page_test.cc
+++ b/chrome/browser/safe_browsing/safe_browsing_blocking_page_test.cc
@@ -93,7 +93,6 @@
 #include "components/safe_browsing/core/browser/verdict_cache_manager.h"
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/hashprefix_realtime/hash_realtime_utils.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/web_ui_constants.h"
 #include "components/security_interstitials/content/security_interstitial_controller_client.h"
 #include "components/security_interstitials/content/security_interstitial_tab_helper.h"
--- a/chrome/browser/safe_browsing/safe_browsing_service.cc
+++ b/chrome/browser/safe_browsing/safe_browsing_service.cc
@@ -198,7 +198,6 @@ bool SafeBrowsingServiceImpl::IsUserElig
 
 SafeBrowsingServiceImpl::SafeBrowsingServiceImpl()
     : services_delegate_(ServicesDelegate::Create(this)),
-      estimated_extended_reporting_by_prefs_(SBER_LEVEL_OFF),
       shutdown_(false),
       enabled_(false),
       enabled_by_prefs_(false) {}
@@ -339,9 +338,6 @@ TriggerManager* SafeBrowsingServiceImpl:
 
 PasswordProtectionService*
 SafeBrowsingServiceImpl::GetPasswordProtectionService(Profile* profile) const {
-  if (IsSafeBrowsingEnabled(*profile->GetPrefs())) {
-    return ChromePasswordProtectionServiceFactory::GetForProfile(profile);
-  }
   return nullptr;
 }
 
@@ -442,9 +438,6 @@ void SafeBrowsingServiceImpl::OnProfileA
   std::unique_ptr<PrefChangeRegistrar> registrar =
       std::make_unique<PrefChangeRegistrar>();
   registrar->Init(pref_service);
-  registrar->Add(prefs::kSafeBrowsingEnabled,
-                 base::BindRepeating(&SafeBrowsingServiceImpl::RefreshState,
-                                     base::Unretained(this)));
   // ClientSideDetectionService will need to be refresh the models
   // renderers have if extended-reporting changes.
   registrar->Add(prefs::kSafeBrowsingScoutReportingEnabled,
@@ -484,14 +477,6 @@ void SafeBrowsingServiceImpl::OnProfileA
                           NoCachedPopulationReason::kChangeMbbPref));
   user_population_prefs_[pref_service] = std::move(user_population_registrar);
 
-  // Record the current pref state for standard protection.
-  UMA_HISTOGRAM_BOOLEAN(kSafeBrowsingEnabledHistogramName,
-                        pref_service->GetBoolean(prefs::kSafeBrowsingEnabled));
-  // Record the current pref state for enhanced protection. Enhanced protection
-  // is a subset of the standard protection. Thus, |kSafeBrowsingEnabled| count
-  // should always be more than the count of enhanced protection.
-  UMA_HISTOGRAM_BOOLEAN("SafeBrowsing.Pref.Enhanced",
-                        pref_service->GetBoolean(prefs::kSafeBrowsingEnhanced));
 
   // Record the current enhanced protection pref state for regular profiles only
   if (profiles::IsRegularUserProfile(profile)) {
@@ -603,18 +588,6 @@ void SafeBrowsingServiceImpl::RefreshSta
 
   // Check if any profile requires the service to be active.
   enabled_by_prefs_ = false;
-  estimated_extended_reporting_by_prefs_ = SBER_LEVEL_OFF;
-  for (const auto& pref : prefs_map_) {
-    if (IsSafeBrowsingEnabled(*pref.first)) {
-      enabled_by_prefs_ = true;
-
-      ExtendedReportingLevel erl =
-          safe_browsing::GetExtendedReportingLevel(*pref.first);
-      if (erl != SBER_LEVEL_OFF) {
-        estimated_extended_reporting_by_prefs_ = erl;
-      }
-    }
-  }
 
   if (enabled_by_prefs_) {
     Start();
--- a/chrome/browser/safe_browsing/safe_browsing_service.h
+++ b/chrome/browser/safe_browsing/safe_browsing_service.h
@@ -31,7 +31,6 @@
 #include "components/safe_browsing/core/browser/db/util.h"
 #include "components/safe_browsing/core/browser/ping_manager.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/browser_context.h"
 #include "content/public/browser/browser_thread.h"
 #include "services/network/public/mojom/network_context.mojom-forward.h"
@@ -105,15 +104,6 @@ class SafeBrowsingServiceImpl : public S
   // Called on the main thread to let us know that the io_thread is going away.
   void ShutDown();
 
-  // NOTE(vakh): This is not the most reliable way to find out if extended
-  // reporting has been enabled. That's why it starts with estimated_. It
-  // returns true if any of the profiles have extended reporting enabled. It may
-  // be called on any thread. That can lead to a race condition, but that's
-  // acceptable.
-  ExtendedReportingLevel estimated_extended_reporting_by_prefs() const {
-    return estimated_extended_reporting_by_prefs_;
-  }
-
   // Get current enabled status. Must be called on IO thread.
   bool enabled() const {
     DCHECK_CURRENTLY_ON(content::BrowserThread::IO);
@@ -382,10 +372,6 @@ class SafeBrowsingServiceImpl : public S
 
   std::unique_ptr<ProxyConfigMonitor> proxy_config_monitor_;
 
-  // Whether SafeBrowsing Extended Reporting is enabled by the current set of
-  // profiles. Updated on the UI thread.
-  ExtendedReportingLevel estimated_extended_reporting_by_prefs_;
-
   // Whether the service has been shutdown.
   bool shutdown_;
 
--- a/chrome/browser/safe_browsing/telemetry/android/android_telemetry_service.cc
+++ b/chrome/browser/safe_browsing/telemetry/android/android_telemetry_service.cc
@@ -30,7 +30,6 @@
 #include "components/safe_browsing/core/browser/db/database_manager.h"
 #include "components/safe_browsing/core/browser/ping_manager.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/browser_task_traits.h"
 #include "content/public/browser/browser_thread.h"
 #include "content/public/browser/download_item_utils.h"
@@ -137,11 +136,9 @@ bool AndroidTelemetryService::CanSendPin
     return false;
   }
 
-  if (!IsSafeBrowsingEnabled(*GetPrefs())) {
-    RecordApkDownloadTelemetryOutcome(
-        ApkDownloadTelemetryOutcome::NOT_SENT_SAFE_BROWSING_NOT_ENABLED);
-    return false;
-  }
+  RecordApkDownloadTelemetryOutcome(
+      ApkDownloadTelemetryOutcome::NOT_SENT_SAFE_BROWSING_NOT_ENABLED);
+  return false;
 
   if (profile_->IsOffTheRecord()) {
     RecordApkDownloadTelemetryOutcome(
--- a/chrome/browser/safe_browsing/telemetry/android/android_telemetry_service_unittest.cc
+++ b/chrome/browser/safe_browsing/telemetry/android/android_telemetry_service_unittest.cc
@@ -21,7 +21,6 @@
 #include "components/safe_browsing/android/safe_browsing_api_handler_bridge.h"
 #include "components/safe_browsing/android/safe_browsing_api_handler_util.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/browser_task_traits.h"
 #include "content/public/browser/browser_thread.h"
 #include "content/public/browser/download_item_utils.h"
--- a/chrome/browser/safe_browsing/url_checker_delegate_impl.cc
+++ b/chrome/browser/safe_browsing/url_checker_delegate_impl.cc
@@ -23,7 +23,6 @@
 #include "components/safe_browsing/core/browser/db/database_manager.h"
 #include "components/safe_browsing/core/browser/db/v4_protocol_manager_util.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/browser_task_traits.h"
 #include "content/public/browser/browser_thread.h"
 #include "content/public/browser/navigation_entry.h"
--- a/chrome/browser/send_tab_to_self/send_tab_to_self_util.cc
+++ b/chrome/browser/send_tab_to_self/send_tab_to_self_util.cc
@@ -11,7 +11,6 @@
 #include "components/send_tab_to_self/features.h"
 #include "components/send_tab_to_self/send_tab_to_self_model.h"
 #include "components/send_tab_to_self/send_tab_to_self_sync_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/sync/service/sync_service.h"
 #include "content/public/browser/web_contents.h"
--- a/chrome/browser/signin/account_consistency_mode_manager.cc
+++ b/chrome/browser/signin/account_consistency_mode_manager.cc
@@ -24,7 +24,6 @@
 #include "components/pref_registry/pref_registry_syncable.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/signin_buildflags.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "google_apis/google_api_keys.h"
 
 #if BUILDFLAG(IS_CHROMEOS)
@@ -94,21 +93,6 @@ AccountConsistencyModeManager::AccountCo
                           ->GetProfileAttributesStorage()
                           .GetProfileAttributesWithPath(profile_->GetPath())
                     : nullptr;
-  PrefService* prefs = profile_->GetPrefs();
-  // Propagate settings changes from the previous launch to the signin-allowed
-  // pref.
-  bool signin_allowed = IsDiceSignInAllowed(entry) &&
-                        prefs->GetBoolean(prefs::kSigninAllowedOnNextStartup);
-
-  // Disable sign-in if experimental-ai is enabled, regardless of channel.
-  auto* command_line = base::CommandLine::ForCurrentProcess();
-  if (command_line->HasSwitch(::switches::kExperimentalAiStableChannel)) {
-    signin_allowed = false;
-  }
-
-  prefs->SetBoolean(prefs::kSigninAllowed, signin_allowed);
-
-  UMA_HISTOGRAM_BOOLEAN("Signin.SigninAllowed", signin_allowed);
 #endif
 
   account_consistency_ = ComputeAccountConsistencyMethod(profile_);
@@ -121,7 +105,6 @@ AccountConsistencyModeManager::~AccountC
 // static
 void AccountConsistencyModeManager::RegisterProfilePrefs(
     user_prefs::PrefRegistrySyncable* registry) {
-  registry->RegisterBooleanPref(prefs::kSigninAllowedOnNextStartup, true);
 }
 
 // static
@@ -203,13 +186,9 @@ AccountConsistencyModeManager::ComputeAc
 #if BUILDFLAG(ENABLE_MIRROR)
   return AccountConsistencyMethod::kMirror;
 #elif BUILDFLAG(ENABLE_DICE_SUPPORT)
-  if (!profile->GetPrefs()->GetBoolean(prefs::kSigninAllowed)) {
     VLOG(1) << "Desktop Identity Consistency disabled as sign-in to Chrome "
                "is not allowed";
     return AccountConsistencyMethod::kDisabled;
-  }
-
-  return AccountConsistencyMethod::kDice;
 #else
   NOTREACHED();
 #endif
--- a/chrome/browser/signin/account_consistency_mode_manager_unittest.cc
+++ b/chrome/browser/signin/account_consistency_mode_manager_unittest.cc
@@ -19,7 +19,6 @@
 #include "components/prefs/testing_pref_store.h"
 #include "components/signin/public/base/account_consistency_method.h"
 #include "components/signin/public/base/signin_buildflags.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/sync_preferences/testing_pref_service_syncable.h"
 #include "content/public/test/browser_task_environment.h"
 #include "testing/gtest/include/gtest/gtest.h"
--- a/chrome/browser/signin/accounts_policy_manager.cc
+++ b/chrome/browser/signin/accounts_policy_manager.cc
@@ -21,7 +21,6 @@
 #include "chrome/grit/generated_resources.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/signin/public/identity_manager/identity_utils.h"
@@ -197,26 +196,12 @@ void AccountsPolicyManager::Initialize()
   EnsurePrimaryAccountAllowedForProfile(
       profile_, signin_metrics::ProfileSignout::kSigninNotAllowedOnProfileInit);
 
-  signin_allowed_.Init(
-      prefs::kSigninAllowed, profile_->GetPrefs(),
-      base::BindRepeating(&AccountsPolicyManager::OnSigninAllowedPrefChanged,
-                          weak_pointer_factory_.GetWeakPtr()));
-
   local_state_pref_registrar_.Init(g_browser_process->local_state());
-  local_state_pref_registrar_.Add(
-      prefs::kGoogleServicesUsernamePattern,
-      base::BindRepeating(
-          &AccountsPolicyManager::OnGoogleServicesUsernamePatternChanged,
-          weak_pointer_factory_.GetWeakPtr()));
 
 #if BUILDFLAG(IS_WIN) || BUILDFLAG(IS_MAC) || BUILDFLAG(IS_LINUX)
   auto* identity_manager = IdentityManagerFactory::GetForProfile(profile_);
   identity_manager_observation_.Observe(identity_manager);
   profile_pref_change_registrar_.Init(profile_->GetPrefs());
-  profile_pref_change_registrar_.Add(
-      prefs::kProfileSeparationDomainExceptionList,
-      base::BindRepeating(&AccountsPolicyManager::RemoveUnallowedAccounts,
-                          weak_pointer_factory_.GetWeakPtr()));
   if (identity_manager->AreRefreshTokensLoaded()) {
     OnRefreshTokensLoaded();
   }
@@ -228,7 +213,6 @@ void AccountsPolicyManager::Shutdown() {
   profile_pref_change_registrar_.RemoveAll();
 #endif  // BUILDFLAG(IS_WIN) || BUILDFLAG(IS_MAC) || BUILDFLAG(IS_LINUX)
   local_state_pref_registrar_.RemoveAll();
-  signin_allowed_.Destroy();
 }
 
 void AccountsPolicyManager::OnGoogleServicesUsernamePatternChanged() {
@@ -255,11 +239,6 @@ void AccountsPolicyManager::EnsurePrimar
 
   CoreAccountInfo primary_account =
       identity_manager->GetPrimaryAccountInfo(signin::ConsentLevel::kSync);
-  if (profile->GetPrefs()->GetBoolean(prefs::kSigninAllowed) &&
-      signin::IsUsernameAllowedByPatternFromPrefs(
-          g_browser_process->local_state(), primary_account.email)) {
-    return;
-  }
 
   if (ChromeSigninClientFactory::GetForProfile(profile)
           ->IsClearPrimaryAccountAllowed(identity_manager->HasPrimaryAccount(
--- a/chrome/browser/signin/accounts_policy_manager.h
+++ b/chrome/browser/signin/accounts_policy_manager.h
@@ -73,9 +73,6 @@ class AccountsPolicyManager : public Key
 
   raw_ptr<Profile> profile_;
 
-  // Helper object to listen for changes to the signin allowed preference.
-  BooleanPrefMember signin_allowed_;
-
   // Helper object to listen for changes to signin preferences stored in non-
   // profile-specific local prefs (like kGoogleServicesUsernamePattern).
   PrefChangeRegistrar local_state_pref_registrar_;
--- a/chrome/browser/signin/android/signin_manager_android.cc
+++ b/chrome/browser/signin/android/signin_manager_android.cc
@@ -33,7 +33,6 @@
 #include "components/policy/core/common/cloud/user_cloud_policy_manager.h"
 #include "components/policy/core/common/policy_switches.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_managed_status_finder.h"
 #include "components/signin/public/identity_manager/accounts_cookie_mutator.h"
@@ -116,11 +115,6 @@ class ProfileDataRemover : public conten
       // All the Profile data has been wiped. Clear the last signed in username
       // as well, so that the next signin doesn't trigger the account
       // change dialog.
-      profile_->GetPrefs()->ClearPref(prefs::kGoogleServicesLastSyncingGaiaId);
-      profile_->GetPrefs()->ClearPref(
-          prefs::kGoogleServicesLastSyncingUsername);
-      profile_->GetPrefs()->ClearPref(
-          prefs::kGoogleServicesLastSignedInUsername);
     }
 
     origin_runner_->PostTask(FROM_HERE, std::move(callback_));
--- a/chrome/browser/signin/bound_session_credentials/bound_session_cookie_refresh_service_factory.cc
+++ b/chrome/browser/signin/bound_session_credentials/bound_session_cookie_refresh_service_factory.cc
@@ -19,7 +19,6 @@
 #include "chrome/browser/signin/bound_session_credentials/unexportable_key_service_factory.h"
 #include "components/pref_registry/pref_registry_syncable.h"
 #include "components/signin/public/base/account_consistency_method.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "content/public/browser/network_service_instance.h"
 
@@ -112,7 +111,4 @@ BoundSessionCookieRefreshServiceFactory:
 void BoundSessionCookieRefreshServiceFactory::RegisterProfilePrefs(
     user_prefs::PrefRegistrySyncable* registry) {
   BoundSessionParamsStorage::RegisterProfilePrefs(registry);
-  // Default value for this pref doesn't matter since it is only used when
-  // explicitly set.
-  registry->RegisterBooleanPref(prefs::kBoundSessionCredentialsEnabled, false);
 }
--- a/chrome/browser/signin/chrome_device_id_helper.cc
+++ b/chrome/browser/signin/chrome_device_id_helper.cc
@@ -18,7 +18,6 @@
 #include "chrome/browser/ash/profiles/profile_helper.h"
 #include "chrome/browser/browser_process.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/user_manager/known_user.h"
 #include "components/user_manager/user_manager.h"
@@ -87,21 +86,11 @@ void MigrateSigninScopedDeviceId(Profile
   user_manager::KnownUser known_user(g_browser_process->local_state());
   const AccountId account_id = user->GetAccountId();
   if (known_user.GetDeviceId(account_id).empty()) {
-    const std::string legacy_device_id = profile->GetPrefs()->GetString(
-        prefs::kGoogleServicesSigninScopedDeviceId);
-    if (!legacy_device_id.empty()) {
-      // Need to move device ID from the old location to the new one, if it has
-      // not been done yet.
-      known_user.SetDeviceId(account_id, legacy_device_id);
-    } else {
-      known_user.SetDeviceId(
-          account_id, GenerateSigninScopedDeviceId(
-                          user_manager::UserManager::Get()
-                              ->IsUserNonCryptohomeDataEphemeral(account_id)));
-    }
+    known_user.SetDeviceId(
+        account_id, GenerateSigninScopedDeviceId(
+                        user_manager::UserManager::Get()
+                            ->IsUserNonCryptohomeDataEphemeral(account_id)));
   }
-  profile->GetPrefs()->SetString(prefs::kGoogleServicesSigninScopedDeviceId,
-                                 std::string());
 }
 
 #endif  // BUILDFLAG(IS_CHROMEOS)
--- a/chrome/browser/signin/chrome_signin_client.cc
+++ b/chrome/browser/signin/chrome_signin_client.cc
@@ -48,7 +48,6 @@
 #include "components/signin/public/base/signin_buildflags.h"
 #include "components/signin/public/base/signin_client.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/access_token_info.h"
--- a/chrome/browser/signin/chromeos_mirror_account_consistency_browsertest.cc
+++ b/chrome/browser/signin/chromeos_mirror_account_consistency_browsertest.cc
@@ -18,7 +18,6 @@
 #include "components/policy/core/common/policy_pref_names.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/core/browser/signin_header_helper.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_test_utils.h"
 #include "components/supervised_user/core/browser/supervised_user_preferences.h"
 #include "components/user_manager/user.h"
--- a/chrome/browser/signin/dice_browsertest.cc
+++ b/chrome/browser/signin/dice_browsertest.cc
@@ -82,7 +82,6 @@
 #include "components/signin/public/base/signin_buildflags.h"
 #include "components/signin/public/base/signin_client.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_capabilities_test_mutator.h"
--- a/chrome/browser/signin/dice_signed_in_profile_creator.cc
+++ b/chrome/browser/signin/dice_signed_in_profile_creator.cc
@@ -20,7 +20,6 @@
 #include "chrome/browser/signin/identity_manager_factory.h"
 #include "chrome/browser/signin/signin_util.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/accounts_mutator.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "content/public/browser/storage_partition.h"
--- a/chrome/browser/signin/dice_web_signin_interceptor.cc
+++ b/chrome/browser/signin/dice_web_signin_interceptor.cc
@@ -73,7 +73,6 @@
 #include "components/search_engines/template_url_service.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/identity_manager/account_capabilities.h"
 #include "components/signin/public/identity_manager/account_info.h"
@@ -462,16 +461,8 @@ void DiceWebSigninInterceptor::RegisterP
   registry->RegisterBooleanPref(prefs::kSigninInterceptionEnabled, true);
   registry->RegisterStringPref(prefs::kManagedAccountsSigninRestriction,
                                std::string());
-  registry->RegisterStringPref(prefs::kSigninInterceptionIDPCookiesUrl,
-                               std::string());
   registry->RegisterBooleanPref(
       prefs::kManagedAccountsSigninRestrictionScopeMachine, false);
-  registry->RegisterIntegerPref(prefs::kProfileSeparationSettings, 0);
-  registry->RegisterIntegerPref(prefs::kProfileSeparationDataMigrationSettings,
-                                1);
-  registry->RegisterListPref(prefs::kProfileSeparationDomainExceptionList);
-  registry->RegisterStringPref(
-      prefs::kUserCloudSigninPolicyResponseFromPolicyTestPage, std::string());
 }
 
 std::optional<SigninInterceptionHeuristicOutcome>
@@ -1504,16 +1495,6 @@ void DiceWebSigninInterceptor::
           g_browser_process->browser_policy_connector(),
           g_browser_process->system_network_context_manager()
               ->GetSharedURLLoaderFactory());
-  state_->account_level_signin_restriction_policy_fetcher_
-      ->GetManagedAccountsSigninRestriction(
-          identity_manager_, account_info.account_id, std::move(callback),
-          policy::utils::IsPolicyTestingEnabled(profile_->GetPrefs(),
-                                                chrome::GetChannel())
-              ? profile_->GetPrefs()
-                    ->GetDefaultPrefValue(
-                        prefs::kUserCloudSigninPolicyResponseFromPolicyTestPage)
-                    ->GetString()
-              : std::string());
 }
 
 void DiceWebSigninInterceptor::
--- a/chrome/browser/signin/header_modification_delegate_impl.cc
+++ b/chrome/browser/signin/header_modification_delegate_impl.cc
@@ -14,7 +14,6 @@
 #include "chrome/browser/sync/sync_service_factory.h"
 #include "components/policy/core/common/policy_pref_names.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
@@ -162,7 +161,7 @@ void HeaderModificationDelegateImpl::Pro
       // the sync feature is enabled, which in particular triggers a
       // confirmation web page on signout.
       sync_service && sync_service->IsSyncFeatureEnabled(),
-      prefs->GetString(prefs::kGoogleServicesSigninScopedDeviceId),
+      std::string(),
 #endif
       cookie_settings_.get());
 }
--- a/chrome/browser/signin/mirror_browsertest.cc
+++ b/chrome/browser/signin/mirror_browsertest.cc
@@ -33,7 +33,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/signin/core/browser/dice_header_helper.h"
 #include "components/signin/core/browser/signin_header_helper.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "content/public/common/content_client.h"
 #include "content/public/test/browser_test.h"
 #include "google_apis/gaia/gaia_urls.h"
--- a/chrome/browser/signin/signin_manager.cc
+++ b/chrome/browser/signin/signin_manager.cc
@@ -16,7 +16,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/signin_client.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
@@ -63,10 +62,6 @@ SigninManager::SigninManager(PrefService
     : prefs_(prefs),
       signin_client_(client),
       identity_manager_(identity_manager) {
-  signin_allowed_.Init(
-      prefs::kSigninAllowed, &prefs,
-      base::BindRepeating(&SigninManager::OnSigninAllowedPrefChanged,
-                          base::Unretained(this)));
   UpdateUnconsentedPrimaryAccount();
   identity_manager_observation_.Observe(&identity_manager_.get());
 }
@@ -150,16 +145,6 @@ CoreAccountInfo SigninManager::ComputeUn
         signin::ConsentLevel::kSync);
   }
 
-  // Clearing the primary sync account when sign-in is not allowed is handled
-  // by PrimaryAccountPolicyManager. That flow is extremely hard to follow
-  // especially for the case when the user is syncing with a managed account
-  // as in that case the whole profile needs to be deleted.
-  //
-  // It was considered simpler to keep the logic to update the unconsented
-  // primary account in a single place.
-  if (!signin_allowed_.GetValue()) {
-    return CoreAccountInfo();
-  }
 
   bool is_current_primary_account_valid =
       IsValidUnconsentedPrimaryAccount(current_primary_account);
--- a/chrome/browser/signin/signin_promo_util.cc
+++ b/chrome/browser/signin/signin_promo_util.cc
@@ -12,7 +12,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
@@ -170,84 +169,14 @@ namespace signin {
 
 #if !BUILDFLAG(IS_ANDROID)
 bool ShouldShowSyncPromo(Profile& profile) {
-#if BUILDFLAG(IS_CHROMEOS)
-  // There's no need to show the sign in promo on cros since cros users are
-  // already logged in.
-  return false;
-#else
-
-  // Don't bother if we don't have any kind of network connection.
-  if (net::NetworkChangeNotifier::IsOffline()) {
-    return false;
-  }
-
-  // Consider original profile even if an off-the-record profile was
-  // passed to this method as sign-in state is only defined for the
-  // primary profile.
-  Profile* original_profile = profile.GetOriginalProfile();
-
-  // Don't show for supervised child profiles.
-  if (original_profile->IsChild()) {
-    return false;
-  }
-
-  // Don't show if sign in is not allowed.
-  if (!original_profile->GetPrefs()->GetBoolean(prefs::kSigninAllowed)) {
-    return false;
-  }
-
-  signin::IdentityManager* identity_manager =
-      IdentityManagerFactory::GetForProfile(original_profile);
-
-  // No promo if the user is already syncing.
-  if (identity_manager->HasPrimaryAccount(signin::ConsentLevel::kSync)) {
-    return false;
-  }
-
-  syncer::SyncPrefs prefs(profile.GetPrefs());
-  // Don't show if sync is not allowed to start or is running in local mode.
-  if (!SyncServiceFactory::IsSyncAllowed(&profile) ||
-      prefs.IsLocalSyncEnabled()) {
     return false;
-  }
-
-  // Verified the base checks. Depending on whether the promo should be for sync
-  // or signin, additional checks are necessary.
-  return true;
-#endif
 }
 #endif  // !BUILDFLAG(IS_ANDROID)
 
 #if BUILDFLAG(ENABLE_EXTENSIONS)
 bool ShouldShowExtensionSyncPromo(Profile& profile,
                                   const extensions::Extension& extension) {
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-  // Don't show the promo if it does not pass the sync base checks.
-  if (!signin::ShouldShowSyncPromo(profile)) {
-    return false;
-  }
-
-  if (!extensions::sync_util::ShouldSync(&profile, &extension)) {
-    return false;
-  }
-
-  // `ShouldShowSyncPromo()` does not check if extensions are syncing in
-  // transport mode. That's why `IsSyncingExtensionsEnabled()` is added so the
-  // sign in promo is not shown in that case.
-  if (extensions::sync_util::IsSyncingExtensionsEnabled(&profile)) {
-    return false;
-  }
-
-  // The promo is not shown to users that have explicitly signed in through the
-  // browser (even if extensions are not syncing).
-  if (profile.GetPrefs()->GetBoolean(prefs::kExplicitBrowserSignin)) {
-    return false;
-  }
-
-  return true;
-#else
   return false;
-#endif  // BUILDFLAG(ENABLE_DICE_SUPPORT)
 }
 
 bool ShouldShowExtensionSignInPromo(Profile& profile,
@@ -295,43 +224,7 @@ bool ShouldShowAddressSignInPromo(Profil
 }
 
 bool ShouldShowBookmarkSignInPromo(Profile& profile) {
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-  if (!base::FeatureList::IsEnabled(
-          switches::kSyncEnableBookmarksInTransportMode)) {
-    return false;
-  }
-
-  // Do not show the promo if a user was previously syncing, as this may result
-  // in duplicate data.
-  // TODO(crbug.com/402748138): Remove this once bookmarks de-duplication is
-  // implemented.
-  if (!profile.GetPrefs()
-           ->GetString(::prefs::kGoogleServicesLastSyncingGaiaId)
-           .empty()) {
-    return false;
-  }
-
-  if (!ShouldShowSignInPromoCommon(profile, SignInPromoType::kBookmark)) {
-    return false;
-  }
-
-  // At this point, both the identity manager and sync service should not be
-  // null.
-  IdentityManager* identity_manager =
-      IdentityManagerFactory::GetForProfile(&profile);
-  syncer::SyncService* sync_service =
-      SyncServiceFactory::GetForProfile(&profile);
-  CHECK(identity_manager);
-  CHECK(sync_service);
-
-  // If the user is in sign in pending state, the promo should only be shown if
-  // they already have account storage for bookmarks enabled.
-  return !signin_util::IsSigninPending(identity_manager) ||
-         sync_service->GetUserSettings()->GetSelectedTypes().Has(
-             syncer::UserSelectableType::kBookmarks);
-#else
   return false;
-#endif  // BUILDFLAG(ENABLE_DICE_SUPPORT)
 }
 
 bool IsAutofillSigninPromo(signin_metrics::AccessPoint access_point) {
--- a/chrome/browser/signin/signin_ui_util.cc
+++ b/chrome/browser/signin/signin_ui_util.cc
@@ -41,7 +41,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
--- a/chrome/browser/signin/signin_util.cc
+++ b/chrome/browser/signin/signin_util.cc
@@ -34,7 +34,6 @@
 #include "components/policy/core/browser/signin/profile_separation_policies.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/core/browser/account_reconcilor.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_managed_status_finder.h"
 #include "components/signin/public/identity_manager/accounts_mutator.h"
@@ -79,8 +78,7 @@ ScopedForceSigninSetterForTesting::~Scop
 CookiesMover::CookiesMover(base::WeakPtr<Profile> source_profile,
                            base::WeakPtr<Profile> destination_profile,
                            base::OnceCallback<void()> callback)
-    : url_(source_profile->GetPrefs()->GetString(
-          prefs::kSigninInterceptionIDPCookiesUrl)),
+    : url_(std::string()),
       source_profile_(std::move(source_profile)),
       destination_profile_(std::move(destination_profile)),
       callback_(std::move(callback)) {
@@ -90,25 +88,7 @@ CookiesMover::CookiesMover(base::WeakPtr
 CookiesMover::~CookiesMover() = default;
 
 void CookiesMover::StartMovingCookies() {
-#if BUILDFLAG(IS_MAC) || BUILDFLAG(IS_LINUX) || BUILDFLAG(IS_WIN)
-  bool allow_cookies_to_be_moved = base::FeatureList::IsEnabled(
-      profile_management::features::kThirdPartyProfileManagement);
-#else
-  bool allow_cookies_to_be_moved = false;
-#endif
-  if (!allow_cookies_to_be_moved || url_.is_empty() || !url_.is_valid()) {
     std::move(callback_).Run();
-    return;
-  }
-
-  source_profile_->GetPrefs()->ClearPref(
-      prefs::kSigninInterceptionIDPCookiesUrl);
-  source_profile_->GetDefaultStoragePartition()
-      ->GetCookieManagerForBrowserProcess()
-      ->GetCookieList(url_, net::CookieOptions::MakeAllInclusive(),
-                      net::CookiePartitionKeyCollection::Todo(),
-                      base::BindOnce(&CookiesMover::OnCookiesReceived,
-                                     weak_pointer_factory_.GetWeakPtr()));
 }
 
 void CookiesMover::OnCookiesReceived(
@@ -248,16 +228,7 @@ bool ProfileSeparationAllowsKeepingUnman
 bool IsAccountExemptedFromEnterpriseProfileSeparation(
     Profile* profile,
     const std::string& email) {
-  if (profile->GetPrefs()
-          ->FindPreference(prefs::kProfileSeparationDomainExceptionList)
-          ->IsDefaultValue()) {
     return true;
-  }
-
-  const std::string domain = gaia::ExtractDomainName(email);
-  const auto& allowed_domains = profile->GetPrefs()->GetList(
-      prefs::kProfileSeparationDomainExceptionList);
-  return base::Contains(allowed_domains, base::Value(domain));
 }
 #endif  // !BUILDFLAG(IS_CHROMEOS)
 
@@ -410,59 +381,7 @@ bool ShouldShowHistorySyncOptinScreen(Pr
 }
 
 bool ShouldShowAvatarSyncPromo(Profile* profile) {
-  CHECK(switches::IsAvatarSyncPromoFeatureEnabled());
-
-  // Do not show the promo for users that are not signed in. (E.g. Signed out,
-  // Signin Pending or already syncing).
-  if (GetSignedInState(IdentityManagerFactory::GetForProfile(profile)) !=
-      signin_util::SignedInState::kSignedIn) {
-    return false;
-  }
-
-  // SyncService should be usable.
-  syncer::SyncService* sync_service =
-      SyncServiceFactory::GetForProfile(profile);
-  if (!sync_service) {
     return false;
-  }
-  if (sync_service->HasDisableReason(
-          syncer::SyncService::DISABLE_REASON_ENTERPRISE_POLICY)) {
-    return false;
-  }
-
-  signin::IdentityManager* identity_manager =
-      IdentityManagerFactory::GetForProfile(profile);
-  CHECK(identity_manager->AreRefreshTokensLoaded());
-  AccountInfo account_info = identity_manager->FindExtendedAccountInfo(
-      identity_manager->GetPrimaryAccountInfo(signin::ConsentLevel::kSignin));
-  // Do not show the promo for non signed in accounts, or managed accounts.
-  if (account_info.IsEmpty() ||
-      account_info.IsManaged() != signin::Tribool::kFalse) {
-    return false;
-  }
-
-  // Do not show the promo if there was a previously syncing account that does
-  // not match the currently signed in one.
-  PrefService* pref_service = profile->GetPrefs();
-  GaiaId previously_syncing_gaia_id =
-      GaiaId(pref_service->GetString(prefs::kGoogleServicesLastSyncingGaiaId));
-  if (IsCrossAccountError(profile, account_info.gaia)) {
-    return false;
-  }
-
-  // For non-dice users, do not show the promo for users that have been signed
-  // for a short period of time.
-  if (pref_service->GetBoolean(prefs::kExplicitBrowserSignin)) {
-    const base::Time last_changed = base::Time::FromSecondsSinceUnixEpoch(
-        pref_service->GetDouble(prefs::kGaiaCookieChangedTime));
-    if (last_changed.is_null() ||
-        (base::Time::Now() - last_changed <
-         switches::GetAvatarSyncPromoFeatureMinimumCookeAgeParam())) {
-      return false;
-    }
-  }
-
-  return true;
 }
 #endif  // BUILDFLAG(IS_LINUX) ||  BUILDFLAG(IS_MAC) ||  BUILDFLAG(IS_WIN)
 
--- a/chrome/browser/signin/signin_util_win.cc
+++ b/chrome/browser/signin/signin_util_win.cc
@@ -36,7 +36,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/signin/core/browser/about_signin_internals.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/accounts_mutator.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/signin/public/identity_manager/primary_account_mutator.h"
@@ -149,9 +148,6 @@ void ImportCredentialsFromProvider(Profi
                                   account_id, profile));
     }
   }
-
-  // Mark this profile as having been signed in with the credential provider.
-  profile->GetPrefs()->SetBoolean(prefs::kSignedInWithCredentialProvider, true);
 }
 
 // Extracts the |cred_provider_gaia_id| and |cred_provider_email| for the user
@@ -328,19 +324,7 @@ bool ReauthWithCredentialProviderIfPossi
   //  - The profile is marked as having been signed in with a system credential.
   //  - The profile is already signed in.
   //  - The profile is in an auth error state.
-  auto* identity_manager = IdentityManagerFactory::GetForProfile(profile);
-  if (!(profile->GetPrefs()->GetBoolean(
-            prefs::kSignedInWithCredentialProvider) &&
-        identity_manager->HasPrimaryAccount(signin::ConsentLevel::kSync) &&
-        identity_manager->HasAccountWithRefreshTokenInPersistentErrorState(
-            identity_manager->GetPrimaryAccountId(
-                signin::ConsentLevel::kSync)))) {
     return false;
-  }
-
-  const GaiaId gaia_id =
-      identity_manager->GetPrimaryAccountInfo(signin::ConsentLevel::kSync).gaia;
-  return TrySigninWithCredentialProvider(profile, gaia_id, false);
 }
 
 }  // namespace signin_util
--- a/chrome/browser/signin/signin_util_win_browsertest.cc
+++ b/chrome/browser/signin/signin_util_win_browsertest.cc
@@ -41,7 +41,6 @@
 #include "components/keep_alive_registry/keep_alive_types.h"
 #include "components/keep_alive_registry/scoped_keep_alive.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/signin/public/identity_manager/identity_test_utils.h"
 #include "components/signin/public/identity_manager/primary_account_mutator.h"
--- a/chrome/browser/ssl/sct_reporting_service.cc
+++ b/chrome/browser/ssl/sct_reporting_service.cc
@@ -16,7 +16,6 @@
 #include "chrome/common/chrome_features.h"
 #include "chrome/common/pref_names.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/network_service_instance.h"
 #include "content/public/browser/storage_partition.h"
 #include "google_apis/google_api_keys.h"
@@ -204,18 +203,6 @@ SCTReportingService::SCTReportingService
 SCTReportingService::~SCTReportingService() = default;
 
 network::mojom::SCTAuditingMode SCTReportingService::GetReportingMode() {
-  if (profile_->IsOffTheRecord() ||
-      !base::FeatureList::IsEnabled(features::kSCTAuditing)) {
-    return network::mojom::SCTAuditingMode::kDisabled;
-  }
-  if (safe_browsing::IsSafeBrowsingEnabled(*pref_service_)) {
-    if (safe_browsing::IsExtendedReportingEnabled(*pref_service_)) {
-      return network::mojom::SCTAuditingMode::kEnhancedSafeBrowsingReporting;
-    }
-    if (base::FeatureList::IsEnabled(features::kSCTAuditingHashdance)) {
-      return network::mojom::SCTAuditingMode::kHashdance;
-    }
-  }
   return network::mojom::SCTAuditingMode::kDisabled;
 }
 
--- a/chrome/browser/ssl/ssl_browsertest.cc
+++ b/chrome/browser/ssl/ssl_browsertest.cc
@@ -99,7 +99,6 @@
 #include "components/policy/policy_constants.h"
 #include "components/prefs/testing_pref_service.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_interstitials/content/bad_clock_blocking_page.h"
 #include "components/security_interstitials/content/captive_portal_blocking_page.h"
 #include "components/security_interstitials/content/common_name_mismatch_handler.h"
--- a/chrome/browser/ssl/ssl_error_controller_client.cc
+++ b/chrome/browser/ssl/ssl_error_controller_client.cc
@@ -21,7 +21,6 @@
 #include "chrome/browser/ssl/stateful_ssl_host_state_delegate_factory.h"
 #include "chrome/common/pref_names.h"
 #include "chrome/common/url_constants.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_interstitials/content/content_metrics_helper.h"
 #include "components/security_interstitials/content/settings_page_helper.h"
 #include "components/security_interstitials/content/stateful_ssl_host_state_delegate.h"
--- a/chrome/browser/ui/autofill/payments/save_card_bubble_controller_impl.cc
+++ b/chrome/browser/ui/autofill/payments/save_card_bubble_controller_impl.cc
@@ -48,7 +48,6 @@
 #include "components/autofill/core/common/autofill_features.h"
 #include "components/autofill/core/common/autofill_payments_features.h"
 #include "components/signin/public/base/signin_buildflags.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/strings/grit/components_strings.h"
 #include "components/sync/service/sync_service.h"
--- a/chrome/browser/ui/browser_command_controller.cc
+++ b/chrome/browser/ui/browser_command_controller.cc
@@ -94,7 +94,6 @@
 #include "components/sessions/core/tab_restore_service.h"
 #include "components/signin/public/base/signin_buildflags.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/translate/core/browser/translate_manager.h"
 #include "content/public/browser/navigation_controller.h"
--- a/chrome/browser/ui/browser_command_controller_unittest.cc
+++ b/chrome/browser/ui/browser_command_controller_unittest.cc
@@ -34,7 +34,6 @@
 #include "components/input/native_web_keyboard_event.h"
 #include "components/performance_manager/public/features.h"
 #include "components/policy/core/common/policy_pref_names.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "content/public/test/web_contents_tester.h"
 #include "extensions/buildflags/buildflags.h"
 #include "ui/events/keycodes/dom/dom_code.h"
--- a/chrome/browser/ui/chrome_pages.cc
+++ b/chrome/browser/ui/chrome_pages.cc
@@ -81,7 +81,6 @@
 #if !BUILDFLAG(IS_ANDROID)
 #include "base/metrics/histogram_functions.h"
 #include "chrome/browser/signin/identity_manager_factory.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #endif
 
--- a/chrome/browser/ui/extensions/extension_installed_bubble_model.cc
+++ b/chrome/browser/ui/extensions/extension_installed_bubble_model.cc
@@ -14,7 +14,6 @@
 #include "chrome/grit/branded_strings.h"
 #include "chrome/grit/generated_resources.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "extensions/common/api/extension_action/action_info.h"
 #include "extensions/common/command.h"
 #include "extensions/common/extension.h"
--- a/chrome/browser/ui/hats/trust_safety_sentiment_service.cc
+++ b/chrome/browser/ui/hats/trust_safety_sentiment_service.cc
@@ -31,8 +31,6 @@
 #include "components/privacy_sandbox/privacy_sandbox_prefs.h"
 #include "components/privacy_sandbox/tracking_protection_prefs.h"
 #include "components/safe_browsing/core/browser/db/v4_protocol_manager_util.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/unified_consent/pref_names.h"
 #include "components/version_info/channel.h"
 
@@ -78,9 +76,6 @@ bool HasNonDefaultPrivacySetting(Profile
   auto* prefs = profile->GetPrefs();
 
   std::vector<std::string> prefs_to_check = {
-      prefs::kSafeBrowsingEnabled,
-      prefs::kSafeBrowsingEnhanced,
-      prefs::kSafeBrowsingScoutReportingEnabled,
       prefs::kEnableDoNotTrack,
       password_manager::prefs::kPasswordLeakDetectionEnabled,
       prefs::kCookieControlsMode,
@@ -98,13 +93,9 @@ bool HasNonDefaultPrivacySetting(Profile
   // Users consenting to sync automatically enable UKM collection
   auto* ukm_pref = prefs->FindPreference(
       unified_consent::prefs::kUrlKeyedAnonymizedDataCollectionEnabled);
-  auto* sync_consent_pref =
-      prefs->FindPreference(prefs::kGoogleServicesConsentedToSync);
 
   bool has_non_default_ukm =
-      ukm_pref->GetValue()->GetBool() !=
-          sync_consent_pref->GetValue()->GetBool() &&
-      (ukm_pref->IsUserControlled() || sync_consent_pref->IsUserControlled());
+      ukm_pref->GetValue()->GetBool() != false && ukm_pref->IsUserControlled();
 
   // Check the default value for each user facing content setting. Note that
   // this will not include content setting exceptions set via permission
@@ -177,8 +168,6 @@ std::map<std::string, bool> BuildProduct
     PasswordProtectionUIType ui_type,
     PasswordProtectionUIAction action) {
   std::map<std::string, bool> product_specific_data;
-  product_specific_data["Enhanced protection enabled"] =
-      safe_browsing::IsEnhancedProtectionEnabled(*profile->GetPrefs());
   product_specific_data["Is page info UI"] = false;
   product_specific_data["Is modal dialog UI"] = false;
   product_specific_data["Is interstitial UI"] = false;
@@ -440,31 +429,12 @@ void TrustSafetySentimentService::Finish
 void TrustSafetySentimentService::InteractedWithSafeBrowsingInterstitial(
     bool did_proceed,
     safe_browsing::SBThreatType threat_type) {
-  std::map<std::string, bool> product_specific_data;
-  product_specific_data["User proceeded past interstitial"] = did_proceed;
-  product_specific_data["Enhanced protection enabled"] =
-      safe_browsing::IsEnhancedProtectionEnabled(*profile_->GetPrefs());
-  product_specific_data["Threat is phishing"] =
-      threat_type == safe_browsing::SBThreatType::SB_THREAT_TYPE_URL_PHISHING ||
-      threat_type ==
-          safe_browsing::SBThreatType::SB_THREAT_TYPE_URL_CLIENT_SIDE_PHISHING;
-  product_specific_data["Threat is malware"] =
-      threat_type == safe_browsing::SBThreatType::SB_THREAT_TYPE_URL_MALWARE;
-  product_specific_data["Threat is unwanted software"] =
-      threat_type == safe_browsing::SBThreatType::SB_THREAT_TYPE_URL_UNWANTED;
-  product_specific_data["Threat is billing"] =
-      threat_type == safe_browsing::SBThreatType::SB_THREAT_TYPE_BILLING;
-  DCHECK(!IsOtherSBInterstitialCategory(threat_type));
-  TriggerOccurred(FeatureArea::kSafeBrowsingInterstitial,
-                  product_specific_data);
 }
 
 void TrustSafetySentimentService::InteractedWithDownloadWarningUI(
     DownloadItemWarningData::WarningSurface surface,
     DownloadItemWarningData::WarningAction action) {
   std::map<std::string, bool> product_specific_data;
-  product_specific_data["Enhanced protection enabled"] =
-      safe_browsing::IsEnhancedProtectionEnabled(*profile_->GetPrefs());
   product_specific_data["Is mainpage UI"] = false;
   product_specific_data["Is downloads page UI"] = false;
   product_specific_data["Is download prompt UI"] = false;
--- a/chrome/browser/ui/safety_hub/menu_notification_service.cc
+++ b/chrome/browser/ui/safety_hub/menu_notification_service.cc
@@ -22,7 +22,6 @@
 #include "chrome/browser/ui/safety_hub/safety_hub_service.h"
 #include "chrome/common/chrome_features.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 #if BUILDFLAG(IS_ANDROID)
 #include "chrome/browser/ui/safety_hub/password_status_check_result_android.h"
@@ -134,14 +133,6 @@ SafetyHubMenuNotificationService::Safety
   }
 #endif  // !BUILDFLAG(IS_ANDROID)
 
-  // Listen for changes to the Safe Browsing pref to accommodate the trigger
-  // logic.
-  registrar_.Init(pref_service);
-  registrar_.Add(
-      prefs::kSafeBrowsingEnabled,
-      base::BindRepeating(
-          &SafetyHubMenuNotificationService::OnSafeBrowsingPrefUpdate,
-          base::Unretained(this)));
 }
 
 void SafetyHubMenuNotificationService::UpdateResultGetterForTesting(
--- a/chrome/browser/ui/safety_hub/safe_browsing_result.cc
+++ b/chrome/browser/ui/safety_hub/safe_browsing_result.cc
@@ -12,7 +12,6 @@
 #include "chrome/browser/ui/safety_hub/safety_hub_result.h"
 #include "chrome/grit/generated_resources.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "ui/base/l10n/l10n_util.h"
 
 #if !BUILDFLAG(IS_ANDROID)
@@ -54,18 +53,6 @@ SafetyHubSafeBrowsingResult::GetResult(c
 // static
 SafeBrowsingState SafetyHubSafeBrowsingResult::GetState(
     const PrefService* pref_service) {
-  if (safe_browsing::IsEnhancedProtectionEnabled(*pref_service)) {
-    return SafeBrowsingState::kEnabledEnhanced;
-  }
-  if (safe_browsing::IsSafeBrowsingEnabled(*pref_service)) {
-    return SafeBrowsingState::kEnabledStandard;
-  }
-  if (safe_browsing::IsSafeBrowsingPolicyManaged(*pref_service)) {
-    return SafeBrowsingState::kDisabledByAdmin;
-  }
-  if (safe_browsing::IsSafeBrowsingExtensionControlled(*pref_service)) {
-    return SafeBrowsingState::kDisabledByExtension;
-  }
   return SafeBrowsingState::kDisabledByUser;
 }
 
--- a/chrome/browser/ui/signin/cookie_clear_on_exit_migration_notice.cc
+++ b/chrome/browser/ui/signin/cookie_clear_on_exit_migration_notice.cc
@@ -23,7 +23,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_buildflags.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/strings/grit/components_strings.h"
 #include "ui/base/l10n/l10n_util.h"
@@ -67,7 +66,6 @@ void OpenCookieSettingsAndCloseDialog(Br
 }
 
 bool SetCookieClearOnExitMigrationComplete(PrefService& prefs, bool can_close) {
-  prefs.SetBoolean(prefs::kCookieClearOnExitMigrationNoticeComplete, true);
   return can_close;
 }
 
@@ -78,11 +76,6 @@ bool SetCookieClearOnExitMigrationComple
 bool CanShowCookieClearOnExitMigrationNotice(const Browser& browser) {
 #if BUILDFLAG(ENABLE_DICE_SUPPORT)
   Profile* profile = browser.profile();
-  PrefService* prefs = profile->GetPrefs();
-
-  if (prefs->GetBoolean(prefs::kCookieClearOnExitMigrationNoticeComplete)) {
-    return false;
-  }
 
   if (CookieClearOnExitMigrationNoticeShowingUserData::HasForProfile(
           *profile)) {
@@ -96,10 +89,6 @@ bool CanShowCookieClearOnExitMigrationNo
   // User has to be signed in with UNO (non-syncing).
   signin::IdentityManager* identity_manager =
       IdentityManagerFactory::GetForProfile(profile);
-  if (!prefs->GetBoolean(prefs::kExplicitBrowserSignin)) {
-    return false;
-  }
-
   if (identity_manager->HasPrimaryAccount(signin::ConsentLevel::kSync)) {
     return false;
   }
--- a/chrome/browser/ui/signin/dice_migration_service.cc
+++ b/chrome/browser/ui/signin/dice_migration_service.cc
@@ -24,7 +24,6 @@
 #include "chrome/grit/theme_resources.h"
 #include "components/pref_registry/pref_registry_syncable.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_managed_status_finder.h"
 #include "components/signin/public/identity_manager/account_managed_status_finder_outcome.h"
@@ -118,36 +117,7 @@ void SetBannerImage(ui::DialogModel::Bui
 }
 
 bool MaybeMigrateUser(Profile* profile) {
-  if (!IsUserEligibleForDiceMigration(profile)) {
     return false;
-  }
-  PrefService* prefs = profile->GetPrefs();
-  CHECK(prefs);
-
-  // Backup the prefs.
-  prefs->SetDict(
-      kDiceMigrationBackup,
-      base::Value::Dict()
-          .SetByDottedPath(prefs::kExplicitBrowserSignin,
-                           prefs->GetBoolean(prefs::kExplicitBrowserSignin))
-          .SetByDottedPath(
-              prefs::kPrefsThemesSearchEnginesAccountStorageEnabled,
-              prefs->GetBoolean(
-                  prefs::kPrefsThemesSearchEnginesAccountStorageEnabled)));
-
-  // TODO(crbug.com/*********): Consider calling
-  // `PrimaryAccountManager::ComputeExplicitBrowserSignin` upon explicit signin
-  // pref change.
-  prefs->SetBoolean(prefs::kPrefsThemesSearchEnginesAccountStorageEnabled,
-                    true);
-
-  prefs->SetBoolean(prefs::kExplicitBrowserSignin, true);
-
-  // Mark the migration pref as successful.
-  prefs->SetBoolean(kDiceMigrationMigrated, true);
-  // Reset the restoration pref.
-  prefs->SetBoolean(kDiceMigrationRestoredFromBackup, false);
-  return true;
 }
 
 bool MaybeShowToast(Browser* browser) {
@@ -288,23 +258,7 @@ void DiceMigrationService::RevertDiceMig
   }
 
   const bool restored_from_backup = [prefs]() -> bool {
-    const base::Value* backup = prefs->GetUserPrefValue(kDiceMigrationBackup);
-    if (!backup || !backup->is_dict()) {
       return false;
-    }
-    const std::optional<bool> prefs_account_storage_enabled =
-        backup->GetDict().FindBoolByDottedPath(
-            prefs::kPrefsThemesSearchEnginesAccountStorageEnabled);
-    const std::optional<bool> explicit_browser_signin =
-        backup->GetDict().FindBoolByDottedPath(prefs::kExplicitBrowserSignin);
-    if (!explicit_browser_signin.has_value() ||
-        !prefs_account_storage_enabled.has_value()) {
-      return false;
-    }
-    prefs->SetBoolean(prefs::kPrefsThemesSearchEnginesAccountStorageEnabled,
-                      *prefs_account_storage_enabled);
-    prefs->SetBoolean(prefs::kExplicitBrowserSignin, *explicit_browser_signin);
-    return true;
   }();
 
   prefs->SetBoolean(kDiceMigrationRestoredFromBackup, restored_from_backup);
--- a/chrome/browser/ui/signin/signin_view_controller.cc
+++ b/chrome/browser/ui/signin/signin_view_controller.cc
@@ -32,7 +32,6 @@
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_buildflags.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
@@ -355,15 +354,6 @@ void SigninViewController::SignoutOrReau
           &SigninViewController::SignoutOrReauthWithPromptWithUnsyncedDataTypes,
           weak_ptr_factory_.GetWeakPtr(), reauth_access_point,
           profile_signout_source, token_signout_source);
-  // Fetch the unsynced datatypes, as this is required to decide whether the
-  // confirmation prompt is needed.
-  if (sync_service &&
-      profile_->GetPrefs()->GetBoolean(prefs::kExplicitBrowserSignin)) {
-    sync_service->GetTypesWithUnsyncedData(
-        syncer::TypesRequiringUnsyncedDataCheckOnSignout(),
-        std::move(signout_prompt_with_datatypes));
-    return;
-  }
   // Dice users don't see the prompt, pass empty datatypes.
   std::move(signout_prompt_with_datatypes)
       .Run(absl::flat_hash_map<syncer::DataType, size_t>());
@@ -726,9 +716,7 @@ void SigninViewController::SignoutOrReau
   bool sign_out_immediately = unsynced_datatypes.empty() && needs_reauth;
 
   // Do not show the dialog to users with implicit signin.
-  if (!profile_->GetPrefs()->GetBoolean(prefs::kExplicitBrowserSignin)) {
     sign_out_immediately = true;
-  }
 
   if (ShowAccountExtensionsOnSignout(GetProfile())) {
     sign_out_immediately = false;
--- a/chrome/browser/ui/startup/first_run_service.cc
+++ b/chrome/browser/ui/startup/first_run_service.cc
@@ -28,7 +28,6 @@
 #include "components/prefs/pref_registry_simple.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
@@ -74,32 +73,7 @@ enum class PolicyEffect {
 };
 
 PolicyEffect ComputeDevicePolicyEffect(Profile& profile) {
-  const PrefService* const local_state = g_browser_process->local_state();
-  if (!local_state->GetBoolean(prefs::kPromotionsEnabled)) {
-    // Corresponding policy: PromotionsEnabled=false
-    return PolicyEffect::kDisabled;
-  }
-
-  if (!SyncServiceFactory::IsSyncAllowed(&profile)) {
-    // Corresponding policy: SyncDisabled=true
-    return PolicyEffect::kDisabled;
-  }
-
-  if (signin_util::IsForceSigninEnabled()) {
-    // Corresponding policy: BrowserSignin=2
-    // Debugging note: On Linux this policy is not supported and does not get
-    // translated to the prefs (see crbug.com/956998), but we still respond to
-    // `prefs::kForceBrowserSignin` being set (e.g. if manually edited).
-    return PolicyEffect::kDisabled;
-  }
-
-  if (!profile.GetPrefs()->GetBoolean(prefs::kSigninAllowed) ||
-      !profile.GetPrefs()->GetBoolean(prefs::kSigninAllowedOnNextStartup)) {
-    // Corresponding policy: BrowserSignin=0
-    return PolicyEffect::kDisabled;
-  }
-
-  return PolicyEffect::kNone;
+  return PolicyEffect::kDisabled;
 }
 
 void SetFirstRunFinished(FirstRunService::FinishedReason reason) {
--- a/chrome/browser/ui/toolbar/app_menu_model.cc
+++ b/chrome/browser/ui/toolbar/app_menu_model.cc
@@ -111,7 +111,6 @@
 #include "components/profile_metrics/browser_profile_type.h"
 #include "components/saved_tab_groups/public/features.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/strings/grit/components_strings.h"
 #include "components/user_education/common/feature_promo/feature_promo_controller.h"
@@ -596,54 +595,7 @@ int ProfileSubMenuModel::GetAndIncrement
 }
 
 bool ProfileSubMenuModel::BuildSyncSection() {
-  if (!profile_->GetPrefs()->GetBoolean(prefs::kSigninAllowed)) {
     return false;
-  }
-
-  if (!SyncServiceFactory::IsSyncAllowed(profile_)) {
-    return false;
-  }
-
-  signin::IdentityManager* identity_manager =
-      IdentityManagerFactory::GetForProfile(profile_);
-  AddTitle(GetSyncSectionTitle(profile_, identity_manager));
-
-  // First, check for sync errors. They may exist even if sync-the-feature is
-  // disabled and only sync-the-transport is running.
-  const std::optional<AvatarSyncErrorType> error =
-      GetAvatarSyncErrorType(profile_);
-  if (error.has_value()) {
-    if (error == AvatarSyncErrorType::kSyncPaused) {
-      if (identity_manager->HasPrimaryAccount(signin::ConsentLevel::kSync)) {
-        // If sync is paused the menu item will be specific to the paused error.
-        AddItemWithStringIdAndVectorIcon(
-            this, IDC_SHOW_SIGNIN_WHEN_PAUSED, IDS_PROFILE_ROW_SIGN_IN_AGAIN,
-            vector_icons::kSyncOffChromeRefreshIcon);
-      } else {
-        AddItemWithStringIdAndVectorIcon(
-            this, IDC_SHOW_SIGNIN_WHEN_PAUSED,
-            IDS_PROFILES_VERIFY_ACCOUNT_BUTTON,
-            vector_icons::kAccountCircleOffChromeRefreshIcon);
-      }
-    } else {
-      // All remaining errors will have the same menu item.
-      AddItemWithStringIdAndVectorIcon(
-          this, IDC_SHOW_SYNC_SETTINGS, IDS_PROFILE_ROW_SYNC_ERROR_MESSAGE,
-          vector_icons::kSyncProblemChromeRefreshIcon);
-    }
-    return true;
-  }
-
-  if (identity_manager->HasPrimaryAccount(signin::ConsentLevel::kSync)) {
-    AddItemWithStringIdAndVectorIcon(this, IDC_SHOW_SYNC_SETTINGS,
-                                     IDS_PROFILE_ROW_SYNC_IS_ON,
-                                     vector_icons::kSyncChromeRefreshIcon);
-  } else {
-    AddItemWithStringIdAndVectorIcon(this, IDC_TURN_ON_SYNC,
-                                     IDS_PROFILE_ROW_TURN_ON_SYNC,
-                                     vector_icons::kSyncOffChromeRefreshIcon);
-  }
-  return true;
 }
 
 void ProfileSubMenuModel::BuildGuestProfileRow(Profile* profile) {
--- a/chrome/browser/ui/views/download/bubble/download_toolbar_ui_controller.cc
+++ b/chrome/browser/ui/views/download/bubble/download_toolbar_ui_controller.cc
@@ -44,7 +44,6 @@
 #include "components/feature_engagement/public/feature_constants.h"
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/safe_browsing_policy_handler.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/browser/browser_thread.h"
 #include "ui/base/l10n/l10n_util.h"
 #include "ui/base/metadata/metadata_impl_macros.h"
--- a/chrome/browser/ui/views/page_info/page_info_bubble_view_sync_browsertest.cc
+++ b/chrome/browser/ui/views/page_info/page_info_bubble_view_sync_browsertest.cc
@@ -18,7 +18,6 @@
 #include "components/safe_browsing/content/browser/password_protection/password_protection_test_util.h"
 #include "components/safe_browsing/core/browser/password_protection/metrics_util.h"
 #include "components/security_state/content/security_state_tab_helper.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/signin/public/identity_manager/identity_test_utils.h"
--- a/chrome/browser/ui/views/profiles/avatar_toolbar_button.cc
+++ b/chrome/browser/ui/views/profiles/avatar_toolbar_button.cc
@@ -49,7 +49,6 @@
 #include "components/feature_engagement/public/feature_constants.h"
 #include "components/feature_engagement/public/tracker.h"
 #include "components/password_manager/content/common/web_ui_constants.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/tribool.h"
@@ -583,25 +582,6 @@ void AvatarToolbarButton::OnErrorStateOf
     const CoreAccountInfo& account_info,
     const GoogleServiceAuthError& error,
     signin_metrics::SourceForRefreshTokenOperation token_operation_source) {
-  Profile* profile = browser_->profile();
-  CHECK(profile);
-  PrefService* prefs = profile->GetPrefs();
-  CHECK(prefs);
-  signin::IdentityManager* identity_manager =
-      IdentityManagerFactory::GetForProfile(profile);
-  CHECK(identity_manager);
-  if (base::FeatureList::IsEnabled(
-          syncer::kReplaceSyncPromosWithSignInPromos) &&
-      prefs->GetBoolean(prefs::kExplicitBrowserSignin) &&
-      account_info == identity_manager->GetPrimaryAccountInfo(
-                          signin::ConsentLevel::kSignin) &&
-      !identity_manager->HasPrimaryAccount(signin::ConsentLevel::kSync) &&
-      error.state() ==
-          GoogleServiceAuthError::State::INVALID_GAIA_CREDENTIALS &&
-      token_operation_source == signin_metrics::SourceForRefreshTokenOperation::
-                                    kDiceResponseHandler_Signout) {
-    MaybeShowWebSignoutIPH(account_info.gaia);
-  }
 }
 
 void AvatarToolbarButton::AfterPropertyChange(const void* key,
--- a/chrome/browser/ui/views/profiles/avatar_toolbar_button_state_manager.cc
+++ b/chrome/browser/ui/views/profiles/avatar_toolbar_button_state_manager.cc
@@ -60,7 +60,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
--- a/chrome/browser/ui/views/profiles/profile_menu_view.cc
+++ b/chrome/browser/ui/views/profiles/profile_menu_view.cc
@@ -78,7 +78,6 @@
 #include "components/signin/core/browser/signin_error_controller.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/primary_account_mutator.h"
@@ -711,22 +710,6 @@ ProfileMenuView::GetIdentitySectionParam
       signin_metrics::AccessPoint::kAvatarBubbleSignIn;
   switch (signin_util::GetSignedInState(identity_manager)) {
     case signin_util::SignedInState::kSignedOut:
-      if (profile().GetPrefs()->GetBoolean(prefs::kSigninAllowed)) {
-        button_type = ActionableItem::kSigninButton;
-        access_point =
-            signin_metrics::AccessPoint::kAvatarBubbleSignInWithSyncPromo;
-        params.subtitle = l10n_util::GetStringUTF16(
-            base::FeatureList::IsEnabled(
-                syncer::kReplaceSyncPromosWithSignInPromos)
-                ? IDS_PROFILE_MENU_SIGNIN_PROMO_DESCRIPTION_WITH_BOOKMARKS
-                : IDS_PROFILE_MENU_SIGNIN_PROMO_DESCRIPTION);
-        params.button_text =
-            l10n_util::GetStringUTF16(IDS_PROFILE_MENU_SIGNIN_PROMO_BUTTON);
-        signin_metrics::LogSignInOffered(
-            explicit_signin_access_point_.value_or(access_point),
-            signin_metrics::PromoAction::
-                PROMO_ACTION_NEW_ACCOUNT_NO_EXISTING_ACCOUNT);
-      }
       break;
     case signin_util::SignedInState::kWebOnlySignedIn: {
       access_point =
@@ -908,9 +891,7 @@ void ProfileMenuView::MaybeBuildChromeAc
   // Show the settings button when signed in to Chrome or to the web, or if
   // signin is disallowed.
   const bool should_show_settings_button =
-      !identity_manager->GetExtendedAccountInfoForAccountsWithRefreshToken()
-           .empty() ||
-      !profile().GetPrefs()->GetBoolean(prefs::kSigninAllowed);
+      false;
   if (!should_show_settings_button) {
     return;
   }
--- a/chrome/browser/ui/views/profiles/profile_menu_view_browsertest.cc
+++ b/chrome/browser/ui/views/profiles/profile_menu_view_browsertest.cc
@@ -95,7 +95,6 @@
 #include "components/policy/core/common/management/scoped_management_service_override_for_testing.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/identity_test_utils.h"
 #include "components/signin/public/identity_manager/primary_account_mutator.h"
--- a/chrome/browser/ui/views/safe_browsing/password_reuse_modal_warning_dialog.cc
+++ b/chrome/browser/ui/views/safe_browsing/password_reuse_modal_warning_dialog.cc
@@ -125,7 +125,6 @@ PasswordReuseModalWarningDialog::Passwor
     OnWarningDone done_callback)
     : content::WebContentsObserver(web_contents),
       done_callback_(std::move(done_callback)),
-      service_(service),
       url_(web_contents->GetLastCommittedURL()),
       password_type_(password_type) {
   bool show_check_passwords = false;
@@ -169,31 +168,13 @@ PasswordReuseModalWarningDialog::Passwor
   SetCancelCallback(make_done_callback(WarningAction::IGNORE_WARNING));
   SetCloseCallback(make_done_callback(WarningAction::CLOSE));
 
-  // |service| maybe NULL in tests.
-  if (service_) {
-    service_->AddObserver(this);
-  }
-
-  if (password_type.account_type() ==
-      ReusedPasswordAccountType::SAVED_PASSWORD) {
-    const std::u16string message_body =
-        service_->GetWarningDetailText(password_type);
-
-    CreateSavedPasswordReuseModalWarningDialog(message_body);
-  } else {
-    views::Label* message_body_label = CreateMessageBodyLabel(
-        service_
-            ? service_->GetWarningDetailText(password_type)
-            : l10n_util::GetStringUTF16(IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS));
-    CreateGaiaPasswordReuseModalWarningDialog(message_body_label);
-  }
+  views::Label* message_body_label = CreateMessageBodyLabel(
+      l10n_util::GetStringUTF16(IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS));
+  CreateGaiaPasswordReuseModalWarningDialog(message_body_label);
   modal_construction_start_time_ = base::TimeTicks::Now();
 }
 
 PasswordReuseModalWarningDialog::~PasswordReuseModalWarningDialog() {
-  if (service_) {
-    service_->RemoveObserver(this);
-  }
   LogModalWarningDialogLifetime(modal_construction_start_time_);
 }
 
--- a/chrome/browser/ui/views/safe_browsing/password_reuse_modal_warning_dialog.h
+++ b/chrome/browser/ui/views/safe_browsing/password_reuse_modal_warning_dialog.h
@@ -65,7 +65,6 @@ class PasswordReuseModalWarningDialog
 
  private:
   OnWarningDone done_callback_;
-  raw_ptr<ChromePasswordProtectionService> service_;
   const GURL url_;
   const ReusedPasswordAccountType password_type_;
 
--- a/chrome/browser/ui/views/sync/inline_login_ui_browsertest.cc
+++ b/chrome/browser/ui/views/sync/inline_login_ui_browsertest.cc
@@ -48,7 +48,6 @@
 #include "components/keyed_service/content/browser_context_dependency_manager.h"
 #include "components/prefs/pref_service.h"
 #include "components/prefs/scoped_user_pref_update.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_test_utils.h"
 #include "content/public/browser/render_frame_host.h"
 #include "content/public/browser/render_process_host.h"
--- a/chrome/browser/ui/views/toolbar/app_menu.cc
+++ b/chrome/browser/ui/views/toolbar/app_menu.cc
@@ -61,7 +61,6 @@
 #include "chrome/grit/theme_resources.h"
 #include "components/prefs/pref_service.h"
 #include "components/saved_tab_groups/public/features.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/zoom/page_zoom.h"
 #include "components/zoom/zoom_controller.h"
@@ -386,68 +385,6 @@ void AddSignedInChipToProfileMenuItem(
     const int horizontal_padding,
     std::vector<base::CallbackListSubscription>&
         profile_menu_subscription_list) {
-  if (!profile->GetPrefs()->GetBoolean(prefs::kSigninAllowed) ||
-      profile->IsIncognitoProfile()) {
-    return;
-  }
-  constexpr int profile_chip_corner_radii = 100;
-  raw_ptr<views::Label> profile_chip_label;
-  const MenuConfig& config = MenuConfig::instance();
-
-  // We need to have the label of the profile chip within a
-  // BoxLayoutView and inside border insets because MenuItemView will
-  // layout the child items to the full height of the menu item in
-  // MenuItemView::Layout().
-  auto profile_chip =
-      views::Builder<views::BoxLayoutView>()
-          .SetInsideBorderInsets(
-              gfx::Insets::VH(config.item_vertical_margin, 0))
-          .AddChildren(
-              views::Builder<views::Label>()
-                  .SetText(GetSigninStatusChipString(profile))
-                  .CopyAddressTo(&profile_chip_label)
-                  .SetBackground(views::CreateRoundedRectBackground(
-                      item->IsSelected()
-                          ? ui::kColorAppMenuProfileRowChipHovered
-                          : ui::kColorAppMenuProfileRowChipBackground,
-                      profile_chip_corner_radii))
-                  // Add additional horizontal padding. Vertical
-                  // padding depends on menu margins to get alignment
-                  // with other items in the menu.
-                  .SetBorder(views::CreateEmptyBorder(gfx::Insets::VH(
-                      0, views::LayoutProvider::Get()
-                             ->GetInsetsMetric(views::INSETS_LABEL_BUTTON)
-                             .left()))))
-          .Build();
-
-  // MenuItemView has specific layout logic for child views which does not work
-  // very well with more custom menu items. We use this view to add the correct
-  // spacing between the profile chip and the edge of the menu.
-  auto profile_chip_edge_spacing_view =
-      views::Builder<views::View>()
-          .SetPreferredSize(gfx::Size(horizontal_padding, 0))
-          .Build();
-  profile_menu_subscription_list.push_back(
-      item->AddSelectedChangedCallback(base::BindRepeating(
-          [](MenuItemView* menu_item_view, View* child_view,
-             int corner_radius) {
-            child_view->SetBackground(views::CreateRoundedRectBackground(
-                menu_item_view->IsSelected()
-                    ? ui::kColorAppMenuProfileRowChipHovered
-                    : ui::kColorAppMenuProfileRowChipBackground,
-                corner_radius));
-          },
-          item, profile_chip_label, profile_chip_corner_radii)));
-  item->AddChildView(std::move(profile_chip));
-  item->AddChildView(std::move(profile_chip_edge_spacing_view));
-  item->SetHighlightWhenSelectedWithChildViews(true);
-  // MenuItemView only delegates accessible names when its title is empty with a
-  // single container view. The Profile MenuItemView has a title and multiple
-  // views. As a result, the accessible name must be manually computed to
-  // account for the profile chip.
-  item->GetViewAccessibility().SetName(
-      views::MenuItemView::GetAccessibleNameForMenuItem(
-          item->title(), GetSigninStatusChipString(profile), false));
 }
 
 // AppMenuView is a view that can contain label buttons.
--- a/chrome/browser/ui/views/user_education/browser_ntp_promos.cc
+++ b/chrome/browser/ui/views/user_education/browser_ntp_promos.cc
@@ -15,7 +15,6 @@
 #include "chrome/browser/user_education/ntp_promo_identifiers.h"
 #include "chrome/grit/generated_resources.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/sync/base/features.h"
 #include "components/user_education/common/ntp_promo/ntp_promo_registry.h"
@@ -31,28 +30,7 @@ namespace {
 
 NtpPromoSpecification::Eligibility CheckSignInPromoEligibility(
     Profile* profile) {
-  if (!profile->GetPrefs()->GetBoolean(prefs::kSigninAllowed)) {
     return NtpPromoSpecification::Eligibility::kIneligible;
-  }
-
-  const auto signed_in_state = signin_util::GetSignedInState(
-      IdentityManagerFactory::GetForProfile(profile));
-  switch (signed_in_state) {
-    case signin_util::SignedInState::kSignedOut:
-      // User is fully signed out.
-      return NtpPromoSpecification::Eligibility::kEligible;
-    case signin_util::SignedInState::kWebOnlySignedIn:
-      // When signed in on the web, one-click sign in options exist elsewhere
-      // in Chrome. This promo currently only offers the full-sign-in flow, so
-      // don't show it to users already signed in on the Web.
-      return NtpPromoSpecification::Eligibility::kIneligible;
-    case signin_util::SignedInState::kSignedIn:
-    case signin_util::SignedInState::kSyncing:
-    case signin_util::SignedInState::kSignInPending:
-    case signin_util::SignedInState::kSyncPaused:
-      // All other cases are considered completed.
-      return NtpPromoSpecification::Eligibility::kCompleted;
-  }
 }
 
 void SignInPromoShown() {
--- a/chrome/browser/ui/webui/browser_command/browser_command_handler.cc
+++ b/chrome/browser/ui/webui/browser_command/browser_command_handler.cc
@@ -35,7 +35,6 @@
 #include "components/performance_manager/public/features.h"
 #include "components/safe_browsing/content/browser/web_ui/safe_browsing_ui.h"
 #include "components/safe_browsing/core/common/safe_browsing_policy_handler.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_referral_methods.h"
 #include "components/saved_tab_groups/public/features.h"
 #include "components/user_education/common/tutorial/tutorial_identifier.h"
@@ -98,13 +97,6 @@ void BrowserCommandHandler::CanExecuteCo
     case Command::kOpenSafetyCheck:
       can_execute = !enterprise_util::IsBrowserManaged(profile_);
       break;
-    case Command::kOpenSafeBrowsingEnhancedProtectionSettings: {
-      bool managed = safe_browsing::SafeBrowsingPolicyHandler::
-          IsSafeBrowsingProtectionLevelSetByPolicy(profile_->GetPrefs());
-      bool already_enabled =
-          safe_browsing::IsEnhancedProtectionEnabled(*(profile_->GetPrefs()));
-      can_execute = !managed && !already_enabled;
-    } break;
     case Command::kOpenFeedbackForm:
       can_execute = true;
       break;
--- a/chrome/browser/ui/webui/cr_components/history/history_util.cc
+++ b/chrome/browser/ui/webui/cr_components/history/history_util.cc
@@ -19,7 +19,6 @@
 #include "components/history/core/common/pref_names.h"
 #include "components/history_clusters/core/history_clusters_prefs.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/strings/grit/components_strings.h"
@@ -89,7 +88,7 @@ content::WebUIDataSource* HistoryUtil::P
 
   source->AddBoolean("isGuestSession", profile->IsGuestSession());
   source->AddBoolean("isSignInAllowed",
-                     prefs->GetBoolean(prefs::kSigninAllowed));
+                     false);
 
   source->AddBoolean(kIsUserSignedInKey, IsUserSignedIn(profile));
 
--- a/chrome/browser/ui/webui/downloads/downloads_dom_handler.cc
+++ b/chrome/browser/ui/webui/downloads/downloads_dom_handler.cc
@@ -53,7 +53,6 @@
 #include "components/history/core/common/pref_names.h"
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_referral_methods.h"
 #include "content/public/browser/browser_thread.h"
 #include "content/public/browser/download_item_utils.h"
--- a/chrome/browser/ui/webui/history/history_ui.cc
+++ b/chrome/browser/ui/webui/history/history_ui.cc
@@ -54,7 +54,6 @@
 #include "components/page_image_service/image_service.h"
 #include "components/page_image_service/image_service_handler.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/strings/grit/components_strings.h"
--- a/chrome/browser/ui/webui/management/management_ui_handler.cc
+++ b/chrome/browser/ui/webui/management/management_ui_handler.cc
@@ -55,7 +55,6 @@
 #include "components/policy/core/common/management/management_service.h"
 #include "components/policy/core/common/policy_pref_names.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/strings/grit/components_strings.h"
 #include "components/supervised_user/core/common/pref_names.h"
--- a/chrome/browser/ui/webui/policy/policy_ui_handler.cc
+++ b/chrome/browser/ui/webui/policy/policy_ui_handler.cc
@@ -83,7 +83,6 @@
 #include "components/policy/proto/device_management_backend.pb.h"
 #include "components/prefs/pref_change_registrar.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/strings/grit/components_strings.h"
 #include "content/public/browser/browser_task_traits.h"
@@ -377,15 +376,6 @@ void PolicyUIHandler::HandleSetLocalTest
 
   CHECK(local_test_provider);
 
-#if !BUILDFLAG(IS_ANDROID) && !BUILDFLAG(IS_CHROMEOS)
-  const std::string& profile_separation_policy_response = args[2].GetString();
-  Profile::FromWebUI(web_ui())->GetPrefs()->ClearPref(
-      prefs::kUserCloudSigninPolicyResponseFromPolicyTestPage);
-  Profile::FromWebUI(web_ui())->GetPrefs()->SetDefaultPrefValue(
-      prefs::kUserCloudSigninPolicyResponseFromPolicyTestPage,
-      base::Value(profile_separation_policy_response));
-#endif
-
   Profile::FromWebUI(web_ui())
       ->GetProfilePolicyConnector()
       ->UseLocalTestPolicyProvider();
@@ -399,13 +389,6 @@ void PolicyUIHandler::HandleRevertLocalT
   if (!PolicyUI::ShouldLoadTestPage(Profile::FromWebUI(web_ui()))) {
     return;
   }
-#if !BUILDFLAG(IS_ANDROID) && !BUILDFLAG(IS_CHROMEOS)
-  Profile::FromWebUI(web_ui())->GetPrefs()->ClearPref(
-      prefs::kUserCloudSigninPolicyResponseFromPolicyTestPage);
-  Profile::FromWebUI(web_ui())->GetPrefs()->SetDefaultPrefValue(
-      prefs::kUserCloudSigninPolicyResponseFromPolicyTestPage,
-      base::Value(std::string()));
-#endif
   Profile::FromWebUI(web_ui())
       ->GetProfilePolicyConnector()
       ->RevertUseLocalTestPolicyProvider();
--- a/chrome/browser/ui/webui/profile_info_watcher.cc
+++ b/chrome/browser/ui/webui/profile_info_watcher.cc
@@ -12,7 +12,6 @@
 #include "chrome/browser/signin/identity_manager_factory.h"
 #include "chrome/common/pref_names.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 
 ProfileInfoWatcher::ProfileInfoWatcher(Profile* profile,
@@ -26,11 +25,6 @@ ProfileInfoWatcher::ProfileInfoWatcher(P
   if (profile_manager) {
     profile_manager->GetProfileAttributesStorage().AddObserver(this);
   }
-
-  signin_allowed_pref_.Init(
-      prefs::kSigninAllowed, profile_->GetPrefs(),
-      base::BindRepeating(&ProfileInfoWatcher::RunCallback,
-                          base::Unretained(this)));
 }
 
 ProfileInfoWatcher::~ProfileInfoWatcher() {
--- a/chrome/browser/ui/webui/profile_info_watcher.h
+++ b/chrome/browser/ui/webui/profile_info_watcher.h
@@ -49,7 +49,6 @@ class ProfileInfoWatcher : public Profil
   // Called when the authenticated username changes.
   base::RepeatingClosure callback_;
 
-  BooleanPrefMember signin_allowed_pref_;
 };
 
 #endif  // CHROME_BROWSER_UI_WEBUI_PROFILE_INFO_WATCHER_H_
--- a/chrome/browser/ui/webui/reset_password/reset_password_ui.cc
+++ b/chrome/browser/ui/webui/reset_password/reset_password_ui.cc
@@ -24,7 +24,6 @@
 #include "components/safe_browsing/core/browser/password_protection/metrics_util.h"
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/strings/grit/components_strings.h"
 #include "components/url_formatter/url_formatter.h"
 #include "components/user_prefs/user_prefs.h"
--- a/chrome/browser/ui/webui/settings/people_handler.cc
+++ b/chrome/browser/ui/webui/settings/people_handler.cc
@@ -57,7 +57,6 @@
 #include "components/signin/core/browser/signin_error_controller.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
@@ -414,10 +413,6 @@ void PeopleHandler::OnJavascriptAllowed(
   PrefService* prefs = profile_->GetPrefs();
   profile_pref_registrar_ = std::make_unique<PrefChangeRegistrar>();
   profile_pref_registrar_->Init(prefs);
-  profile_pref_registrar_->Add(
-      prefs::kSigninAllowed,
-      base::BindRepeating(&PeopleHandler::UpdateSyncStatus,
-                          base::Unretained(this)));
 #if BUILDFLAG(ENABLE_DICE_SUPPORT)
   SigninPrefs::ObserveSigninPrefsChanges(
       *profile_pref_registrar_,
--- a/chrome/browser/ui/webui/settings/safety_hub_handler.cc
+++ b/chrome/browser/ui/webui/settings/safety_hub_handler.cc
@@ -50,7 +50,6 @@
 #include "components/password_manager/core/common/password_manager_pref_names.h"
 #include "components/permissions/constants.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/site_engagement/content/site_engagement_service.h"
--- a/chrome/browser/ui/webui/settings/settings_localized_strings_provider.cc
+++ b/chrome/browser/ui/webui/settings/settings_localized_strings_provider.cc
@@ -87,7 +87,6 @@
 #include "components/regional_capabilities/regional_capabilities_service.h"
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/hashprefix_realtime/hash_realtime_utils.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/saved_tab_groups/public/features.h"
 #include "components/signin/public/base/signin_buildflags.h"
 #include "components/strings/grit/components_branded_strings.h"
@@ -3567,11 +3566,6 @@ void AddSiteSettingsStrings(content::Web
   html_source->AddLocalizedStrings(kSensorsLocalizedStrings);
 
   html_source->AddBoolean(
-      "enableSafeBrowsingSubresourceFilter",
-      base::FeatureList::IsEnabled(
-          subresource_filter::kSafeBrowsingSubresourceFilter));
-
-  html_source->AddBoolean(
       "enableBlockAutoplayContentSetting",
       base::FeatureList::IsEnabled(media::kAutoplayDisableSettings));
 
--- a/chrome/browser/ui/webui/settings/settings_ui.cc
+++ b/chrome/browser/ui/webui/settings/settings_ui.cc
@@ -116,7 +116,6 @@
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/hashprefix_realtime/hash_realtime_utils.h"
 #include "components/search_engines/template_url_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/sync/base/features.h"
 #include "content/public/browser/url_data_source.h"
@@ -278,9 +277,7 @@ SettingsUI::SettingsUI(content::WebUI* w
   AddSettingsPageUIHandler(std::make_unique<MacSystemSettingsHandler>());
 #endif
 
-  html_source->AddBoolean("signinAllowed", !profile->IsGuestSession() &&
-                                               profile->GetPrefs()->GetBoolean(
-                                                   prefs::kSigninAllowed));
+  html_source->AddBoolean("signinAllowed", false);
   ProfileAttributesEntry* entry =
       g_browser_process->profile_manager()
           ->GetProfileAttributesStorage()
--- a/chrome/browser/ui/webui/signin/inline_login_handler.cc
+++ b/chrome/browser/ui/webui/signin/inline_login_handler.cc
@@ -27,7 +27,6 @@
 #include "chrome/common/pref_names.h"
 #include "components/metrics/metrics_pref_names.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "content/public/browser/storage_partition.h"
 #include "content/public/browser/web_contents.h"
 #include "content/public/browser/web_ui.h"
@@ -138,8 +137,6 @@ void InlineLoginHandler::ContinueHandleI
   std::string default_email;
   if (reason == signin_metrics::Reason::kSigninPrimaryAccount ||
       reason == signin_metrics::Reason::kForcedSigninPrimaryAccount) {
-    default_email = profile->GetPrefs()->GetString(
-        prefs::kGoogleServicesLastSyncingUsername);
   } else {
     if (!net::GetValueForKeyInQuery(current_url, "email", &default_email)) {
       default_email.clear();
--- a/chrome/browser/ui/webui/signin/inline_login_handler_impl.cc
+++ b/chrome/browser/ui/webui/signin/inline_login_handler_impl.cc
@@ -69,7 +69,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/signin/core/browser/about_signin_internals.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/accounts_mutator.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
--- a/chrome/browser/ui/webui/signin/managed_user_profile_notice_handler.cc
+++ b/chrome/browser/ui/webui/signin/managed_user_profile_notice_handler.cc
@@ -33,7 +33,6 @@
 #include "chrome/grit/generated_resources.h"
 #include "components/policy/core/browser/signin/profile_separation_policies.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/signin_constants.h"
 #include "components/strings/grit/components_strings.h"
@@ -469,23 +468,6 @@ base::Value::Dict ManagedUserProfileNoti
       dict.Set("email", base::UTF16ToUTF8(email_));
       dict.Set("accountName", account_info.full_name);
 
-#if !BUILDFLAG(IS_CHROMEOS)
-      // We apply the checkLinkDataCheckboxByDefault to true value only if the
-      // link data checkbox is visible and the policy
-      // ProfileSeparationDataMigrationSettings is set to its OPTOUT value (2)
-      // or the legacy policy EnterpriseProfileCreationKeepBrowsingData is set
-      // to True.
-      bool profile_separation_data_migration_settings_optout =
-          Profile::FromWebUI(web_ui())->GetPrefs()->GetInteger(
-              prefs::kProfileSeparationDataMigrationSettings) == 2;
-      bool check_link_Data_checkbox_by_default_from_legacy_policy =
-          g_browser_process->local_state()->GetBoolean(
-              prefs::kEnterpriseProfileCreationKeepBrowsingData);
-      dict.Set("checkLinkDataCheckboxByDefault",
-               show_link_data_option_ &&
-                   (profile_separation_data_migration_settings_optout ||
-                    check_link_Data_checkbox_by_default_from_legacy_policy));
-#endif
       break;
   }
 
--- a/chrome/browser/ui/webui/signin/managed_user_profile_notice_ui.cc
+++ b/chrome/browser/ui/webui/signin/managed_user_profile_notice_ui.cc
@@ -32,7 +32,6 @@
 #include "chrome/grit/signin_resources.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/signin/public/identity_manager/tribool.h"
@@ -351,8 +350,7 @@ void ManagedUserProfileNoticeUI::Initial
   // Change the text so that the "(Recommended)" label is not shown when the
   // admin has set merging data as the default option.
   bool profile_separation_data_migration_settings_optout =
-      profile->GetPrefs()->GetInteger(
-          prefs::kProfileSeparationDataMigrationSettings) == 2;
+      false;
   bool check_link_data_checkbox_by_default_from_legacy_policy =
       g_browser_process->local_state()->GetBoolean(
           prefs::kEnterpriseProfileCreationKeepBrowsingData);
--- a/chrome/browser/ui/webui/signin/signin_error_ui.cc
+++ b/chrome/browser/ui/webui/signin/signin_error_ui.cc
@@ -28,7 +28,6 @@
 #include "chrome/grit/generated_resources.h"
 #include "chrome/grit/signin_resources.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/strings/grit/components_strings.h"
 #include "content/public/browser/web_ui.h"
 #include "content/public/browser/web_ui_data_source.h"
@@ -116,9 +115,7 @@ void SigninErrorUI::Initialize(Browser*
   if (is_profile_blocked) {
     source->AddLocalizedString("profileBlockedMessage",
                                IDS_OLD_PROFILES_DISABLED_MESSAGE);
-    std::string allowed_domain = signin_ui_util::GetAllowedDomain(
-        g_browser_process->local_state()->GetString(
-            prefs::kGoogleServicesUsernamePattern));
+    std::string allowed_domain;
     if (allowed_domain.empty()) {
       source->AddLocalizedString(
           "profileBlockedAddPersonSuggestion",
--- a/chrome/browser/ui/webui/signin/signin_utils_desktop.cc
+++ b/chrome/browser/ui/webui/signin/signin_utils_desktop.cc
@@ -17,7 +17,6 @@
 #include "chrome/browser/ui/webui/signin/signin_ui_error.h"
 #include "chrome/common/chrome_switches.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/signin/public/identity_manager/identity_utils.h"
 #include "google_apis/gaia/gaia_auth_util.h"
@@ -26,100 +25,10 @@
 SigninUIError CanOfferSignin(Profile* profile,
                              const GaiaId& gaia_id,
                              const std::string& email) {
-  if (!profile) {
     return SigninUIError::Other(email);
-  }
-
-  if (!profile->GetPrefs()->GetBoolean(prefs::kSigninAllowed)) {
-    return SigninUIError::Other(email);
-  }
-
-  if (!ChromeSigninClient::ProfileAllowsSigninCookies(profile)) {
-    return SigninUIError::Other(email);
-  }
-
-  if (!email.empty()) {
-    auto* identity_manager = IdentityManagerFactory::GetForProfile(profile);
-    if (!identity_manager) {
-      return SigninUIError::Other(email);
-    }
-
-    // Make sure this username is not prohibited by policy.
-    if (!signin::IsUsernameAllowedByPatternFromPrefs(
-            g_browser_process->local_state(), email)) {
-      return SigninUIError::UsernameNotAllowedByPatternFromPrefs(email);
-    }
-
-    // If the identity manager already has a primary account, then this is a
-    // re-auth scenario. Make sure the email just signed in corresponds to
-    // the one sign in manager expects.
-    std::string current_email =
-        identity_manager->GetPrimaryAccountInfo(signin::ConsentLevel::kSync)
-            .email;
-    const bool same_email = gaia::AreEmailsSame(current_email, email);
-    if (!current_email.empty() && !same_email) {
-      return SigninUIError::WrongReauthAccount(email, current_email);
-    }
-
-    // If some profile, not just the current one, is already connected to this
-    // account, don't show the infobar.
-    if (g_browser_process && !same_email) {
-      ProfileManager* profile_manager = g_browser_process->profile_manager();
-      if (profile_manager) {
-        std::vector<ProfileAttributesEntry*> entries =
-            profile_manager->GetProfileAttributesStorage()
-                .GetAllProfilesAttributes();
-
-        for (const ProfileAttributesEntry* entry : entries) {
-          // Ignore omitted profiles (these are notably profiles being created
-          // using the signed-in profile creation flow). This is motivated by
-          // these profile hanging around until the next restart which could
-          // block subsequent profile creation, resulting in
-          // SigninUIError::AccountAlreadyUsedByAnotherProfile.
-          // TODO(crbug.com/********): This opens the possibility for getting
-          // into a state with 2 profiles syncing to the same account:
-          //  - start creating a new profile and sign-in,
-          //  - enabled sync for the same account in another (existing) profile,
-          //  - finish the profile creation by consenting to sync.
-          // Properly addressing this would require deleting profiles from
-          // cancelled flow right away, returning an error here for omitted
-          // profiles, and fix the code that switches to the other syncing
-          // profile so that the profile creation flow window gets activated for
-          // profiles being created (instead of opening a new window).
-          if (entry->IsOmitted() || entry->GetPath() == profile->GetPath()) {
-            continue;
-          }
-          if (!entry->IsAuthenticated() && !entry->CanBeManaged()) {
-            continue;
-          }
-
-          if (base::CommandLine::ForCurrentProcess()->HasSwitch(
-                  switches::kBypassAccountAlreadyUsedByAnotherProfileCheck)) {
-            continue;
-          }
-          if (gaia_id == entry->GetGAIAId()) {
-            return SigninUIError::AccountAlreadyUsedByAnotherProfile(
-                email, entry->GetPath());
-          }
-        }
-      }
-    }
-
-    // With force sign in enabled, cross account sign in is not allowed.
-    if (signin_util::IsForceSigninEnabled() &&
-        IsCrossAccountError(profile, gaia_id)) {
-      std::string last_email = profile->GetPrefs()->GetString(
-          prefs::kGoogleServicesLastSyncingUsername);
-      return SigninUIError::ProfileWasUsedByAnotherAccount(email, last_email);
-    }
-  }
-
-  return SigninUIError::Ok();
 }
 
 bool IsCrossAccountError(Profile* profile, const GaiaId& gaia_id) {
   DCHECK(!gaia_id.empty());
-  const GaiaId last_gaia_id(
-      profile->GetPrefs()->GetString(prefs::kGoogleServicesLastSyncingGaiaId));
-  return !last_gaia_id.empty() && gaia_id != last_gaia_id;
+  return false;
 }
--- a/chrome/browser/ui/webui/signin/signout_confirmation/signout_confirmation_handler.cc
+++ b/chrome/browser/ui/webui/signin/signout_confirmation/signout_confirmation_handler.cc
@@ -13,7 +13,6 @@
 #include "chrome/browser/ui/signin/signin_view_controller.h"
 #include "chrome/grit/branded_strings.h"
 #include "chrome/grit/generated_resources.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/strings/grit/components_strings.h"
 #include "ui/base/l10n/l10n_util.h"
 #include "url/gurl.h"
--- a/chrome/browser/ui/webui/signin/turn_sync_on_helper.cc
+++ b/chrome/browser/ui/webui/signin/turn_sync_on_helper.cc
@@ -47,7 +47,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/signin/core/browser/account_reconcilor.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_managed_status_finder.h"
 #include "components/signin/public/identity_manager/accounts_mutator.h"
@@ -275,8 +274,7 @@ void TurnSyncOnHelper::TurnSyncOnInterna
   // last authenticated account of the current profile, then Chrome will show a
   // confirmation dialog before starting sync.
   // TODO(skym): Warn for high risk upgrade scenario (https://crbug.com/572754).
-  std::string last_email = profile_->GetPrefs()->GetString(
-      prefs::kGoogleServicesLastSyncingUsername);
+  std::string last_email;
   delegate_->ShowMergeSyncDataConfirmation(
       last_email, account_info_.email,
       base::BindOnce(&TurnSyncOnHelper::OnMergeAccountConfirmation,
--- a/chrome/browser/ui/webui/signin/turn_sync_on_helper_delegate_impl.cc
+++ b/chrome/browser/ui/webui/signin/turn_sync_on_helper_delegate_impl.cc
@@ -41,7 +41,6 @@
 #include "components/policy/core/browser/signin/user_cloud_signin_restriction_policy_fetcher.h"
 #include "components/policy/core/common/policy_utils.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "google_apis/gaia/gaia_auth_util.h"
 #include "third_party/skia/include/core/SkColor.h"
 
@@ -253,26 +252,6 @@ void TurnSyncOnHelperDelegateImpl::OnPro
     return;
   }
 
-  if (prompt_for_new_profile) {
-    account_level_signin_restriction_policy_fetcher_
-        ->GetManagedAccountsSigninRestriction(
-            IdentityManagerFactory::GetForProfile(browser_->profile()),
-            account_info.account_id,
-            base::BindOnce(&TurnSyncOnHelperDelegateImpl::
-                               OnProfileSigninRestrictionsFetched,
-                           weak_ptr_factory_.GetWeakPtr(), account_info,
-                           std::move(callback)),
-            policy::utils::IsPolicyTestingEnabled(
-                browser_->profile()->GetPrefs(), chrome::GetChannel())
-                ? browser_->profile()
-                      ->GetPrefs()
-                      ->GetDefaultPrefValue(
-                          prefs::
-                              kUserCloudSigninPolicyResponseFromPolicyTestPage)
-                      ->GetString()
-                : std::string());
-    return;
-  }
 
   browser_->GetFeatures()
       .signin_view_controller()
--- a/chrome/browser/ui/webui/signin/turn_sync_on_helper_unittest.cc
+++ b/chrome/browser/ui/webui/signin/turn_sync_on_helper_unittest.cc
@@ -57,7 +57,6 @@
 #include "components/search_engines/template_url_service.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_capabilities_test_mutator.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
--- a/chrome/browser/unified_consent/unified_consent_service_factory.cc
+++ b/chrome/browser/unified_consent/unified_consent_service_factory.cc
@@ -13,7 +13,6 @@
 #include "components/commerce/core/pref_names.h"
 #include "components/embedder_support/pref_names.h"
 #include "components/prefs/pref_registry_simple.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/spellcheck/browser/pref_names.h"
 #include "components/sync_preferences/pref_service_syncable.h"
 #include "components/unified_consent/unified_consent_metrics.h"
@@ -34,8 +33,6 @@ namespace {
 
 std::vector<std::string> GetSyncedServicePrefNames() {
   return {
-    prefs::kSearchSuggestEnabled, prefs::kSafeBrowsingEnabled,
-        prefs::kSafeBrowsingScoutReportingEnabled,
         spellcheck::prefs::kSpellCheckUseSpellingService,
         commerce::kPriceEmailNotificationsEnabled,
 #if BUILDFLAG(IS_ANDROID)
--- a/chrome/browser/webdata_services/web_data_service_factory.cc
+++ b/chrome/browser/webdata_services/web_data_service_factory.cc
@@ -18,7 +18,6 @@
 #include "components/plus_addresses/webdata/plus_address_webdata_service.h"
 #include "components/prefs/pref_service.h"
 #include "components/search_engines/keyword_web_data_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/webdata/token_web_data.h"
 #include "components/webdata_services/web_data_service_wrapper.h"
 #include "content/public/browser/browser_task_traits.h"
@@ -89,51 +88,7 @@ enum class AutofillAccountStorageResult
 AutofillAccountStorageResult DetermineAutofillAccountStorage(
     PrefService* pref_service) {
   CHECK(pref_service);
-  // The interpretation of the pref mimics what PrimaryAccountManager's
-  // constructor does.
-  const bool is_signed_in =
-      !pref_service->GetString(::prefs::kGoogleServicesAccountId).empty();
-  // If the user is signed out during profile startup, as per switch above
-  // being enabled, any new sign-ins will involve an explicit sign-in (i.e.
-  // interaction with native UI). In this case, on-disk storage is appropriate.
-  if (!is_signed_in) {
     return AutofillAccountStorageResult::kOnDisk_SignedOut;
-  }
-  // It is possible that the user already is in an explicit sign-in state. In
-  // this case, on-disk storage is appropriate, as any additional future
-  // sign-ins (if the user first signs out) are guaranteed to be explicit
-  // sign-ins too.
-  if (pref_service->GetBoolean(::prefs::kExplicitBrowserSignin)) {
-    return AutofillAccountStorageResult::kOnDisk_SignedInExplicitly;
-  }
-  // The interpretation of the pref mimics what PrimaryAccountManager's
-  // constructor does.
-  const bool is_consented_to_sync =
-      pref_service->GetBoolean(::prefs::kGoogleServicesConsentedToSync);
-  // If Sync (the feature) is on, the account storage isn't currently used. This
-  // is because the only way to activate the account storate requires signing
-  // out first, which means the predicate can return false as per earlier
-  // rationale. With one exception: managed profiles may turn sync off without
-  // signing out. Either way, having turned sync on implies the user interacted
-  // explicitly with a sync UI, so in this particular context it is no different
-  // from explicit sign-in, and on-disk storage is appropriate.
-  if (is_consented_to_sync) {
-    return AutofillAccountStorageResult::kOnDisk_SyncFeatureEnabled;
-  }
-  // The remaining case implies a legacy signed-in-non-syncing state with
-  // implicit sign-in, which means the user signed in before the latest feature
-  // flags rolled out. This is the only case where in-memory storage should be
-  // used.
-  //
-  // Note that, during the lifetime of the browser/profile, it is still possible
-  // that the users signs out and signs back in, where the latter is guaranteed
-  // to be an explicit sign-in. In this case, it would be theoretically better
-  // to immediately switch to on-disk storage, but this isn't possible once a
-  // profile is initialized (as this predicate only gets evaluated once).
-  // Conveniently, it is also harmless to use the in-memory storage until the
-  // next browser restart, given that this is a one-off transition (upon restart
-  // the code would run into one of the cases listed earlier that return false).
-  return AutofillAccountStorageResult::kInMemory_SignedInImplicitly;
 }
 #endif  // !BUILDFLAG(IS_ANDROID)
 
--- a/chrome/common/extensions/api/privacy.json
+++ b/chrome/common/extensions/api/privacy.json
@@ -63,16 +63,6 @@
             "value": ["passwordSavingEnabled", {"type":"boolean"}],
             "description": "If enabled, the password manager will ask if you want to save passwords. This preference's value is a boolean, defaulting to <code>true</code>."
           },
-          "safeBrowsingEnabled": {
-            "$ref": "types.ChromeSetting",
-            "value": ["safeBrowsingEnabled", {"type":"boolean"}],
-            "description": "If enabled, Chrome does its best to protect you from phishing and malware. This preference's value is a boolean, defaulting to <code>true</code>."
-          },
-          "safeBrowsingExtendedReportingEnabled": {
-            "$ref": "types.ChromeSetting",
-            "value": ["safeBrowsingExtendedReportingEnabled", {"type":"boolean"}],
-            "description": "If enabled, Chrome will send additional information to Google when SafeBrowsing blocks a page, such as the content of the blocked page. This preference's value is a boolean, defaulting to <code>false</code>."
-          },
           "searchSuggestEnabled": {
             "$ref": "types.ChromeSetting",
             "value": ["searchSuggestEnabled", {"type":"boolean"}],
--- a/components/autofill/core/browser/data_manager/addresses/address_data_manager.cc
+++ b/components/autofill/core/browser/data_manager/addresses/address_data_manager.cc
@@ -33,7 +33,6 @@
 #include "components/autofill/core/common/autofill_prefs.h"
 #include "components/autofill/core/common/dense_set.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/sync/base/data_type.h"
 #include "components/sync/base/features.h"
@@ -294,13 +293,7 @@ void AddressDataManager::RemoveLocalProf
 }
 
 bool AddressDataManager::IsEligibleForAddressAccountStorage() const {
-  if (!sync_service_) {
     return false;
-  }
-
-  // The CONTACT_INFO data type is only running for eligible users. See
-  // ContactInfoDataTypeController.
-  return sync_service_->GetActiveDataTypes().Has(syncer::CONTACT_INFO);
 }
 
 void AddressDataManager::MigrateProfileToAccount(
@@ -584,35 +577,7 @@ bool AddressDataManager::IsAutofillUserS
 }
 
 bool AddressDataManager::IsAutofillSyncToggleAvailable() const {
-  if (!pref_service_->GetBoolean(::prefs::kExplicitBrowserSignin)) {
-    return false;
-  }
-
-  if (!sync_service_) {
-    return false;
-  }
-
-  // Do not show the toggle if Sync is disabled on in error.
-  if (sync_service_->GetTransportState() ==
-          syncer::SyncService::TransportState::PAUSED ||
-      sync_service_->GetTransportState() ==
-          syncer::SyncService::TransportState::DISABLED) {
     return false;
-  }
-
-  // Do not show the toggle for syncing users.
-  if (sync_service_->HasSyncConsent()) {
-    return false;
-  }
-
-  if (sync_service_->GetUserSettings()->IsTypeManagedByPolicy(
-          syncer::UserSelectableType::kAutofill)) {
-    return false;
-  }
-
-  return contact_info_precondition_checker_ &&
-         contact_info_precondition_checker_->GetPreconditionState() ==
-             syncer::DataTypeController::PreconditionState::kPreconditionsMet;
 }
 
 void AddressDataManager::SetAutofillSelectableTypeEnabled(bool enabled) {
--- a/components/autofill/core/browser/studies/autofill_experiments.cc
+++ b/components/autofill/core/browser/studies/autofill_experiments.cc
@@ -35,7 +35,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/prefs/scoped_user_pref_update.h"
 #include "components/signin/public/base/signin_buildflags.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/strings/grit/components_strings.h"
 #include "components/sync/base/features.h"
 #include "components/sync/base/user_selectable_type.h"
@@ -173,12 +172,6 @@ bool IsCreditCardUploadEnabled(
   // TODO(crbug.com/40066949): Simplify once IsSyncFeatureActive() is deleted
   // from the codebase.
   bool addresses_in_transport_mode = true;
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-  // Dice users don't have addresses in transport mode until they went through
-  // the explicit signin flow.
-  addresses_in_transport_mode =
-      pref_service.GetBoolean(::prefs::kExplicitBrowserSignin);
-#endif
   bool syncing_or_addresses_in_transport_mode =
       sync_service->IsSyncFeatureActive() || addresses_in_transport_mode;
   if (syncing_or_addresses_in_transport_mode &&
@@ -292,10 +285,6 @@ bool IsUserOptedInWalletSyncTransport(co
   // On mobile, no specific opt-in is required.
   return true;
 #else
-  if (prefs->GetBoolean(::prefs::kExplicitBrowserSignin)) {
-    // Explicit browser signin makes the explicit opt-in unnecessary.
-    return true;
-  }
 
   // Get the hash of the account id.
   std::string account_hash =
--- a/components/browser_sync/BUILD.gn
+++ b/components/browser_sync/BUILD.gn
@@ -17,8 +17,6 @@ static_library("browser_sync") {
     "sync_engine_factory_impl.h",
     "sync_internals_message_handler.cc",
     "sync_internals_message_handler.h",
-    "sync_to_signin_migration.cc",
-    "sync_to_signin_migration.h",
   ]
 
   public_deps = [
--- a/components/collaboration/internal/collaboration_service_impl.cc
+++ b/components/collaboration/internal/collaboration_service_impl.cc
@@ -20,7 +20,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/saved_tab_groups/public/tab_group_sync_service.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/sync/base/collaboration_id.h"
 #include "components/sync/base/features.h"
@@ -68,10 +67,6 @@ CollaborationServiceImpl::CollaborationS
       prefs::kSharedTabGroupsManagedAccountSetting,
       base::BindRepeating(&CollaborationServiceImpl::RefreshServiceStatus,
                           base::Unretained(this)));
-  registrar_.Add(
-      ::prefs::kSigninAllowed,
-      base::BindRepeating(&CollaborationServiceImpl::RefreshServiceStatus,
-                          base::Unretained(this)));
 }
 
 CollaborationServiceImpl::~CollaborationServiceImpl() {
@@ -410,144 +405,13 @@ SigninStatus CollaborationServiceImpl::G
   DCHECK_CALLED_ON_VALID_THREAD(thread_checker_);
   SigninStatus status = SigninStatus::kNotSignedIn;
 
-  bool has_valid_primary_account =
-      identity_manager_->HasPrimaryAccountWithRefreshToken(
-          signin::ConsentLevel::kSignin) &&
-      !identity_manager_->HasAccountWithRefreshTokenInPersistentErrorState(
-          identity_manager_->GetPrimaryAccountId(
-              signin::ConsentLevel::kSignin));
-  if (has_valid_primary_account) {
-    status = SigninStatus::kSignedIn;
-  } else if (identity_manager_->HasPrimaryAccount(
-                 signin::ConsentLevel::kSignin)) {
-    status = SigninStatus::kSignedInPaused;
-  } else if (!profile_prefs_->GetBoolean(::prefs::kSigninAllowed)) {
-    status = SigninStatus::kSigninDisabled;
-  }
-
-#if BUILDFLAG(IS_IOS)
-  BrowserSigninMode policy_mode = static_cast<BrowserSigninMode>(
-      local_prefs_->GetInteger(::prefs::kBrowserSigninPolicy));
-  if (policy_mode == BrowserSigninMode::kDisabled) {
     status = SigninStatus::kSigninDisabled;
-  }
-#endif
 
   return status;
 }
 
 CollaborationStatus CollaborationServiceImpl::GetCollaborationStatus() {
-  DCHECK_CALLED_ON_VALID_THREAD(thread_checker_);
-  if (!base::FeatureList::IsEnabled(
-          data_sharing::features::kDataSharingFeature) &&
-      !base::FeatureList::IsEnabled(
-          data_sharing::features::kDataSharingJoinOnly)) {
     return CollaborationStatus::kDisabled;
-  }
-
-  // Check if version out-of-date turn off shared tab groups data types and show
-  // update chrome ui.
-  if (base::FeatureList::IsEnabled(
-          data_sharing::features::kSharedDataTypesKillSwitch)) {
-    return base::FeatureList::IsEnabled(
-               data_sharing::features::kDataSharingEnableUpdateChromeUI)
-               ? CollaborationStatus::kVersionOutOfDateShowUpdateChromeUi
-               : CollaborationStatus::kVersionOutOfDate;
-  }
-
-  // Check if device policy allow signin.
-#if BUILDFLAG(IS_IOS)
-  BrowserSigninMode policy_mode = static_cast<BrowserSigninMode>(
-      local_prefs_->GetInteger(::prefs::kBrowserSigninPolicy));
-  if (policy_mode == BrowserSigninMode::kDisabled) {
-    return CollaborationStatus::kDisabledForPolicy;
-  }
-#else
-  if (!profile_prefs_->GetBoolean(::prefs::kSigninAllowed) &&
-      profile_prefs_->IsManagedPreference(::prefs::kSigninAllowed)) {
-    return CollaborationStatus::kDisabledForPolicy;
-  }
-#endif
-
-  // Check if device policy allow sync.
-  if (current_status_.sync_status == SyncStatus::kSyncDisabledByEnterprise) {
-    return CollaborationStatus::kDisabledForPolicy;
-  }
-
-  // TODO(haileywang): Support collaboration status updates.
-  CollaborationStatus status = CollaborationStatus::kDisabled;
-  if (base::FeatureList::IsEnabled(
-          data_sharing::features::kDataSharingFeature)) {
-    status = CollaborationStatus::kEnabledCreateAndJoin;
-  } else if (base::FeatureList::IsEnabled(
-                 data_sharing::features::kDataSharingJoinOnly)) {
-    status = CollaborationStatus::kAllowedToJoin;
-  }
-
-  if (current_status_.signin_status == SigninStatus::kNotSignedIn) {
-    return status;
-  }
-
-  CoreAccountInfo account =
-      identity_manager_->GetPrimaryAccountInfo(signin::ConsentLevel::kSignin);
-  if (!signin::AccountManagedStatusFinder::MayBeEnterpriseUserBasedOnEmail(
-          account.email)) {
-    return status;
-  }
-
-  // Enterprise account handling.
-  if (!account_managed_status_finder_) {
-    account_managed_status_finder_ =
-        std::make_unique<signin::AccountManagedStatusFinder>(
-            identity_manager_, account,
-            base::BindOnce(&CollaborationServiceImpl::RefreshServiceStatus,
-                           weak_ptr_factory_.GetWeakPtr()),
-            base::Seconds(5));
-  }
-
-  // Enterprise V2: Check enterprise policy to allow/disallow collaboration
-  // feature.
-  if (base::FeatureList::IsEnabled(
-          data_sharing::features::kCollaborationEntrepriseV2)) {
-    switch (account_managed_status_finder_->GetOutcome()) {
-      case Outcome::kConsumerGmail:
-      case Outcome::kConsumerWellKnown:
-      case Outcome::kConsumerNotWellKnown:
-        break;
-      default:
-        if (profile_prefs_->GetInteger(
-                collaboration::prefs::kSharedTabGroupsManagedAccountSetting) ==
-            static_cast<int>(
-                prefs::SharedTabGroupsManagedAccountSetting::kDisabled)) {
-          return CollaborationStatus::kDisabledForPolicy;
-        }
-    }
-
-    return status;
-  }
-
-  // Enterprise V1: Figure out if collaboration feature is disabled by account
-  // policy. This early check allows to not disable collaboration feature when
-  // the user need to refresh their account (refresh tokens unavailable).
-  switch (account_managed_status_finder_->GetOutcome()) {
-    case Outcome::kPending:
-      status = CollaborationStatus::kDisabledPending;
-      break;
-    case Outcome::kError:
-    case Outcome::kTimeout:
-      status = CollaborationStatus::kDisabled;
-      break;
-    case Outcome::kEnterpriseGoogleDotCom:
-    case Outcome::kEnterprise:
-      status = CollaborationStatus::kDisabledForPolicy;
-      break;
-    case Outcome::kConsumerGmail:
-    case Outcome::kConsumerWellKnown:
-    case Outcome::kConsumerNotWellKnown:
-      break;
-  }
-
-  return status;
 }
 
 void CollaborationServiceImpl::RefreshServiceStatus() {
--- a/components/commerce/core/shopping_service.cc
+++ b/components/commerce/core/shopping_service.cc
@@ -62,7 +62,6 @@
 #include "components/search/ntp_features.h"
 #include "components/session_proto_db/session_proto_storage.h"
 #include "components/sessions/core/tab_restore_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/sync/base/features.h"
 #include "components/sync/service/sync_service.h"
 #include "components/unified_consent/url_keyed_data_collection_consent_helper.h"
@@ -164,16 +163,6 @@ class ProductSpecificationsUrlObserver
 // This function can be deleted once the Sync feature is removed.
 signin::ConsentLevel GetConsentLevelForEndpointFetchers(
     PrefService* pref_service) {
-  if (base::FeatureList::IsEnabled(
-          syncer::kReplaceSyncPromosWithSignInPromos)) {
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-    return pref_service->GetBoolean(prefs::kExplicitBrowserSignin)
-               ? signin::ConsentLevel::kSignin
-               : signin::ConsentLevel::kSync;
-#else
-    return signin::ConsentLevel::kSignin;
-#endif  // BUILDFLAG(ENABLE_DICE_SUPPORT)
-  }
   return signin::ConsentLevel::kSync;
 }
 
--- a/components/commerce/core/subscriptions/subscriptions_server_proxy.cc
+++ b/components/commerce/core/subscriptions/subscriptions_server_proxy.cc
@@ -22,7 +22,6 @@
 #include "components/commerce/core/subscriptions/commerce_subscription.h"
 #include "components/endpoint_fetcher/endpoint_fetcher.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/sync/base/features.h"
 #include "google_apis/gaia/gaia_constants.h"
--- a/components/device_signals/core/browser/browser_utils.h
+++ b/components/device_signals/core/browser/browser_utils.h
@@ -9,7 +9,6 @@
 
 #include "build/build_config.h"
 #include "components/device_signals/core/common/common_types.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 class PolicyBlocklistService;
 class PrefService;
--- a/components/feed/core/v2/feed_stream.cc
+++ b/components/feed/core/v2/feed_stream.cc
@@ -65,7 +65,6 @@
 #include "components/feed/feed_feature_list.h"
 #include "components/offline_pages/task/closure_task.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "google_apis/gaia/gaia_id.h"
 
 namespace feed {
@@ -174,9 +173,6 @@ FeedStream::FeedStream(RefreshTaskSchedu
   snippets_enabled_by_dse_.Init(prefs::kEnableSnippetsByDse, profile_prefs,
                                 preference_change_callback);
   has_stored_data_.Init(feed::prefs::kHasStoredData, profile_prefs);
-  signin_allowed_.Init(
-      ::prefs::kSigninAllowed, profile_prefs,
-      base::BindRepeating(&FeedStream::ClearAll, GetWeakPtr()));
   web_feed_subscription_coordinator_ =
       std::make_unique<WebFeedSubscriptionCoordinator>(delegate, this);
 
--- a/components/feed/core/v2/public/feed_service.cc
+++ b/components/feed/core/v2/public/feed_service.cc
@@ -33,7 +33,6 @@
 #include "components/history/core/browser/history_types.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "net/base/network_change_notifier.h"
@@ -183,7 +182,7 @@ class FeedService::StreamDelegateImpl :
   // Returns if signin is allowed on Android. Return true on other platform so
   // behavior is unchanged there.
   bool IsSigninAllowed() override {
-    return profile_prefs_->GetBoolean(::prefs::kSigninAllowed);
+    return false;
   }
   void RegisterExperiments(const Experiments& experiments) override {
     service_delegate_->RegisterExperiments(experiments);
--- a/components/password_manager/core/browser/features/password_manager_features_util.cc
+++ b/components/password_manager/core/browser/features/password_manager_features_util.cc
@@ -16,7 +16,6 @@
 #include "components/password_manager/core/common/password_manager_pref_names.h"
 #include "components/prefs/pref_service.h"
 #include "components/prefs/scoped_user_pref_update.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/sync/base/features.h"
 #include "components/sync/base/pref_names.h"
--- a/components/password_manager/core/browser/leak_detection/leak_detection_check_impl.cc
+++ b/components/password_manager/core/browser/leak_detection/leak_detection_check_impl.cc
@@ -22,7 +22,6 @@
 #include "components/password_manager/core/common/password_manager_pref_names.h"
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/signin/public/identity_manager/access_token_fetcher.h"
 #include "components/signin/public/identity_manager/access_token_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
@@ -228,14 +227,7 @@ bool LeakDetectionCheck::CanStartLeakChe
     const PrefService& prefs,
     const GURL& form_url,
     std::unique_ptr<autofill::SavePasswordProgressLogger> logger) {
-  const bool is_leak_protection_on =
-      prefs.GetBoolean(prefs::kPasswordLeakDetectionEnabled);
-  if (!is_leak_protection_on && logger) {
-    logger->LogMessage(autofill::SavePasswordProgressLogger::
-                           STRING_LEAK_DETECTION_DISABLED_FEATURE);
-  }
-  return is_leak_protection_on && !LeakDetectionCheck::IsURLBlockedByPolicy(
-                                      prefs, form_url, logger.get());
+  return false;
 }
 
 void LeakDetectionCheckImpl::OnAccessTokenRequestCompleted(
--- a/components/password_manager/core/browser/leak_detection_delegate.cc
+++ b/components/password_manager/core/browser/leak_detection_delegate.cc
@@ -26,7 +26,6 @@
 #include "components/password_manager/core/common/password_manager_features.h"
 #include "components/password_manager/core/common/password_manager_pref_names.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "services/network/public/cpp/shared_url_loader_factory.h"
 
 namespace password_manager {
--- a/components/password_manager/core/browser/password_reuse_detector_impl.cc
+++ b/components/password_manager/core/browser/password_reuse_detector_impl.cc
@@ -20,7 +20,6 @@
 #include "components/password_manager/core/browser/password_store/password_store_consumer.h"
 #include "components/password_manager/core/browser/password_store/psl_matching_helper.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "google_apis/gaia/gaia_auth_util.h"
 #include "url/gurl.h"
 
@@ -216,9 +215,7 @@ PasswordReuseDetectorImpl::CheckNonGaiaE
   // Skips password reuse check if |domain| matches enterprise login URL or
   // enterprise change password URL.
   GURL page_url(domain);
-  if (enterprise_password_urls_.has_value() &&
-      safe_browsing::MatchesURLList(page_url,
-                                    enterprise_password_urls_.value())) {
+  if (enterprise_password_urls_.has_value()) {
     return std::nullopt;
   }
 
--- a/components/password_manager/core/browser/password_reuse_manager_impl.cc
+++ b/components/password_manager/core/browser/password_reuse_manager_impl.cc
@@ -23,7 +23,6 @@
 #include "components/password_manager/core/browser/password_reuse_detector.h"
 #include "components/password_manager/core/browser/password_reuse_manager_signin_notifier.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/signin/public/base/consent_level.h"
 #include "google_apis/gaia/gaia_auth_util.h"
 
@@ -369,23 +368,6 @@ void PasswordReuseManagerImpl::ScheduleP
 }
 
 void PasswordReuseManagerImpl::ScheduleEnterprisePasswordURLUpdate() {
-  DCHECK(main_task_runner_->RunsTasksInCurrentSequence());
-
-  if (!prefs_) {
-    return;
-  }
-  std::vector<GURL> enterprise_login_urls;
-  safe_browsing::GetPasswordProtectionLoginURLsPref(*prefs_,
-                                                    &enterprise_login_urls);
-  GURL enterprise_change_password_url =
-      safe_browsing::GetPasswordProtectionChangePasswordURLPref(*prefs_);
-  if (!reuse_detector_) {
-    return;
-  }
-  ScheduleTask(base::BindOnce(&PasswordReuseDetector::UseEnterprisePasswordURLs,
-                              base::Unretained(reuse_detector_.get()),
-                              std::move(enterprise_login_urls),
-                              std::move(enterprise_change_password_url)));
 }
 
 void PasswordReuseManagerImpl::RequestLoginsFromStores() {
--- a/components/password_manager/core/browser/password_store/password_store.cc
+++ b/components/password_manager/core/browser/password_store/password_store.cc
@@ -37,7 +37,6 @@
 #include "components/password_manager/core/common/password_manager_features.h"
 #include "components/password_manager/core/common/password_manager_pref_names.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/sync/model/proxy_data_type_controller_delegate.h"
 
 namespace password_manager {
--- a/components/password_manager/core/browser/password_sync_util.cc
+++ b/components/password_manager/core/browser/password_sync_util.cc
@@ -10,7 +10,6 @@
 #include "components/password_manager/core/browser/features/password_manager_features_util.h"
 #include "components/password_manager/core/browser/password_form.h"
 #include "components/password_manager/core/browser/password_manager_client.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/sync/base/user_selectable_type.h"
 #include "components/sync/service/sync_user_settings.h"
@@ -73,9 +72,7 @@ bool IsGaiaCredentialPage(const std::str
 
 bool ShouldSaveEnterprisePasswordHash(const PasswordForm& form,
                                       const PrefService& prefs) {
-  return safe_browsing::MatchesPasswordProtectionLoginURL(form.url, prefs) ||
-         safe_browsing::MatchesPasswordProtectionChangePasswordURL(form.url,
-                                                                   prefs);
+  return false;
 }
 
 bool HasChosenToSyncPasswords(const syncer::SyncService* sync_service) {
--- a/components/password_manager/core/browser/store_metrics_reporter.cc
+++ b/components/password_manager/core/browser/store_metrics_reporter.cc
@@ -30,7 +30,6 @@
 #include "components/password_manager/core/common/password_manager_pref_names.h"
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "google_apis/gaia/gaia_auth_util.h"
 #include "google_apis/gaia/gaia_urls.h"
 
@@ -755,7 +754,7 @@ StoreMetricsReporter::StoreMetricsReport
   is_account_storage_enabled_ =
       features_util::IsAccountStorageEnabled(sync_service);
 
-  is_safe_browsing_enabled_ = safe_browsing::IsSafeBrowsingEnabled(*prefs_);
+  is_safe_browsing_enabled_ = false;
 
   if (settings) {
     // TODO(crbug.com/*********): use PasswordManagerSettingsService here.
--- a/components/password_manager/core/browser/sync_credentials_filter.cc
+++ b/components/password_manager/core/browser/sync_credentials_filter.cc
@@ -12,7 +12,6 @@
 #include "components/password_manager/core/browser/password_manager_util.h"
 #include "components/password_manager/core/browser/password_sync_util.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "google_apis/gaia/gaia_auth_util.h"
@@ -29,48 +28,7 @@ bool SyncCredentialsFilter::ShouldSave(c
   if (client_->IsOffTheRecord()) {
     return false;
   }
-
-  if (form.form_data.is_gaia_with_skip_save_password_form()) {
-    return false;
-  }
-
-  if (!sync_util::IsGaiaCredentialPage(form.signon_realm)) {
-    return true;
-  }
-
-  // Note that `sync_service` may be null in advanced cases like --disable-sync
-  // being used as per syncer::IsSyncAllowedByFlag().
-  const syncer::SyncService* sync_service = client_->GetSyncService();
-
-  // The requirement to fulfill is "don't offer to save a Gaia password inside
-  // its own account".
-  // Let's assume that if the browser is signed-in, new passwords are saved to
-  // the primary signed-in account. Per sync_util::GetAccountForSaving(), that's
-  // not always true, but let's not overcomplicate.
-  const CoreAccountInfo primary_account = sync_service != nullptr
-                                              ? sync_service->GetAccountInfo()
-                                              : CoreAccountInfo();
-  if (!primary_account.IsEmpty()) {
-    // Only save if the account is not the same. If the username is empty, in
-    // doubt don't save (this is relevant in the password change page).
-    return !form.username_value.empty() &&
-           !gaia::AreEmailsSame(base::UTF16ToUTF8(form.username_value),
-                                primary_account.email);
-  }
-
-// The browser is signed-out and the web just signed-in.
-#if !BUILDFLAG(IS_ANDROID) && !BUILDFLAG(IS_IOS)
-  // On desktop, this normally leads to immediate browser sign-in, in which case
-  // we shouldn't offer saving. One exception is if browser sign-in is disabled.
-  return !client_->GetPrefs()->GetBoolean(prefs::kSigninAllowed);
-#else
-  // On mobile, sign-in via the web page doesn't lead to browser sign-in, so
-  // offer saving.
-  // (Navigating to the Gaia web page opens Chrome UI which must be accepted to
-  // perform browser+web sign-in. The code path here is only hit if that UI was
-  // suppressed/ dismissed and the user interacted directly with the page.)
   return true;
-#endif
 }
 
 bool SyncCredentialsFilter::ShouldSaveGaiaPasswordHash(
--- a/components/password_manager/core/browser/sync_credentials_filter_unittest.cc
+++ b/components/password_manager/core/browser/sync_credentials_filter_unittest.cc
@@ -32,7 +32,6 @@
 #include "components/password_manager/core/browser/sync_username_test_base.h"
 #include "components/prefs/pref_registry_simple.h"
 #include "components/prefs/testing_pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "testing/gtest/include/gtest/gtest.h"
--- a/components/safe_browsing/content/browser/base_blocking_page.cc
+++ b/components/safe_browsing/content/browser/base_blocking_page.cc
@@ -16,7 +16,6 @@
 #include "components/safe_browsing/content/browser/content_unsafe_resource_util.h"
 #include "components/safe_browsing/content/browser/safe_browsing_controller_client.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "components/security_interstitials/content/security_interstitial_controller_client.h"
 #include "components/security_interstitials/content/settings_page_helper.h"
--- a/components/safe_browsing/content/browser/client_side_detection_service.cc
+++ b/components/safe_browsing/content/browser/client_side_detection_service.cc
@@ -35,7 +35,6 @@
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/proto/client_model.pb.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_constants.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "content/public/browser/browser_task_traits.h"
--- a/components/safe_browsing/content/browser/safe_browsing_navigation_observer_manager.cc
+++ b/components/safe_browsing/content/browser/safe_browsing_navigation_observer_manager.cc
@@ -22,7 +22,6 @@
 #include "components/safe_browsing/content/browser/web_ui/safe_browsing_ui.h"
 #include "components/safe_browsing/core/browser/referrer_chain_provider.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "components/sessions/content/session_tab_helper.h"
 #include "content/public/browser/browser_context.h"
@@ -379,7 +378,7 @@ GURL SafeBrowsingNavigationObserverManag
 bool SafeBrowsingNavigationObserverManager::IsEnabledAndReady(
     PrefService* prefs,
     bool has_safe_browsing_service) {
-  return IsSafeBrowsingEnabled(*prefs) && has_safe_browsing_service;
+  return false;
 }
 
 // static
--- a/components/safe_browsing/content/browser/triggers/suspicious_site_trigger_unittest.cc
+++ b/components/safe_browsing/content/browser/triggers/suspicious_site_trigger_unittest.cc
@@ -12,7 +12,6 @@
 #include "build/build_config.h"
 #include "components/prefs/testing_pref_service.h"
 #include "components/safe_browsing/content/browser/triggers/mock_trigger_manager.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "content/public/test/browser_task_environment.h"
 #include "content/public/test/navigation_simulator.h"
 #include "content/public/test/test_renderer_host.h"
--- a/components/safe_browsing/content/browser/triggers/trigger_manager.cc
+++ b/components/safe_browsing/content/browser/triggers/trigger_manager.cc
@@ -14,7 +14,6 @@
 #include "components/safe_browsing/content/browser/threat_details.h"
 #include "components/safe_browsing/content/browser/web_contents_key.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_interstitials/core/unsafe_resource.h"
 #include "content/public/browser/browser_context.h"
 #include "content/public/browser/browser_thread.h"
--- a/components/safe_browsing/content/browser/triggers/trigger_throttler.cc
+++ b/components/safe_browsing/content/browser/triggers/trigger_throttler.cc
@@ -146,30 +146,6 @@ void TriggerThrottler::CleanupOldEvents(
 
 void TriggerThrottler::LoadTriggerEventsFromPref() {
   trigger_events_.clear();
-  if (!local_state_prefs_)
-    return;
-
-  const base::Value::Dict& event_dict =
-      local_state_prefs_->GetDict(prefs::kSafeBrowsingTriggerEventTimestamps);
-  for (auto trigger_pair : event_dict) {
-    // Check that the first item in the pair is convertible to a trigger type
-    // and that the second item is a list.
-    int trigger_type_int;
-    if (!base::StringToInt(trigger_pair.first, &trigger_type_int) ||
-        trigger_type_int < static_cast<int>(TriggerType::kMinTriggerType) ||
-        trigger_type_int > static_cast<int>(TriggerType::kMaxTriggerType)) {
-      continue;
-    }
-    if (!trigger_pair.second.is_list())
-      continue;
-
-    const TriggerType trigger_type = static_cast<TriggerType>(trigger_type_int);
-    for (const auto& timestamp : trigger_pair.second.GetList()) {
-      if (timestamp.is_double())
-        trigger_events_[trigger_type].push_back(
-            base::Time::FromSecondsSinceUnixEpoch(timestamp.GetDouble()));
-    }
-  }
 }
 
 void TriggerThrottler::WriteTriggerEventsToPref() {
@@ -186,9 +162,6 @@ void TriggerThrottler::WriteTriggerEvent
     trigger_dict.Set(base::NumberToString(static_cast<int>(trigger_item.first)),
                      std::move(timestamps));
   }
-
-  local_state_prefs_->SetDict(prefs::kSafeBrowsingTriggerEventTimestamps,
-                              std::move(trigger_dict));
 }
 
 size_t TriggerThrottler::GetDailyQuotaForTrigger(
--- a/components/safe_browsing/content/browser/triggers/trigger_throttler_unittest.cc
+++ b/components/safe_browsing/content/browser/triggers/trigger_throttler_unittest.cc
@@ -9,7 +9,6 @@
 #include "base/test/simple_test_clock.h"
 #include "components/prefs/testing_pref_service.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "testing/gmock/include/gmock/gmock.h"
 #include "testing/gtest/include/gtest/gtest.h"
 
--- a/components/safe_browsing/content/browser/ui_manager.cc
+++ b/components/safe_browsing/content/browser/ui_manager.cc
@@ -21,7 +21,6 @@
 #include "components/safe_browsing/core/browser/db/v4_protocol_manager_util.h"
 #include "components/safe_browsing/core/browser/ping_manager.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "components/security_interstitials/content/security_interstitial_tab_helper.h"
 #include "components/security_interstitials/core/unsafe_resource.h"
--- a/components/safe_browsing/content/browser/ui_manager_unittest.cc
+++ b/components/safe_browsing/content/browser/ui_manager_unittest.cc
@@ -17,7 +17,6 @@
 #include "components/safe_browsing/content/browser/safe_browsing_controller_client.h"
 #include "components/safe_browsing/core/browser/db/util.h"
 #include "components/safe_browsing/core/browser/db/v4_protocol_manager_util.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_interstitials/content/security_interstitial_controller_client.h"
 #include "components/security_interstitials/content/settings_page_helper.h"
 #include "components/security_interstitials/core/base_safe_browsing_error_ui.h"
--- a/components/safe_browsing/content/browser/web_ui/safe_browsing_ui.cc
+++ b/components/safe_browsing/content/browser/web_ui/safe_browsing_ui.cc
@@ -42,7 +42,6 @@
 #include "components/safe_browsing/core/common/proto/realtimeapi.to_value.h"
 #include "components/safe_browsing/core/common/proto/safebrowsingv5.pb.h"
 #include "components/safe_browsing/core/common/proto/safebrowsingv5.to_value.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/web_ui_constants.h"
 #include "components/strings/grit/components_strings.h"
 #include "components/user_prefs/user_prefs.h"
@@ -135,18 +134,13 @@ void SafeBrowsingUIHandler::GetPrefs(con
   AllowJavascript();
   DCHECK(!args.empty());
   const std::string& callback_id = args[0].GetString();
-  ResolveJavascriptCallback(callback_id,
-                            safe_browsing::GetSafeBrowsingPreferencesList(
-                                user_prefs::UserPrefs::Get(browser_context_)));
+  ResolveJavascriptCallback(callback_id, base::Value::List());
 }
 
 void SafeBrowsingUIHandler::GetPolicies(const base::Value::List& args) {
   AllowJavascript();
   DCHECK(!args.empty());
   const std::string& callback_id = args[0].GetString();
-  ResolveJavascriptCallback(callback_id,
-                            safe_browsing::GetSafeBrowsingPoliciesList(
-                                user_prefs::UserPrefs::Get(browser_context_)));
 }
 
 void SafeBrowsingUIHandler::GetCookie(const base::Value::List& args) {
--- a/components/safe_browsing/core/browser/db/hit_report.h
+++ b/components/safe_browsing/core/browser/db/hit_report.h
@@ -10,7 +10,6 @@
 #include <string>
 
 #include "components/safe_browsing/core/browser/db/v4_protocol_manager_util.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "url/gurl.h"
 
 namespace safe_browsing {
@@ -62,7 +61,6 @@ struct HitReport {
   SBThreatType threat_type;
   ThreatSource threat_source;
 
-  ExtendedReportingLevel extended_reporting_level;
   bool is_enhanced_protection = false;
   bool is_metrics_reporting_active;
 
--- a/components/safe_browsing/core/browser/db/util.h
+++ b/components/safe_browsing/core/browser/db/util.h
@@ -17,7 +17,6 @@
 #include "base/containers/flat_map.h"
 #include "base/trace_event/traced_value.h"
 #include "components/safe_browsing/core/browser/db/v4_protocol_manager_util.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 namespace safe_browsing {
 
--- a/components/safe_browsing/core/browser/db/v4_local_database_manager.cc
+++ b/components/safe_browsing/core/browser/db/v4_local_database_manager.cc
@@ -298,10 +298,9 @@ const V4LocalDatabaseManager*
 scoped_refptr<V4LocalDatabaseManager> V4LocalDatabaseManager::Create(
     const base::FilePath& base_path,
     scoped_refptr<base::SequencedTaskRunner> ui_task_runner,
-    scoped_refptr<base::SequencedTaskRunner> io_task_runner,
-    ExtendedReportingLevelCallback extended_reporting_level_callback) {
+    scoped_refptr<base::SequencedTaskRunner> io_task_runner) {
   return base::WrapRefCounted(new V4LocalDatabaseManager(
-      base_path, extended_reporting_level_callback, std::move(ui_task_runner),
+      base_path, std::move(ui_task_runner),
       std::move(io_task_runner), nullptr));
 }
 
@@ -324,13 +323,11 @@ void V4LocalDatabaseManager::CollectData
 
 V4LocalDatabaseManager::V4LocalDatabaseManager(
     const base::FilePath& base_path,
-    ExtendedReportingLevelCallback extended_reporting_level_callback,
     scoped_refptr<base::SequencedTaskRunner> ui_task_runner,
     scoped_refptr<base::SequencedTaskRunner> io_task_runner,
     scoped_refptr<base::SequencedTaskRunner> task_runner_for_tests)
     : SafeBrowsingDatabaseManager(std::move(ui_task_runner)),
       base_path_(base_path),
-      extended_reporting_level_callback_(extended_reporting_level_callback),
       list_infos_(GetListInfos()),
       task_runner_(task_runner_for_tests
                        ? task_runner_for_tests
@@ -1190,8 +1187,7 @@ void V4LocalDatabaseManager::SetupUpdate
                           weak_factory_.GetWeakPtr());
 
   v4_update_protocol_manager_ = std::make_unique<V4UpdateProtocolManager>(
-      url_loader_factory, config, update_callback,
-      extended_reporting_level_callback_);
+      url_loader_factory, config, update_callback);
 }
 
 void V4LocalDatabaseManager::UpdateRequestCompleted(
--- a/components/safe_browsing/core/browser/db/v4_local_database_manager.h
+++ b/components/safe_browsing/core/browser/db/v4_local_database_manager.h
@@ -41,8 +41,7 @@ class V4LocalDatabaseManager : public Sa
   static scoped_refptr<V4LocalDatabaseManager> Create(
       const base::FilePath& base_path,
       scoped_refptr<base::SequencedTaskRunner> ui_task_runner,
-      scoped_refptr<base::SequencedTaskRunner> io_task_runner,
-      ExtendedReportingLevelCallback extended_reporting_level_callback);
+      scoped_refptr<base::SequencedTaskRunner> io_task_runner);
 
   V4LocalDatabaseManager(const V4LocalDatabaseManager&) = delete;
   V4LocalDatabaseManager& operator=(const V4LocalDatabaseManager&) = delete;
@@ -111,7 +110,6 @@ class V4LocalDatabaseManager : public Sa
   // Must be initialized by calling StartOnUIThread() before using.
   V4LocalDatabaseManager(
       const base::FilePath& base_path,
-      ExtendedReportingLevelCallback extended_reporting_level_callback,
       scoped_refptr<base::SequencedTaskRunner> ui_task_runner,
       scoped_refptr<base::SequencedTaskRunner> io_task_runner,
       scoped_refptr<base::SequencedTaskRunner> task_runner_for_tests);
@@ -411,10 +409,6 @@ class V4LocalDatabaseManager : public Sa
   // ready to process next update.
   DatabaseUpdatedCallback db_updated_callback_;
 
-  // Callback to get the current extended reporting level. Needed by the update
-  // manager.
-  ExtendedReportingLevelCallback extended_reporting_level_callback_;
-
   // The client_state of each list currently being synced. This is updated each
   // time a database update completes, and used to send list client_state
   // information in the full hash request.
--- a/components/safe_browsing/core/browser/db/v4_protocol_manager_util.cc
+++ b/components/safe_browsing/core/browser/db/v4_protocol_manager_util.cc
@@ -91,7 +91,6 @@ void SetSbV4UrlPrefixForTesting(const ch
 
 std::string GetReportUrl(const V4ProtocolConfig& config,
                          const std::string& method,
-                         const ExtendedReportingLevel* reporting_level,
                          const bool is_enhanced_protection) {
   std::string url = base::StringPrintf(
       "%s/%s?client=%s&appver=%s&pver=4.0", kSbReportsURLPrefix, method.c_str(),
@@ -101,9 +100,6 @@ std::string GetReportUrl(const V4Protoco
     base::StringAppendF(&url, "&key=%s",
                         base::EscapeQueryParamValue(api_key, true).c_str());
   }
-  if (reporting_level)
-    url.append(
-        base::StringPrintf("&ext=%d", static_cast<int>(*reporting_level)));
   if (is_enhanced_protection)
     url.append(base::StringPrintf("&enh=%d", is_enhanced_protection));
   return url;
--- a/components/safe_browsing/core/browser/db/v4_protocol_manager_util.h
+++ b/components/safe_browsing/core/browser/db/v4_protocol_manager_util.h
@@ -19,9 +19,10 @@
 #include <vector>
 
 #include "base/containers/flat_set.h"
+#include "base/files/file_path.h"
 #include "base/gtest_prod_util.h"
+#include "base/time/time.h"
 #include "components/safe_browsing/core/browser/db/safebrowsing.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "url/gurl.h"
 
 namespace net {
@@ -87,7 +88,6 @@ V4ProtocolConfig GetV4ProtocolConfig(con
 std::string GetReportUrl(
     const V4ProtocolConfig& config,
     const std::string& method,
-    const ExtendedReportingLevel* reporting_level = nullptr,
     const bool is_enhanced_protection = false);
 
 // Used to specify the type of check to perform in CheckBrowseUrl function.
--- a/components/safe_browsing/core/browser/db/v4_update_protocol_manager.cc
+++ b/components/safe_browsing/core/browser/db/v4_update_protocol_manager.cc
@@ -78,20 +78,8 @@ static const int kV4TimerStartIntervalSe
 // Maximum time, in seconds, to wait for a response to an update request.
 static const int kV4TimerUpdateWaitSecMax = 15 * 60;  // 15 minutes
 
-ChromeClientInfo::SafeBrowsingReportingPopulation GetReportingLevelProtoValue(
-    ExtendedReportingLevel reporting_level) {
-  switch (reporting_level) {
-    case SBER_LEVEL_OFF:
-      return ChromeClientInfo::OPT_OUT;
-    case SBER_LEVEL_LEGACY:
-      return ChromeClientInfo::EXTENDED;
-    case SBER_LEVEL_SCOUT:
-      return ChromeClientInfo::SCOUT;
-    case SBER_LEVEL_ENHANCED_PROTECTION:
-      return ChromeClientInfo::ENHANCED_PROTECTION;
-    default:
-      NOTREACHED() << "Unexpected reporting_level!";
-  }
+ChromeClientInfo::SafeBrowsingReportingPopulation GetReportingLevelProtoValue() {
+  return ChromeClientInfo::OPT_OUT;
 }
 
 // V4UpdateProtocolManager implementation --------------------------------
@@ -104,8 +92,7 @@ void V4UpdateProtocolManager::ResetUpdat
 V4UpdateProtocolManager::V4UpdateProtocolManager(
     scoped_refptr<network::SharedURLLoaderFactory> url_loader_factory,
     const V4ProtocolConfig& config,
-    V4UpdateCallback update_callback,
-    ExtendedReportingLevelCallback extended_reporting_level_callback)
+    V4UpdateCallback update_callback)
     : update_error_count_(0),
       update_back_off_mult_(1),
       next_update_interval_(
@@ -113,8 +100,7 @@ V4UpdateProtocolManager::V4UpdateProtoco
                                       kV4TimerStartIntervalSecMax))),
       config_(config),
       url_loader_factory_(url_loader_factory),
-      update_callback_(update_callback),
-      extended_reporting_level_callback_(extended_reporting_level_callback) {
+      update_callback_(update_callback) {
   // Do not auto-schedule updates. Let the owner (V4LocalDatabaseManager) do it
   // when it is ready to process updates.
 }
@@ -209,11 +195,6 @@ std::string V4UpdateProtocolManager::Get
         RICE);
   }
 
-  if (!extended_reporting_level_callback_.is_null()) {
-    request.mutable_chrome_client_info()->set_reporting_population(
-        GetReportingLevelProtoValue(extended_reporting_level_callback_.Run()));
-  }
-
   V4ProtocolManagerUtil::SetClientInfoFromConfig(request.mutable_client(),
                                                  config_);
 
--- a/components/safe_browsing/core/browser/db/v4_update_protocol_manager.h
+++ b/components/safe_browsing/core/browser/db/v4_update_protocol_manager.h
@@ -24,7 +24,6 @@
 #include "components/safe_browsing/core/browser/db/util.h"
 #include "components/safe_browsing/core/browser/db/v4_protocol_manager_util.h"
 #include "components/safe_browsing/core/common/proto/webui.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 class GURL;
 
@@ -42,9 +41,6 @@ namespace safe_browsing {
 using V4UpdateCallback =
     base::RepeatingCallback<void(std::unique_ptr<ParsedServerResponse>)>;
 
-using ExtendedReportingLevelCallback =
-    base::RepeatingCallback<ExtendedReportingLevel()>;
-
 class V4UpdateProtocolManager {
  public:
   V4UpdateProtocolManager(const V4UpdateProtocolManager&) = delete;
@@ -59,8 +55,7 @@ class V4UpdateProtocolManager {
   V4UpdateProtocolManager(
       scoped_refptr<network::SharedURLLoaderFactory> url_loader_factory,
       const V4ProtocolConfig& config,
-      V4UpdateCallback update_callback,
-      ExtendedReportingLevelCallback extended_reporting_level_callback);
+      V4UpdateCallback update_callback);
 
   void OnURLLoaderComplete(std::unique_ptr<std::string> response_body);
 
@@ -185,8 +180,6 @@ class V4UpdateProtocolManager {
   // complete.
   base::OneShotTimer timeout_timer_;
 
-  ExtendedReportingLevelCallback extended_reporting_level_callback_;
-
   SEQUENCE_CHECKER(sequence_checker_);
 };
 
--- a/components/safe_browsing/core/browser/password_protection/password_protection_service_base.h
+++ b/components/safe_browsing/core/browser/password_protection/password_protection_service_base.h
@@ -29,7 +29,6 @@
 #include "components/safe_browsing/core/browser/safe_browsing_metrics_collector.h"
 #include "components/safe_browsing/core/browser/safe_browsing_token_fetcher.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/sessions/core/session_id.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "services/network/public/cpp/shared_url_loader_factory.h"
@@ -149,10 +148,6 @@ class PasswordProtectionServiceBase : pu
   // Returns if the warning UI is enabled.
   bool IsWarningEnabled(ReusedPasswordAccountType password_type);
 
-  // Returns the pref value of password protection warning trigger.
-  virtual PasswordProtectionTrigger GetPasswordProtectionWarningTriggerPref(
-      ReusedPasswordAccountType password_type) const = 0;
-
   // If |url| matches Safe Browsing allowlist domains, password protection
   // change password URL, or password protection login URLs in the enterprise
   // policy.
--- a/components/safe_browsing/core/browser/ping_manager.cc
+++ b/components/safe_browsing/core/browser/ping_manager.cc
@@ -474,7 +474,7 @@ GURL PingManager::SafeBrowsingHitUrl(
          hit_report->threat_type == SB_THREAT_TYPE_URL_BINARY_MALWARE ||
          hit_report->threat_type == SB_THREAT_TYPE_URL_CLIENT_SIDE_PHISHING);
   std::string url =
-      GetReportUrl(config_, "report", &hit_report->extended_reporting_level,
+      GetReportUrl(config_, "report",
                    hit_report->is_enhanced_protection);
   std::string threat_list = "none";
   switch (hit_report->threat_type) {
--- a/components/safe_browsing/core/browser/realtime/policy_engine.cc
+++ b/components/safe_browsing/core/browser/realtime/policy_engine.cc
@@ -11,7 +11,6 @@
 #include "components/enterprise/connectors/core/common.h"
 #include "components/enterprise/connectors/core/connectors_prefs.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "components/unified_consent/pref_names.h"
 #include "components/user_prefs/user_prefs.h"
@@ -56,7 +55,7 @@ bool RealTimePolicyEngine::IsUserMbbOpte
 
 // static
 bool RealTimePolicyEngine::IsUserEpOptedIn(PrefService* pref_service) {
-  return IsEnhancedProtectionEnabled(*pref_service);
+  return false;
 }
 
 // static
@@ -118,15 +117,7 @@ bool RealTimePolicyEngine::CanPerformEnt
     return false;
   }
 
-  if (pref_service->GetInteger(
-          enterprise_connectors::kEnterpriseRealTimeUrlCheckMode) !=
-      enterprise_connectors::REAL_TIME_CHECK_FOR_MAINFRAME_ENABLED) {
-    base::UmaHistogramEnumeration("SafeBrowsing.RT.ConsumerVersionReason",
-                                  ConsumerVersionReason::POLICY_DISABLED);
-    return false;
-  }
-
-  return true;
+  return false;
 }
 
 }  // namespace safe_browsing
--- a/components/safe_browsing/core/browser/realtime/url_lookup_service.cc
+++ b/components/safe_browsing/core/browser/realtime/url_lookup_service.cc
@@ -23,7 +23,6 @@
 #include "components/safe_browsing/core/browser/utils/safe_browsing_web_app_utils.h"
 #include "components/safe_browsing/core/browser/verdict_cache_manager.h"
 #include "components/safe_browsing/core/common/proto/csd.pb.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "components/unified_consent/pref_names.h"
 #include "net/base/ip_address.h"
@@ -103,8 +102,7 @@ bool RealTimeUrlLookupService::CanSendPa
 }
 
 bool RealTimeUrlLookupService::CanIncludeSubframeUrlInReferrerChain() const {
-  return IsEnhancedProtectionEnabled(*pref_service_) &&
-         CanPerformFullURLLookup();
+  return false;
 }
 
 bool RealTimeUrlLookupService::CanCheckSafeBrowsingDb() const {
@@ -121,9 +119,7 @@ bool RealTimeUrlLookupService::CanCheckS
 }
 
 bool RealTimeUrlLookupService::CanSendRTSampleRequest() const {
-  return IsExtendedReportingEnabled(*pref_service_) &&
-         (bypass_protego_probability_for_tests_ ||
-          base::RandDouble() <= kProbabilityForSendingSampledRequests);
+  return false;
 }
 
 std::string RealTimeUrlLookupService::GetUserEmail() const {
@@ -235,14 +231,6 @@ RealTimeUrlLookupService::GetMinAllowedT
 
 void RealTimeUrlLookupService::MaybeLogLastProtegoPingTimeToPrefs(
     bool sent_with_token) {
-  // `pref_service_` can be null in tests.
-  if (pref_service_ && IsEnhancedProtectionEnabled(*pref_service_)) {
-    pref_service_->SetTime(
-        sent_with_token
-            ? prefs::kSafeBrowsingEsbProtegoPingWithTokenLastLogTime
-            : prefs::kSafeBrowsingEsbProtegoPingWithoutTokenLastLogTime,
-        base::Time::Now());
-  }
 }
 
 void RealTimeUrlLookupService::MaybeLogProtegoPingCookieHistograms(
--- a/components/safe_browsing/core/browser/realtime/url_lookup_service_base.cc
+++ b/components/safe_browsing/core/browser/realtime/url_lookup_service_base.cc
@@ -22,7 +22,6 @@
 #include "components/safe_browsing/core/browser/referrer_chain_provider.h"
 #include "components/safe_browsing/core/browser/verdict_cache_manager.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_constants.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "net/base/ip_address.h"
--- a/components/safe_browsing/core/browser/safe_browsing_metrics_collector.cc
+++ b/components/safe_browsing/core/browser/safe_browsing_metrics_collector.cc
@@ -15,13 +15,11 @@
 #include "components/prefs/pref_service.h"
 #include "components/prefs/scoped_user_pref_update.h"
 #include "components/safe_browsing/core/browser/db/hit_report.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 namespace {
 
 using EventType = safe_browsing::SafeBrowsingMetricsCollector::EventType;
 using UserState = safe_browsing::SafeBrowsingMetricsCollector::UserState;
-using SafeBrowsingState = safe_browsing::SafeBrowsingState;
 
 const int kMetricsLoggingIntervalDay = 1;
 
@@ -62,11 +60,6 @@ SafeBrowsingMetricsCollector::SafeBrowsi
     PrefService* pref_service)
     : pref_service_(pref_service) {
   pref_change_registrar_.Init(pref_service_);
-  pref_change_registrar_.Add(
-      prefs::kSafeBrowsingEnhanced,
-      base::BindRepeating(
-          &SafeBrowsingMetricsCollector::OnEnhancedProtectionPrefChanged,
-          base::Unretained(this)));
 }
 
 void SafeBrowsingMetricsCollector::Shutdown() {
@@ -74,16 +67,6 @@ void SafeBrowsingMetricsCollector::Shutd
 }
 
 void SafeBrowsingMetricsCollector::StartLogging() {
-  base::TimeDelta log_interval = base::Days(kMetricsLoggingIntervalDay);
-  base::Time last_log_time =
-      base::Time::FromDeltaSinceWindowsEpoch(base::Seconds(
-          pref_service_->GetInt64(prefs::kSafeBrowsingMetricsLastLogTime)));
-  base::TimeDelta delay = base::Time::Now() - last_log_time;
-  if (delay >= log_interval) {
-    LogMetricsAndScheduleNextLogging();
-  } else {
-    ScheduleNextLoggingAfterInterval(log_interval - delay);
-  }
 }
 
 void SafeBrowsingMetricsCollector::LogMetricsAndScheduleNextLogging() {
@@ -92,77 +75,10 @@ void SafeBrowsingMetricsCollector::LogMe
   MaybeLogDailyEsbProtegoPingSent();
   RemoveOldEventsFromPref();
 
-  pref_service_->SetInt64(
-      prefs::kSafeBrowsingMetricsLastLogTime,
-      base::Time::Now().ToDeltaSinceWindowsEpoch().InSeconds());
   ScheduleNextLoggingAfterInterval(base::Days(kMetricsLoggingIntervalDay));
 }
 
-safe_browsing::SafeBrowsingMetricsCollector::ProtegoPingType
-SafeBrowsingMetricsCollector::GetMostRecentPingType(
-    base::Time last_ping_with_token,
-    base::Time last_ping_without_token,
-    base::TimeDelta time_delta) {
-  // If a ping with token was sent within the last time_delta,
-  // the most recent ping type is kWithToken.
-  // If both last_ping_with_token and last_ping_without_token are present,
-  // we log kWithToken instead of kWithoutToken because if a token has been
-  // sent before, we are certain that this account is a signed in account
-  // and the server has received the token.
-  // The kWithoutToken ping could be sent after the account logged out.
-  if (base::Time::Now() - last_ping_with_token < time_delta) {
-    return ProtegoPingType::kWithToken;
-  } else if (base::Time::Now() - last_ping_without_token < time_delta) {
-    return ProtegoPingType::kWithoutToken;
-  }
-  return ProtegoPingType::kNone;
-}
-
 void SafeBrowsingMetricsCollector::MaybeLogDailyEsbProtegoPingSent() {
-  if (GetSafeBrowsingState(*pref_service_) !=
-      SafeBrowsingState::ENHANCED_PROTECTION) {
-    return;
-  }
-
-  auto last_ping_with_token = pref_service_->GetTime(
-      prefs::kSafeBrowsingEsbProtegoPingWithTokenLastLogTime);
-  auto last_ping_without_token = pref_service_->GetTime(
-      prefs::kSafeBrowsingEsbProtegoPingWithoutTokenLastLogTime);
-  auto most_recent_ping_type = last_ping_with_token > last_ping_without_token
-                                   ? ProtegoPingType::kWithToken
-                                   : ProtegoPingType::kWithoutToken;
-  auto most_recent_ping_time =
-      std::max(last_ping_with_token, last_ping_without_token);
-
-  auto most_recent_collector_run_time = PrefValueToTime(
-      pref_service_->GetValue(prefs::kSafeBrowsingMetricsLastLogTime));
-
-  bool sent_ping_since_last_collector_run =
-      most_recent_ping_time > most_recent_collector_run_time;
-  base::UmaHistogramEnumeration(
-      "SafeBrowsing.Enhanced.ProtegoRequestSentInLast24Hours",
-      sent_ping_since_last_collector_run ? most_recent_ping_type
-                                         : ProtegoPingType::kNone);
-
-  auto logged_ping_last_24_hours_type = GetMostRecentPingType(
-      last_ping_with_token, last_ping_without_token, base::Hours(24));
-  base::UmaHistogramEnumeration(
-      "SafeBrowsing.Enhanced.ProtegoRequestSentInLast24Hours2",
-      logged_ping_last_24_hours_type);
-
-  auto logged_ping_last_7_days_type = GetMostRecentPingType(
-      last_ping_with_token, last_ping_without_token, base::Days(7));
-
-  base::UmaHistogramEnumeration(
-      "SafeBrowsing.Enhanced.ProtegoRequestSentInLast7Days",
-      logged_ping_last_7_days_type);
-
-  auto logged_ping_last_28_days_type = GetMostRecentPingType(
-      last_ping_with_token, last_ping_without_token, base::Days(28));
-
-  base::UmaHistogramEnumeration(
-      "SafeBrowsing.Enhanced.ProtegoRequestSentInLast28Days",
-      logged_ping_last_28_days_type);
 }
 
 void SafeBrowsingMetricsCollector::ScheduleNextLoggingAfterInterval(
@@ -174,75 +90,16 @@ void SafeBrowsingMetricsCollector::Sched
 }
 
 void SafeBrowsingMetricsCollector::LogDailyOptInMetrics() {
-  base::UmaHistogramEnumeration("SafeBrowsing.Pref.Daily.SafeBrowsingState",
-                                GetSafeBrowsingState(*pref_service_));
-  base::UmaHistogramBoolean("SafeBrowsing.Pref.Daily.Extended",
-                            IsExtendedReportingEnabled(*pref_service_));
-  base::UmaHistogramBoolean("SafeBrowsing.Pref.Daily.SafeBrowsingModeManaged",
-                            IsSafeBrowsingPolicyManaged(*pref_service_));
-  base::UmaHistogramBoolean(
-      "SafeBrowsing.Pref.Daily.PasswordLeakToggle",
-      pref_service_->GetBoolean(
-          password_manager::prefs::kPasswordLeakDetectionEnabled));
 }
 
 void SafeBrowsingMetricsCollector::LogDailyEventMetrics() {
-  SafeBrowsingState sb_state = GetSafeBrowsingState(*pref_service_);
-  if (sb_state == SafeBrowsingState::NO_SAFE_BROWSING) {
-    return;
-  }
-  UserState user_state = GetUserState();
-
-  int total_bypass_count = 0;
-  int total_security_sensitive_event_count = 0;
-  for (int event_type_int = 0; event_type_int <= EventType::kMaxValue;
-       event_type_int += 1) {
-    EventType event_type = static_cast<EventType>(event_type_int);
-    if (IsBypassEventType(event_type)) {
-      int bypass_count = GetEventCountSince(user_state, event_type,
-                                            base::Time::Now() - base::Days(28));
-      total_bypass_count += bypass_count;
-    }
-    if (IsSecuritySensitiveEventType(event_type)) {
-      int security_sensitive_event_count = GetEventCountSince(
-          user_state, event_type, base::Time::Now() - base::Days(28));
-      total_security_sensitive_event_count += security_sensitive_event_count;
-    }
-  }
-  base::UmaHistogramCounts100("SafeBrowsing.Daily.BypassCountLast28Days." +
-                                  GetUserStateMetricSuffix(user_state) +
-                                  ".AllEvents",
-                              total_bypass_count);
-  base::UmaHistogramCounts100(
-      "SafeBrowsing.Daily.SecuritySensitiveCountLast28Days." +
-          GetUserStateMetricSuffix(user_state) + ".AllEvents",
-      total_security_sensitive_event_count);
 }
 
 void SafeBrowsingMetricsCollector::RemoveOldEventsFromPref() {
-  ScopedDictPrefUpdate update(pref_service_,
-                              prefs::kSafeBrowsingEventTimestamps);
-  base::Value::Dict& mutable_state_dict = update.Get();
-
-  for (auto state_map : mutable_state_dict) {
-    for (auto event_map : state_map.second.GetDict()) {
-      event_map.second.GetList().EraseIf([&](const auto& timestamp) {
-        return base::Time::Now() - PrefValueToTime(timestamp) >
-               base::Days(kEventMaxDurationDay);
-      });
-    }
-  }
 }
 
 void SafeBrowsingMetricsCollector::AddSafeBrowsingEventToPref(
     EventType event_type) {
-  SafeBrowsingState sb_state = GetSafeBrowsingState(*pref_service_);
-  // Skip the event if Safe Browsing is disabled.
-  if (sb_state == SafeBrowsingState::NO_SAFE_BROWSING) {
-    return;
-  }
-
-  AddSafeBrowsingEventAndUserStateToPref(GetUserState(), event_type);
 }
 
 void SafeBrowsingMetricsCollector::AddBypassEventToPref(
@@ -285,15 +142,7 @@ std::optional<base::Time> SafeBrowsingMe
 std::optional<base::Time> SafeBrowsingMetricsCollector::GetLatestEventTimestamp(
     EventTypeFilter event_type_filter) {
   // Events are not logged when Safe Browsing is disabled.
-  SafeBrowsingState sb_state = GetSafeBrowsingState(*pref_service_);
-  if (sb_state == SafeBrowsingState::NO_SAFE_BROWSING) {
     return std::nullopt;
-  }
-
-  const std::optional<Event> event =
-      GetLatestEventFromEventTypeFilter(GetUserState(), event_type_filter);
-  return event ? std::optional<base::Time>(event.value().timestamp)
-               : std::nullopt;
 }
 
 std::optional<base::Time>
@@ -305,45 +154,15 @@ SafeBrowsingMetricsCollector::GetLatestS
 void SafeBrowsingMetricsCollector::AddSafeBrowsingEventAndUserStateToPref(
     UserState user_state,
     EventType event_type) {
-  ScopedDictPrefUpdate update(pref_service_,
-                              prefs::kSafeBrowsingEventTimestamps);
-  base::Value::Dict& mutable_state_dict = update.Get();
-  base::Value::Dict* event_dict =
-      mutable_state_dict.EnsureDict(UserStateToPrefKey(user_state));
-  base::Value::List* timestamps =
-      event_dict->EnsureList(EventTypeToPrefKey(event_type));
-
-  // Remove the oldest timestamp if the length of the timestamps hits the limit.
-  while (timestamps->size() >= kTimestampsMaxLength) {
-    timestamps->erase(timestamps->begin());
-  }
-
-  timestamps->Append(TimeToPrefValue(base::Time::Now()));
 }
 
 void SafeBrowsingMetricsCollector::OnEnhancedProtectionPrefChanged() {
-  // Pref changed by policy is not initiated by users, so this case is ignored.
-  if (IsSafeBrowsingPolicyManaged(*pref_service_)) {
-    return;
-  }
-
-  if (!pref_service_->GetBoolean(prefs::kSafeBrowsingEnhanced)) {
-    AddSafeBrowsingEventAndUserStateToPref(UserState::kEnhancedProtection,
-                                           EventType::USER_STATE_DISABLED);
-    LogEnhancedProtectionDisabledMetrics();
-  } else {
-    AddSafeBrowsingEventAndUserStateToPref(UserState::kEnhancedProtection,
-                                           EventType::USER_STATE_ENABLED);
-  }
 }
 
 const base::Value::Dict*
 SafeBrowsingMetricsCollector::GetSafeBrowsingEventDictionary(
     UserState user_state) {
-  const base::Value::Dict& state_dict =
-      pref_service_->GetDict(prefs::kSafeBrowsingEventTimestamps);
-
-  return state_dict.FindDict(UserStateToPrefKey(user_state));
+  return nullptr;
 }
 
 std::optional<SafeBrowsingMetricsCollector::Event>
@@ -470,22 +289,6 @@ int SafeBrowsingMetricsCollector::GetEve
   });
 }
 
-UserState SafeBrowsingMetricsCollector::GetUserState() {
-  if (IsSafeBrowsingPolicyManaged(*pref_service_)) {
-    return UserState::kManaged;
-  }
-
-  SafeBrowsingState sb_state = GetSafeBrowsingState(*pref_service_);
-  switch (sb_state) {
-    case SafeBrowsingState::ENHANCED_PROTECTION:
-      return UserState::kEnhancedProtection;
-    case SafeBrowsingState::STANDARD_PROTECTION:
-      return UserState::kStandardProtection;
-    case SafeBrowsingState::NO_SAFE_BROWSING:
-      NOTREACHED() << "Unexpected Safe Browsing state.";
-  }
-}
-
 bool SafeBrowsingMetricsCollector::IsBypassEventType(const EventType& type) {
   switch (type) {
     case EventType::USER_STATE_DISABLED:
--- a/components/safe_browsing/core/browser/tailored_security_service/tailored_security_service.cc
+++ b/components/safe_browsing/core/browser/tailored_security_service/tailored_security_service.cc
@@ -24,7 +24,6 @@
 #include "components/safe_browsing/core/browser/tailored_security_service/tailored_security_service_util.h"
 #include "components/safe_browsing/core/common/features.h"
 #include "components/safe_browsing/core/common/safe_browsing_policy_handler.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "components/signin/public/identity_manager/access_token_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
@@ -241,15 +240,6 @@ TailoredSecurityService::TailoredSecurit
     : identity_manager_(identity_manager),
       sync_service_(sync_service),
       prefs_(prefs) {
-  // `prefs` can be nullptr in unit tests.
-  if (prefs_) {
-    pref_registrar_.Init(prefs_);
-    pref_registrar_.Add(
-        prefs::kAccountTailoredSecurityUpdateTimestamp,
-        base::BindRepeating(
-            &TailoredSecurityService::TailoredSecurityTimestampUpdateCallback,
-            weak_ptr_factory_.GetWeakPtr()));
-  }
 }
 
 TailoredSecurityService::~TailoredSecurityService() {
@@ -419,26 +409,6 @@ void TailoredSecurityService::MaybeNotif
     return;
   }
 
-  if (is_enabled && IsEnhancedProtectionEnabled(*prefs())) {
-    RecordEnabledNotificationResult(
-        TailoredSecurityNotificationResult::kEnhancedProtectionAlreadyEnabled);
-    SaveRetryState(TailoredSecurityRetryState::NO_RETRY_NEEDED);
-    return;
-  }
-
-  if (is_enabled && !IsEnhancedProtectionEnabled(*prefs())) {
-    for (auto& observer : observer_list_) {
-      observer.OnSyncNotificationMessageRequest(true);
-    }
-  }
-
-  if (!is_enabled && IsEnhancedProtectionEnabled(*prefs()) &&
-      prefs()->GetBoolean(
-          prefs::kEnhancedProtectionEnabledViaTailoredSecurity)) {
-    for (auto& observer : observer_list_) {
-      observer.OnSyncNotificationMessageRequest(false);
-    }
-  }
 }
 
 bool TailoredSecurityService::HistorySyncEnabledForUser() {
--- a/components/safe_browsing/core/browser/tailored_security_service/tailored_security_service_observer_util.cc
+++ b/components/safe_browsing/core/browser/tailored_security_service/tailored_security_service_observer_util.cc
@@ -8,7 +8,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/core/browser/tailored_security_service/tailored_security_service.h"
 #include "components/safe_browsing/core/common/safe_browsing_policy_handler.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/sync/base/user_selectable_type.h"
 #include "components/sync/service/sync_service.h"
 #include "components/sync/service/sync_user_settings.h"
@@ -17,39 +16,12 @@
 namespace safe_browsing {
 
 bool CanQueryTailoredSecurityForUrl(GURL url) {
-  return google_util::IsGoogleDomainUrl(
-             url, google_util::ALLOW_SUBDOMAIN,
-             google_util::ALLOW_NON_STANDARD_PORTS) ||
-         google_util::IsYoutubeDomainUrl(url, google_util::ALLOW_SUBDOMAIN,
-                                         google_util::ALLOW_NON_STANDARD_PORTS);
+  return false;
 }
 
 bool CanShowUnconsentedTailoredSecurityDialog(syncer::SyncService* sync_service,
                                               PrefService* prefs) {
-  if (IsEnhancedProtectionEnabled(*prefs))
     return false;
-
-  if (!sync_service) {
-    return false;
-  }
-
-  bool sync_history_enabled =
-      sync_service->GetUserSettings()->GetSelectedTypes().Has(
-          syncer::UserSelectableType::kHistory);
-  if (sync_history_enabled) {
-    return false;
-  }
-
-  if (prefs->GetBoolean(prefs::kAccountTailoredSecurityShownNotification)) {
-    return false;
-  }
-
-  if (SafeBrowsingPolicyHandler::IsSafeBrowsingProtectionLevelSetByPolicy(
-          prefs)) {
-    return false;
-  }
-
-  return true;
 }
 
 }  // namespace safe_browsing
--- a/components/safe_browsing/core/common/BUILD.gn
+++ b/components/safe_browsing/core/common/BUILD.gn
@@ -6,22 +6,8 @@ import("//build/config/features.gni")
 import("//components/safe_browsing/buildflags.gni")
 import("//mojo/public/tools/bindings/mojom.gni")
 
-static_library("safe_browsing_prefs") {
-  sources = [
-    "safe_browsing_prefs.cc",
-    "safe_browsing_prefs.h",
-  ]
-
-  configs += [ "//build/config/compiler:wexit_time_destructors" ]
-
-  deps = [
-    ":features",
-    "//components/pref_registry",
-    "//components/prefs",
-    "//net",
-  ]
-
-  public_deps = [ "//base" ]
+group("safe_browsing_prefs") {
+  # SafeBrowsing prefs are disabled
 }
 
 source_set("safe_browsing_policy_handler") {
--- a/components/safe_browsing/core/common/hashprefix_realtime/hash_realtime_utils.cc
+++ b/components/safe_browsing/core/common/hashprefix_realtime/hash_realtime_utils.cc
@@ -11,7 +11,6 @@
 #include "build/branding_buildflags.h"
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/utils.h"
 #include "components/variations/pref_names.h"
 #include "components/variations/service/variations_service.h"
@@ -99,71 +98,13 @@ HashRealTimeSelection DetermineHashRealT
     std::optional<std::string> latest_country,
     bool log_usage_histograms,
     bool are_background_lookups_allowed) {
-  // All prefs used in this method must match the ones returned by
-  // |GetHashRealTimeSelectionConfiguringPrefs| so that consumers listening for
-  // changes can receive them correctly.
-  bool only_background_lookup_eligible =
-      base::FeatureList::IsEnabled(kHashPrefixRealTimeLookupsSamplePing) &&
-      safe_browsing::GetSafeBrowsingState(*prefs) ==
-          SafeBrowsingState::ENHANCED_PROTECTION &&
-      are_background_lookups_allowed;
-
-  struct Requirement {
-    std::string failed_requirement_histogram_suffix;
-    bool passes_requirement;
-  } requirements[] = {
-      {"IneligibleForSessionOrLocation",
-       hash_realtime_utils::IsHashRealTimeLookupEligibleInSessionAndLocation(
-           latest_country)},
-      {"OffTheRecord", !is_off_the_record},
-      {"IneligibleForSafeBrowsingState",
-       safe_browsing::GetSafeBrowsingState(*prefs) ==
-               SafeBrowsingState::STANDARD_PROTECTION ||
-           only_background_lookup_eligible},
-      {"NotAllowedByPolicy",
-       safe_browsing::AreHashPrefixRealTimeLookupsAllowedByPolicy(*prefs)}};
-  bool can_do_lookup = true;
-  for (const auto& requirement : requirements) {
-    if (!requirement.passes_requirement) {
-      can_do_lookup = false;
-    }
-    if (log_usage_histograms) {
-      base::UmaHistogramBoolean(
-          base::StrCat({"SafeBrowsing.HPRT.Ineligible.",
-                        requirement.failed_requirement_histogram_suffix}),
-          !requirement.passes_requirement);
-    }
-  }
-  if (log_usage_histograms) {
-    base::UmaHistogramBoolean(
-        "SafeBrowsing.HPRT.Ineligible.NoGoogleChromeBranding",
-        !HasGoogleChromeBranding());
-    base::UmaHistogramBoolean(
-        "SafeBrowsing.HPRT.Ineligible.FeatureOff",
-        !base::FeatureList::IsEnabled(kHashPrefixRealTimeLookups));
-    base::UmaHistogramBoolean(
-        "SafeBrowsing.HPRT.Ineligible.IneligibleForLocation",
-        !IsHashRealTimeLookupEligibleInLocation(latest_country));
-  }
-  return can_do_lookup
-             ?
-#if BUILDFLAG(IS_ANDROID)
-             (only_background_lookup_eligible
-                  ? HashRealTimeSelection::kDatabaseManagerBackgroundOnly
-                  : HashRealTimeSelection::kDatabaseManager)
-#else
-             (only_background_lookup_eligible
-                  ? HashRealTimeSelection::kHashRealTimeServiceBackgroundOnly
-                  : HashRealTimeSelection::kHashRealTimeService)
-#endif
-             : HashRealTimeSelection::kNone;
+  return HashRealTimeSelection::kNone;
 }
 
 HashRealTimeSelectionConfiguringPrefs
 GetHashRealTimeSelectionConfiguringPrefs() {
   std::vector<const char*> profile_prefs = {
-      prefs::kSafeBrowsingEnabled, prefs::kSafeBrowsingEnhanced,
-      prefs::kHashPrefixRealTimeChecksAllowedByPolicy};
+      };
   // |kVariationsCountry| is used by |VariationsService::GetLatestCountry|.
   std::vector<const char*> local_state_prefs = {
       variations::prefs::kVariationsCountry};
--- a/components/safe_browsing/core/common/safe_browsing_policy_handler.cc
+++ b/components/safe_browsing/core/common/safe_browsing_policy_handler.cc
@@ -13,7 +13,6 @@
 #include "components/policy/policy_constants.h"
 #include "components/prefs/pref_service.h"
 #include "components/prefs/pref_value_map.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/strings/grit/components_strings.h"
 
 namespace safe_browsing {
@@ -181,34 +180,14 @@ void SafeBrowsingPolicyHandler::ApplyPol
 
   if (!value.has_value())
     return;
-
-  switch (value.value()) {
-    case ProtectionLevel::kNoProtection:
-      prefs->SetBoolean(prefs::kSafeBrowsingEnabled, false);
-      prefs->SetBoolean(prefs::kSafeBrowsingEnhanced, false);
-      return;
-    case ProtectionLevel::kStandardProtection:
-      prefs->SetBoolean(prefs::kSafeBrowsingEnabled, true);
-      prefs->SetBoolean(prefs::kSafeBrowsingEnhanced, false);
-      return;
-    case ProtectionLevel::kEnhancedProtection:
-      // |kSafeBrowsingEnhanced| is enabled, but so is
-      // |kSafeBrowsingEnabled| because the extensions API should see Safe
-      // Browsing as enabled. See https://crbug.com/1064722 for more background.
-      prefs->SetBoolean(prefs::kSafeBrowsingEnabled, true);
-      prefs->SetBoolean(prefs::kSafeBrowsingEnhanced, true);
-      return;
-  }
 }
 
 // static
 SafeBrowsingPolicyHandler::ProtectionLevel
 SafeBrowsingPolicyHandler::GetSafeBrowsingProtectionLevel(
     const PrefService* pref_sevice) {
-  bool safe_browsing_enhanced =
-      pref_sevice->GetBoolean(prefs::kSafeBrowsingEnhanced);
-  bool safe_browsing_enabled =
-      pref_sevice->GetBoolean(prefs::kSafeBrowsingEnabled);
+  bool safe_browsing_enhanced = false;
+  bool safe_browsing_enabled = false;
 
   if (safe_browsing_enhanced)
     return ProtectionLevel::kEnhancedProtection;
@@ -222,10 +201,8 @@ SafeBrowsingPolicyHandler::GetSafeBrowsi
 // static
 bool SafeBrowsingPolicyHandler::IsSafeBrowsingProtectionLevelSetByPolicy(
     const PrefService* pref_service) {
-  bool is_safe_browsing_enabled_managed =
-      pref_service->IsManagedPreference(prefs::kSafeBrowsingEnabled);
-  bool is_safe_browsing_enhanced_managed =
-      pref_service->IsManagedPreference(prefs::kSafeBrowsingEnhanced);
+  bool is_safe_browsing_enabled_managed = false;
+  bool is_safe_browsing_enhanced_managed = false;
   DCHECK_EQ(is_safe_browsing_enabled_managed,
             is_safe_browsing_enhanced_managed);
   return is_safe_browsing_enabled_managed && is_safe_browsing_enhanced_managed;
--- a/components/safe_browsing/core/common/safe_browsing_prefs_unittest.cc
+++ b/components/safe_browsing/core/common/safe_browsing_prefs_unittest.cc
@@ -2,7 +2,6 @@
 // Use of this source code is governed by a BSD-style license that can be
 // found in the LICENSE file.
 
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 
 #include <string>
 
--- a/components/safety_check/safety_check.cc
+++ b/components/safety_check/safety_check.cc
@@ -4,26 +4,9 @@
 
 #include "components/safety_check/safety_check.h"
 
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
-
 namespace safety_check {
 
 SafeBrowsingStatus CheckSafeBrowsing(PrefService* pref_service) {
-  const PrefService::Preference* enabled_pref =
-      pref_service->FindPreference(prefs::kSafeBrowsingEnabled);
-  bool is_sb_enabled = pref_service->GetBoolean(prefs::kSafeBrowsingEnabled);
-  bool is_sb_managed = enabled_pref->IsManaged();
-
-  if (is_sb_enabled && pref_service->GetBoolean(prefs::kSafeBrowsingEnhanced))
-    return SafeBrowsingStatus::kEnabledEnhanced;
-  if (is_sb_enabled && is_sb_managed)
-    return SafeBrowsingStatus::kEnabledStandard;
-  if (is_sb_enabled && !is_sb_managed)
-    return SafeBrowsingStatus::kEnabledStandardAvailableEnhanced;
-  if (is_sb_managed)
-    return SafeBrowsingStatus::kDisabledByAdmin;
-  if (enabled_pref->IsExtensionControlled())
-    return SafeBrowsingStatus::kDisabledByExtension;
   return SafeBrowsingStatus::kDisabled;
 }
 
--- a/components/security_interstitials/content/bad_clock_blocking_page.cc
+++ b/components/security_interstitials/content/bad_clock_blocking_page.cc
@@ -7,7 +7,6 @@
 #include <utility>
 
 #include "base/strings/string_number_conversions.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_interstitials/content/security_interstitial_controller_client.h"
 #include "components/security_interstitials/content/security_interstitial_page.h"
 #include "components/security_interstitials/core/bad_clock_ui.h"
--- a/components/security_interstitials/content/captive_portal_blocking_page.cc
+++ b/components/security_interstitials/content/captive_portal_blocking_page.cc
@@ -15,7 +15,6 @@
 #include "build/build_config.h"
 #include "components/captive_portal/core/captive_portal_detector.h"
 #include "components/captive_portal/core/captive_portal_metrics.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_interstitials/content/security_interstitial_controller_client.h"
 #include "components/security_interstitials/core/controller_client.h"
 #include "components/security_interstitials/core/metrics_helper.h"
--- a/components/security_interstitials/content/mitm_software_blocking_page.cc
+++ b/components/security_interstitials/content/mitm_software_blocking_page.cc
@@ -7,7 +7,6 @@
 #include <utility>
 
 #include "base/strings/string_number_conversions.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_interstitials/content/security_interstitial_controller_client.h"
 #include "components/security_interstitials/content/security_interstitial_page.h"
 #include "components/security_interstitials/core/metrics_helper.h"
--- a/components/security_interstitials/content/security_interstitial_controller_client.cc
+++ b/components/security_interstitials/content/security_interstitial_controller_client.cc
@@ -8,7 +8,6 @@
 
 #include "components/prefs/pref_service.h"
 #include "components/safe_browsing/core/common/features.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/safe_browsing/core/common/safebrowsing_referral_methods.h"
 #include "components/security_interstitials/content/security_interstitial_page.h"
 #include "components/security_interstitials/content/security_interstitial_tab_helper.h"
@@ -164,11 +163,6 @@ PrefService* SecurityInterstitialControl
   return prefs_;
 }
 
-const std::string
-SecurityInterstitialControllerClient::GetExtendedReportingPrefName() const {
-  return prefs::kSafeBrowsingScoutReportingEnabled;
-}
-
 bool SecurityInterstitialControllerClient::CanLaunchDateAndTimeSettings() {
   NOTREACHED();
 }
--- a/components/security_interstitials/content/security_interstitial_controller_client.h
+++ b/components/security_interstitials/content/security_interstitial_controller_client.h
@@ -59,8 +59,6 @@ class SecurityInterstitialControllerClie
   bool CanGoBackBeforeNavigation() override;
 
  protected:
-  // security_interstitials::ControllerClient overrides.
-  const std::string GetExtendedReportingPrefName() const override;
   content::WebContents* web_contents() { return &*web_contents_; }
   content::RenderFrameHost* InterstitialRenderFrameHost() const;
 
--- a/components/security_interstitials/content/security_interstitial_page.cc
+++ b/components/security_interstitials/content/security_interstitial_page.cc
@@ -11,7 +11,6 @@
 #include "base/values.h"
 #include "components/grit/components_resources.h"
 #include "components/prefs/pref_service.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_interstitials/content/security_interstitial_controller_client.h"
 #include "components/security_interstitials/core/common_string_util.h"
 #include "components/security_interstitials/core/metrics_helper.h"
@@ -31,13 +30,6 @@ SecurityInterstitialPage::SecurityInters
       create_view_(true),
       on_show_extended_reporting_pref_value_(false),
       controller_(std::move(controller)) {
-  // Determine if any prefs need to be updated prior to showing the security
-  // interstitial. Note that some content embedders (such as Android WebView)
-  // uses security interstitials without a prefservice.
-  if (controller_->GetPrefService()) {
-    safe_browsing::UpdatePrefsBeforeSecurityInterstitial(
-        controller_->GetPrefService());
-  }
   SetUpMetrics();
 }
 
@@ -91,13 +83,7 @@ SecurityInterstitialControllerClient* Se
 }
 
 void SecurityInterstitialPage::SetUpMetrics() {
-  // Remember the initial state of the extended reporting pref, to be compared
-  // to the same data when the interstitial is closed.
-  PrefService* prefs = controller_->GetPrefService();
-  if (prefs) {
-    on_show_extended_reporting_pref_value_ =
-        safe_browsing::IsExtendedReportingEnabled(*prefs);
-  }
+  on_show_extended_reporting_pref_value_ = false;
 }
 
 std::u16string SecurityInterstitialPage::GetFormattedHostName() const {
--- a/components/security_interstitials/content/ssl_blocking_page.cc
+++ b/components/security_interstitials/content/ssl_blocking_page.cc
@@ -11,7 +11,6 @@
 #include "base/functional/callback_helpers.h"
 #include "base/strings/string_number_conversions.h"
 #include "base/time/time.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/security_interstitials/content/security_interstitial_controller_client.h"
 #include "components/security_interstitials/content/security_interstitial_page.h"
 #include "components/security_interstitials/core/controller_client.h"
--- a/components/security_interstitials/core/controller_client.cc
+++ b/components/security_interstitials/core/controller_client.cc
@@ -36,7 +36,6 @@ MetricsHelper* ControllerClient::metrics
 
 void ControllerClient::SetReportingPreference(bool report) {
   DCHECK(GetPrefService());
-  GetPrefService()->SetBoolean(GetExtendedReportingPrefName(), report);
   metrics_helper_->RecordUserInteraction(
       report ? MetricsHelper::SET_EXTENDED_REPORTING_ENABLED
              : MetricsHelper::SET_EXTENDED_REPORTING_DISABLED);
--- a/components/security_interstitials/core/controller_client.h
+++ b/components/security_interstitials/core/controller_client.h
@@ -130,9 +130,6 @@ class ControllerClient {
 
   void SetBaseHelpCenterUrlForTesting(const GURL& test_url);
 
- protected:
-  virtual const std::string GetExtendedReportingPrefName() const = 0;
-
  private:
   std::unique_ptr<MetricsHelper> metrics_helper_;
   // Link to the help center.
--- a/components/send_tab_to_self/entry_point_display_reason.cc
+++ b/components/send_tab_to_self/entry_point_display_reason.cc
@@ -7,7 +7,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/send_tab_to_self/send_tab_to_self_model.h"
 #include "components/send_tab_to_self/send_tab_to_self_sync_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/sync/service/sync_service.h"
 #include "components/sync/service/sync_user_settings.h"
@@ -19,11 +18,7 @@ namespace {
 
 bool ShouldOfferSignin(syncer::SyncService* sync_service,
                        PrefService* pref_service) {
-  return pref_service->GetBoolean(prefs::kSigninAllowed) &&
-         sync_service->GetAccountInfo().IsEmpty() &&
-         !sync_service->HasDisableReason(
-             syncer::SyncService::DISABLE_REASON_ENTERPRISE_POLICY) &&
-         !sync_service->IsLocalSyncEnabled();
+  return false;
 }
 
 }  // namespace
--- a/components/signin/core/browser/account_investigator.cc
+++ b/components/signin/core/browser/account_investigator.cc
@@ -13,7 +13,6 @@
 #include "components/prefs/pref_registry_simple.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
 #include "components/signin/public/identity_manager/tribool.h"
 #include "google_apis/gaia/gaia_auth_util.h"
@@ -64,28 +63,18 @@ AccountInvestigator::AccountInvestigator
     PrefService* pref_service,
     signin::IdentityManager* identity_manager)
     : pref_service_(pref_service),
-      identity_manager_(identity_manager),
-      timer_(pref_service,
-             prefs::kGaiaCookiePeriodicReportTime,
-             kPeriodicReportingInterval,
-             base::BindRepeating(&AccountInvestigator::TryPeriodicReport,
-                                 base::Unretained(this))) {}
+      identity_manager_(identity_manager) {}
 
 AccountInvestigator::~AccountInvestigator() = default;
 
 // static
 void AccountInvestigator::RegisterPrefs(PrefRegistrySimple* registry) {
-  registry->RegisterStringPref(prefs::kGaiaCookieHash, std::string());
-  registry->RegisterDoublePref(prefs::kGaiaCookieChangedTime, 0);
-  registry->RegisterTimePref(prefs::kGaiaCookiePeriodicReportTime,
-                             base::Time());
 }
 
 void AccountInvestigator::Initialize() {
   identity_manager_->AddObserver(this);
   previously_authenticated_ =
       identity_manager_->HasPrimaryAccount(signin::ConsentLevel::kSignin);
-  timer_.Start();
 }
 
 void AccountInvestigator::Shutdown() {
@@ -111,7 +100,7 @@ void AccountInvestigator::OnAccountsInCo
   // a valid cached ListAccounts response ready for us. Or even both of these
   // could be simultaneously happening, although this should be extremely
   // infrequent.
-  const std::string old_hash(pref_service_->GetString(prefs::kGaiaCookieHash));
+  const std::string old_hash;
   const std::string new_hash(
       HashAccounts(signed_in_accounts, signed_out_accounts));
   const bool currently_authenticated =
@@ -119,9 +108,6 @@ void AccountInvestigator::OnAccountsInCo
   if (old_hash != new_hash) {
     SharedCookieJarReport(signed_in_accounts, signed_out_accounts, Time::Now(),
                           ReportingType::ON_CHANGE);
-    pref_service_->SetString(prefs::kGaiaCookieHash, new_hash);
-    pref_service_->SetDouble(prefs::kGaiaCookieChangedTime,
-                             Time::Now().InSecondsFSinceUnixEpoch());
   } else if (currently_authenticated && !previously_authenticated_) {
     SignedInAccountRelationReport(signed_in_accounts, signed_out_accounts,
                                   ReportingType::ON_CHANGE);
@@ -251,8 +237,7 @@ void AccountInvestigator::SharedCookieJa
     const std::vector<ListedAccount>& signed_out_accounts,
     const Time now,
     const ReportingType type) {
-  const Time last_changed = Time::FromSecondsSinceUnixEpoch(
-      pref_service_->GetDouble(prefs::kGaiaCookieChangedTime));
+  const Time last_changed = Time::Now();
   base::TimeDelta stable_age;
   if (!last_changed.is_null()) {
     stable_age = std::max(now - last_changed, base::TimeDelta());
--- a/components/signin/core/browser/account_investigator.h
+++ b/components/signin/core/browser/account_investigator.h
@@ -119,8 +119,6 @@ class AccountInvestigator : public Keyed
   raw_ptr<PrefService> pref_service_;
   raw_ptr<signin::IdentityManager> identity_manager_;
 
-  // Handles invoking our periodic logic at the right time.
-  signin::PersistentRepeatingTimer timer_;
 
   // If the GaiaCookieManagerService hasn't already cached the cookie data, it
   // will not be able to return enough information for us to always perform
--- a/components/signin/core/browser/account_investigator_unittest.cc
+++ b/components/signin/core/browser/account_investigator_unittest.cc
@@ -13,7 +13,6 @@
 #include "build/build_config.h"
 #include "components/prefs/pref_registry_simple.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_capabilities_test_mutator.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
 #include "components/signin/public/identity_manager/identity_test_environment.h"
--- a/components/signin/core/browser/account_reconcilor.cc
+++ b/components/signin/core/browser/account_reconcilor.cc
@@ -31,7 +31,6 @@
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_client.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/accounts_cookie_mutator.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
@@ -96,12 +95,6 @@ bool IsAnyAccountInErrorState(
 // consider the migration done.
 void MaybeMigrateClearOnExit(SigninClient& client,
                              signin::IdentityManager& identity_manager) {
-  PrefService& prefs = *client.GetPrefs();
-  if (!client.AreSigninCookiesDeletedOnExit() &&
-      signin::AreGoogleCookiesRebuiltAfterClearingWhenSignedIn(identity_manager,
-                                                               prefs)) {
-    prefs.SetBoolean(prefs::kCookieClearOnExitMigrationNoticeComplete, true);
-  }
 }
 #endif  // BUILDFLAG(ENABLE_DICE_SUPPORT)
 
@@ -177,10 +170,6 @@ AccountReconcilor::~AccountReconcilor()
 
 // static
 void AccountReconcilor::RegisterProfilePrefs(PrefRegistrySimple* registry) {
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-  registry->RegisterBooleanPref(
-      prefs::kCookieClearOnExitMigrationNoticeComplete, false);
-#endif
 }
 
 void AccountReconcilor::RegisterWithAllDependencies() {
@@ -288,12 +277,6 @@ void AccountReconcilor::RegisterWithIden
   }
 
   identity_manager_observer_.Observe(identity_manager_);
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-  pref_observer_.Add(
-      prefs::kExplicitBrowserSignin,
-      base::BindRepeating(&MaybeMigrateClearOnExit, std::ref(*client_),
-                          std::ref(*identity_manager_)));
-#endif
   registered_with_identity_manager_ = true;
 }
 
--- a/components/signin/core/browser/account_reconcilor_unittest.cc
+++ b/components/signin/core/browser/account_reconcilor_unittest.cc
@@ -36,7 +36,6 @@
 #include "components/signin/public/base/list_accounts_test_utils.h"
 #include "components/signin/public/base/signin_buildflags.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/base/test_signin_client.h"
 #include "components/signin/public/identity_manager/account_capabilities_test_mutator.h"
--- a/components/signin/core/browser/dice_account_reconcilor_delegate.cc
+++ b/components/signin/core/browser/dice_account_reconcilor_delegate.cc
@@ -15,7 +15,6 @@
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_client.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
 #include "components/signin/public/identity_manager/accounts_mutator.h"
--- a/components/signin/core/browser/signin_metrics_service.cc
+++ b/components/signin/core/browser/signin_metrics_service.cc
@@ -19,7 +19,6 @@
 #include "components/signin/core/browser/active_primary_accounts_metrics_recorder.h"
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
@@ -485,23 +484,6 @@ void SigninMetricsService::OnRefreshToke
 #if BUILDFLAG(ENABLE_DICE_SUPPORT)
 
 void SigninMetricsService::RecordExplicitSigninMigrationStatus() {
-  ExplicitSigninMigration explicit_signin_migration =
-      ExplicitSigninMigration::kMigratedSignedOut;
-  const bool explicit_signin_pref =
-      pref_service_->GetBoolean(prefs::kExplicitBrowserSignin);
-  if (identity_manager_->HasPrimaryAccount(signin::ConsentLevel::kSync)) {
-    explicit_signin_migration =
-        explicit_signin_pref ? ExplicitSigninMigration::kMigratedSyncing
-                             : ExplicitSigninMigration::kNotMigratedSyncing;
-  } else if (identity_manager_->HasPrimaryAccount(
-                 signin::ConsentLevel::kSignin)) {
-    explicit_signin_migration =
-        explicit_signin_pref ? ExplicitSigninMigration::kMigratedSignedIn
-                             : ExplicitSigninMigration::kNotMigratedSignedIn;
-  }
-
-  base::UmaHistogramEnumeration(kExplicitSigninMigrationHistogramName,
-                                explicit_signin_migration);
 }
 
 void SigninMetricsService::MaybeRecordWebSigninToChromeSigninMetrics(
--- a/components/signin/internal/identity_manager/account_tracker_service.cc
+++ b/components/signin/internal/identity_manager/account_tracker_service.cc
@@ -37,7 +37,6 @@
 #include "components/signin/internal/identity_manager/account_capabilities_constants.h"
 #include "components/signin/internal/identity_manager/account_info_util.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/account_capabilities.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/identity_manager/tribool.h"
@@ -194,11 +193,6 @@ AccountTrackerService::~AccountTrackerSe
 
 // static
 void AccountTrackerService::RegisterPrefs(PrefRegistrySimple* registry) {
-  registry->RegisterListPref(prefs::kAccountInfo);
-#if BUILDFLAG(IS_CHROMEOS)
-  registry->RegisterIntegerPref(prefs::kAccountIdMigrationState,
-                                AccountTrackerService::MIGRATION_NOT_STARTED);
-#endif
 }
 
 void AccountTrackerService::Initialize(PrefService* pref_service,
@@ -552,14 +546,12 @@ AccountTrackerService::ComputeNewMigrati
 void AccountTrackerService::SetMigrationState(AccountIdMigrationState state) {
   DCHECK(state != MIGRATION_DONE || AreAllAccountsMigrated())
       << "state: " << state << ", accounts = " << AccountsToString(accounts_);
-  pref_service_->SetInteger(prefs::kAccountIdMigrationState, state);
 }
 
 // static
 AccountTrackerService::AccountIdMigrationState
 AccountTrackerService::GetMigrationState(const PrefService* pref_service) {
-  return static_cast<AccountTrackerService::AccountIdMigrationState>(
-      pref_service->GetInteger(prefs::kAccountIdMigrationState));
+  return MIGRATION_NOT_STARTED;
 }
 #endif  // BUILDFLAG(IS_CHROMEOS)
 
@@ -619,27 +611,6 @@ void AccountTrackerService::OnAccountIma
     const CoreAccountId& account_id,
     const std::string& image_url_with_size,
     bool success) {
-  if (!success || !pref_service_) {
-    return;
-  }
-
-  base::Value::Dict* dict = nullptr;
-  ScopedListPrefUpdate update(pref_service_, prefs::kAccountInfo);
-  for (base::Value& value : *update) {
-    base::Value::Dict* maybe_dict = value.GetIfDict();
-    if (maybe_dict) {
-      const std::string* account_key = maybe_dict->FindString(kAccountKeyKey);
-      if (account_key && *account_key == account_id.ToString()) {
-        dict = maybe_dict;
-        break;
-      }
-    }
-  }
-
-  if (!dict) {
-    return;
-  }
-  dict->Set(kLastDownloadedImageURLWithSizeKey, image_url_with_size);
 }
 
 void AccountTrackerService::RemoveAccountImageFromDisk(
@@ -652,176 +623,9 @@ void AccountTrackerService::RemoveAccoun
 }
 
 void AccountTrackerService::LoadFromPrefs() {
-  const base::Value::List& list = pref_service_->GetList(prefs::kAccountInfo);
-  std::set<CoreAccountId> to_remove;
-  for (size_t i = 0; i < list.size(); ++i) {
-    const base::Value::Dict* dict = list[i].GetIfDict();
-    if (!dict) {
-      continue;
-    }
-
-    const std::string* account_key = dict->FindString(kAccountKeyKey);
-    if (!account_key) {
-      continue;
-    }
-
-    // Ignore empty account ids.
-    if (account_key->empty()) {
-      to_remove.insert(CoreAccountId());
-      continue;
-    }
-    // Ignore incorrectly persisted non-canonical account ids.
-    if (account_key->find('@') != std::string::npos &&
-        *account_key != gaia::CanonicalizeEmail(*account_key)) {
-      to_remove.insert(CoreAccountId::FromString(*account_key));
-      continue;
-    }
-
-    CoreAccountId account_id = CoreAccountId::FromString(*account_key);
-    StartTrackingAccount(account_id);
-    AccountInfo& account_info = accounts_[account_id];
-
-    std::string gaia_id_string;
-    GetString(*dict, kAccountGaiaKey, gaia_id_string);
-    account_info.gaia = GaiaId(gaia_id_string);
-
-    GetString(*dict, kAccountEmailKey, account_info.email);
-    GetString(*dict, kAccountHostedDomainKey, account_info.hosted_domain);
-    GetString(*dict, kAccountFullNameKey, account_info.full_name);
-    GetString(*dict, kAccountGivenNameKey, account_info.given_name);
-    GetString(*dict, kAccountLocaleKey, account_info.locale);
-    GetString(*dict, kAccountPictureURLKey, account_info.picture_url);
-    GetString(*dict, kLastDownloadedImageURLWithSizeKey,
-              account_info.last_downloaded_image_url_with_size);
-
-    account_info.is_child_account =
-        ParseTribool(dict->FindInt(kAccountChildAttributeKey));
-
-    std::optional<bool> is_under_advanced_protection =
-        dict->FindBool(kAdvancedProtectionAccountStatusKey);
-    if (is_under_advanced_protection.has_value()) {
-      account_info.is_under_advanced_protection =
-          is_under_advanced_protection.value();
-    }
-
-    std::optional<int> access_point = dict->FindInt(kAccountAccessPoint);
-    if (access_point.has_value()) {
-      account_info.access_point =
-          static_cast<signin_metrics::AccessPoint>(access_point.value());
-    }
-
-    if (std::optional<int> deprecated_can_offer_extended_chrome_sync_promos =
-            dict->FindIntByDottedPath(
-                kDeprecatedCanOfferExtendedChromeSyncPromosPrefPath)) {
-      // Migrate to Capability names based pref paths.
-      ScopedListPrefUpdate update(pref_service_, prefs::kAccountInfo);
-      base::Value::Dict& update_dict = (*update)[i].GetDict();
-      SetAccountCapabilityState(
-          update_dict,
-          kCanShowHistorySyncOptInsWithoutMinorModeRestrictionsCapabilityName,
-          ParseTribool(deprecated_can_offer_extended_chrome_sync_promos));
-      update_dict.RemoveByDottedPath(
-          kDeprecatedCanOfferExtendedChromeSyncPromosPrefPath);
-    }
-
-    for (std::string_view name :
-         AccountCapabilities::GetSupportedAccountCapabilityNames()) {
-      switch (FindAccountCapabilityState(*dict, name)) {
-        case signin::Tribool::kUnknown:
-          account_info.capabilities.capabilities_map_.erase(name);
-          break;
-        case signin::Tribool::kTrue:
-          account_info.capabilities.capabilities_map_[std::string(name)] = true;
-          break;
-        case signin::Tribool::kFalse:
-          account_info.capabilities.capabilities_map_[std::string(name)] =
-              false;
-          break;
-      }
-    }
-
-    if (!account_info.gaia.empty()) {
-      NotifyAccountUpdated(account_info);
-    }
-  }
-
-  // Remove any obsolete prefs.
-  for (auto account_id : to_remove) {
-    AccountInfo account_info;
-    account_info.account_id = account_id;
-    RemoveFromPrefs(account_info);
-    RemoveAccountImageFromDisk(account_id);
-  }
-
-#if BUILDFLAG(IS_CHROMEOS)
-  if (GetMigrationState() != MIGRATION_DONE) {
-    const AccountIdMigrationState new_state = ComputeNewMigrationState();
-    SetMigrationState(new_state);
-
-    if (new_state == MIGRATION_IN_PROGRESS) {
-      MigrateToGaiaId();
-    }
-  }
-  DCHECK(GetMigrationState() != MIGRATION_DONE || AreAllAccountsMigrated())
-      << "state: " << (int)GetMigrationState()
-      << ", accounts = " << AccountsToString(accounts_);
-
-  UMA_HISTOGRAM_ENUMERATION("Signin.AccountTracker.GaiaIdMigrationState",
-                            GetMigrationState(), NUM_MIGRATION_STATES);
-#else
-  DCHECK(AreAllAccountsMigrated())
-      << "accounts = " << AccountsToString(accounts_);
-#endif  // BUILDFLAG(IS_CHROMEOS)
-
-  UMA_HISTOGRAM_COUNTS_100("Signin.AccountTracker.CountOfLoadedAccounts",
-                           accounts_.size());
 }
 
 void AccountTrackerService::SaveToPrefs(const AccountInfo& account_info) {
-  if (!pref_service_) {
-    return;
-  }
-
-  base::Value::Dict* dict = nullptr;
-  ScopedListPrefUpdate update(pref_service_, prefs::kAccountInfo);
-  for (base::Value& value : *update) {
-    base::Value::Dict* maybe_dict = value.GetIfDict();
-    if (maybe_dict) {
-      const std::string* account_key = maybe_dict->FindString(kAccountKeyKey);
-      if (account_key && *account_key == account_info.account_id.ToString()) {
-        dict = maybe_dict;
-        break;
-      }
-    }
-  }
-
-  if (!dict) {
-    update->Append(base::Value::Dict());
-    dict = &update->back().GetDict();
-    dict->Set(kAccountKeyKey, account_info.account_id.ToString());
-  }
-
-  dict->Set(kAccountEmailKey, account_info.email);
-  dict->Set(kAccountGaiaKey, account_info.gaia.ToString());
-  dict->Set(kAccountHostedDomainKey, account_info.hosted_domain);
-  dict->Set(kAccountFullNameKey, account_info.full_name);
-  dict->Set(kAccountGivenNameKey, account_info.given_name);
-  dict->Set(kAccountLocaleKey, account_info.locale);
-  dict->Set(kAccountPictureURLKey, account_info.picture_url);
-  dict->Set(kAccountChildAttributeKey,
-            static_cast<int>(account_info.is_child_account));
-  dict->Set(kAdvancedProtectionAccountStatusKey,
-            account_info.is_under_advanced_protection);
-  dict->Set(kAccountAccessPoint, static_cast<int>(account_info.access_point));
-  // |kLastDownloadedImageURLWithSizeKey| should only be set after the GAIA
-  // picture is successufly saved to disk. Otherwise, there is no guarantee that
-  // |kLastDownloadedImageURLWithSizeKey| matches the picture on disk.
-  for (std::string_view name :
-       AccountCapabilities::GetSupportedAccountCapabilityNames()) {
-    signin::Tribool capability_state =
-        account_info.capabilities.GetCapabilityByName(name);
-    SetAccountCapabilityState(*dict, name, capability_state);
-  }
 }
 
 void AccountTrackerService::RemoveFromPrefs(const AccountInfo& account_info) {
@@ -829,15 +633,7 @@ void AccountTrackerService::RemoveFromPr
     return;
   }
 
-  ScopedListPrefUpdate update(pref_service_, prefs::kAccountInfo);
   const std::string account_id = account_info.account_id.ToString();
-  update->EraseIf([&account_id](const base::Value& value) {
-    if (!value.is_dict()) {
-      return false;
-    }
-    const std::string* account_key = value.GetDict().FindString(kAccountKeyKey);
-    return account_key && *account_key == account_id;
-  });
 }
 
 CoreAccountId AccountTrackerService::PickAccountIdForAccount(
--- a/components/signin/internal/identity_manager/account_tracker_service_unittest.cc
+++ b/components/signin/internal/identity_manager/account_tracker_service_unittest.cc
@@ -29,7 +29,6 @@
 #include "components/signin/internal/identity_manager/fake_profile_oauth2_token_service.h"
 #include "components/signin/public/base/avatar_icon_util.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/test_signin_client.h"
 #include "components/signin/public/identity_manager/account_capabilities.h"
 #include "components/signin/public/identity_manager/account_capabilities_test_mutator.h"
--- a/components/signin/internal/identity_manager/gaia_cookie_manager_service.cc
+++ b/components/signin/internal/identity_manager/gaia_cookie_manager_service.cc
@@ -32,7 +32,6 @@
 #include "components/signin/internal/identity_manager/oauth_multilogin_helper.h"
 #include "components/signin/public/base/signin_buildflags.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
 #include "components/signin/public/identity_manager/set_accounts_in_cookie_result.h"
 #include "google_apis/credentials_mode.h"
@@ -434,28 +433,7 @@ GaiaCookieManagerService::GaiaCookieMana
       listAccountsUnexpectedServerResponseRetried_(false),
       external_cc_result_fetched_(false),
       list_accounts_stale_(true) {
-  std::string gaia_cookie_last_list_accounts_data =
-      signin_client_->GetPrefs()->GetString(
-          prefs::kGaiaCookieLastListAccountsData);
-  std::string gaia_cookie_last_list_accounts_binary_data =
-      signin_client_->GetPrefs()->GetString(
-          prefs::kGaiaCookieLastListAccountsBinaryData);
-
-  // Parse ListAccounts data from prefs. In case both jspb and protobuf encoded
-  // data are present prefer the jspb one.
-  bool parse_success = false;
-  if (!gaia_cookie_last_list_accounts_data.empty()) {
-    parse_success = gaia::ParseListAccountsData(
-        gaia_cookie_last_list_accounts_data, &accounts_);
-  } else if (!gaia_cookie_last_list_accounts_binary_data.empty()) {
-    parse_success = gaia::ParseBinaryListAccountsData(
-        gaia_cookie_last_list_accounts_binary_data, &accounts_);
-  }
-  if (!parse_success) {
     accounts_.clear();
-    return;
-  }
-  InitializeListedAccountsIds();
 }
 
 GaiaCookieManagerService::~GaiaCookieManagerService() {
@@ -466,10 +444,6 @@ GaiaCookieManagerService::~GaiaCookieMan
 
 // static
 void GaiaCookieManagerService::RegisterPrefs(PrefRegistrySimple* registry) {
-  registry->RegisterStringPref(prefs::kGaiaCookieLastListAccountsData,
-                               std::string());
-  registry->RegisterStringPref(prefs::kGaiaCookieLastListAccountsBinaryData,
-                               std::string());
 }
 
 void GaiaCookieManagerService::InitCookieListener() {
@@ -697,10 +671,6 @@ void GaiaCookieManagerService::OnListAcc
                            : gaia::ParseListAccountsData(data, &accounts_);
   if (!parse_success) {
     accounts_.clear();
-    signin_client_->GetPrefs()->ClearPref(
-        prefs::kGaiaCookieLastListAccountsData);
-    signin_client_->GetPrefs()->ClearPref(
-        prefs::kGaiaCookieLastListAccountsBinaryData);
     GoogleServiceAuthError error =
         GoogleServiceAuthError::FromUnexpectedServiceResponse(
             "Error parsing ListAccounts response");
@@ -708,18 +678,6 @@ void GaiaCookieManagerService::OnListAcc
     return;
   }
 
-  if (base::FeatureList::IsEnabled(
-          gaia::features::kListAccountsUsesBinaryFormat)) {
-    signin_client_->GetPrefs()->SetString(
-        prefs::kGaiaCookieLastListAccountsBinaryData, data);
-    signin_client_->GetPrefs()->ClearPref(
-        prefs::kGaiaCookieLastListAccountsData);
-  } else {
-    signin_client_->GetPrefs()->SetString(
-        prefs::kGaiaCookieLastListAccountsData, data);
-    signin_client_->GetPrefs()->ClearPref(
-        prefs::kGaiaCookieLastListAccountsBinaryData);
-  }
   RecordListAccountsFailure(GoogleServiceAuthError::NONE);
 
   InitializeListedAccountsIds();
--- a/components/signin/internal/identity_manager/gaia_cookie_manager_service_unittest.cc
+++ b/components/signin/internal/identity_manager/gaia_cookie_manager_service_unittest.cc
@@ -25,7 +25,6 @@
 #include "components/prefs/testing_pref_service.h"
 #include "components/signin/internal/identity_manager/account_tracker_service.h"
 #include "components/signin/internal/identity_manager/fake_profile_oauth2_token_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/test_signin_client.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
 #include "components/signin/public/identity_manager/identity_test_utils.h"
--- a/components/signin/internal/identity_manager/mutable_profile_oauth2_token_service_delegate.cc
+++ b/components/signin/internal/identity_manager/mutable_profile_oauth2_token_service_delegate.cc
@@ -32,7 +32,6 @@
 #include "components/signin/public/base/hybrid_encryption_key.h"
 #include "components/signin/public/base/signin_client.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
 #include "components/signin/public/webdata/token_service_table.h"
--- a/components/signin/internal/identity_manager/mutable_profile_oauth2_token_service_delegate_unittest.cc
+++ b/components/signin/internal/identity_manager/mutable_profile_oauth2_token_service_delegate_unittest.cc
@@ -38,7 +38,6 @@
 #include "components/signin/public/base/device_id_helper.h"
 #include "components/signin/public/base/hybrid_encryption_key.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/base/test_signin_client.h"
 #include "components/signin/public/identity_manager/account_info.h"
--- a/components/signin/internal/identity_manager/primary_account_manager.cc
+++ b/components/signin/internal/identity_manager/primary_account_manager.cc
@@ -25,7 +25,6 @@
 #include "components/signin/public/base/account_consistency_method.h"
 #include "components/signin/public/base/signin_client.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/account_info.h"
@@ -208,176 +207,25 @@ PrimaryAccountManager::PrimaryAccountMan
   DCHECK(account_tracker_service_);
   ScopedPrefCommit scoped_pref_commit(client_->GetPrefs(),
                                       /*commit_on_destroy=*/false);
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-  signin_allowed_.Init(
-      prefs::kSigninAllowed, client_->GetPrefs(),
-      base::BindRepeating(&PrimaryAccountManager::OnSigninAllowedPrefChanged,
-                          base::Unretained(this)));
-#else
-  scoped_pref_commit.ClearPref(prefs::kExplicitBrowserSignin);
-#endif
-
-  // Prepare prefs before loading them.
-  PrepareToLoadPrefs();
-
-  PrefService* prefs = client_->GetPrefs();
-  std::string pref_account_id =
-      prefs->GetString(prefs::kGoogleServicesAccountId);
-  bool pref_consented_to_sync =
-      prefs->GetBoolean(prefs::kGoogleServicesConsentedToSync);
-  LogPrimaryAccountPrefsOnInitialize(pref_account_id, pref_consented_to_sync);
-
-  if (pref_account_id.empty()) {
     SetPrimaryAccountInternal(CoreAccountInfo(), /*consented_to_sync=*/false,
                               scoped_pref_commit);
-  } else {
-    auto [account_info, account_info_state] =
-        GetOrRestorePrimaryAccountInfoOnInitialize(pref_account_id,
-                                                   pref_consented_to_sync);
-    base::UmaHistogramEnumeration(
-        "Signin.PAMInitialize.PrimaryAccountInfoState", account_info_state);
-
-    if (pref_consented_to_sync && !account_info.IsEmpty()) {
-      SetPrimaryAccountInternal(account_info, /*consented_to_sync=*/true,
-                                scoped_pref_commit);
-
-      // Ensure that the last syncing account data is consistent with the
-      // primary account. The last signed-in account data is written inside
-      // SetPrimaryAccountInternal().
-      scoped_pref_commit.SetString(prefs::kGoogleServicesLastSyncingGaiaId,
-                                   account_info.gaia.ToString());
-      scoped_pref_commit.SetString(prefs::kGoogleServicesLastSyncingUsername,
-                                   account_info.email);
-    } else if (ShouldSigninAllowedPrefAffectPrimaryAccount(
-                   pref_consented_to_sync)) {
-      SetPrimaryAccountInternal(CoreAccountInfo(), /*consented_to_sync=*/false,
-                                scoped_pref_commit);
-    } else {
-      SetPrimaryAccountInternal(account_info, /*consented_to_sync=*/false,
-                                scoped_pref_commit);
-    }
-  }
 
   // PrimaryAccountManager is initialized once the primary account and consent
   // level are loaded.
   CHECK(primary_account_.has_value());
-
-  bool migrated_sync_user_to_explicit_sign_in = false;
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-  if (!prefs->GetBoolean(prefs::kExplicitBrowserSignin) &&
-      HasPrimaryAccount(signin::ConsentLevel::kSync)) {
-    // A profile that is opted in to sync can be migrated to explicit browser
-    // sign-in as the user has explicitly signed in to the browser when they
-    // opted in to sync.
-    scoped_pref_commit.SetBoolean(prefs::kExplicitBrowserSignin, true);
-    migrated_sync_user_to_explicit_sign_in = true;
-  }
-#endif
-
-  base::UmaHistogramBoolean("Signin.ExplicitSigninMigration.FromSync",
-                            migrated_sync_user_to_explicit_sign_in);
-
-  // `prefs::kPrefsThemesSearchEnginesAccountStorageEnabled` is set for sync
-  // users and new signed in users. It is not cleared on sign out.
-  if (base::FeatureList::IsEnabled(
-          switches::kEnablePreferencesAccountStorage)) {
-    if (HasPrimaryAccount(signin::ConsentLevel::kSync)) {
-      scoped_pref_commit.SetBoolean(
-          prefs::kPrefsThemesSearchEnginesAccountStorageEnabled, true);
-    }
-  } else {
-    scoped_pref_commit.ClearPref(
-        prefs::kPrefsThemesSearchEnginesAccountStorageEnabled);
-  }
-
-  std::vector<AccountInfo> accounts_in_tracker_service =
-      account_tracker_service_->GetAccounts();
-  SigninPrefs signin_prefs(*prefs);
-
-  for (const auto& account : accounts_in_tracker_service) {
-    // Clear the extensions explicit sign in pref if the feature flag is not
-    // enabled.
-    if (!switches::IsExtensionsExplicitBrowserSigninEnabled()) {
-      signin_prefs.SetExtensionsExplicitBrowserSignin(account.gaia, false);
-    }
-    // Clear the bookmarks explicit sign in pref if the feature flag is not
-    // enabled.
-    if (!base::FeatureList::IsEnabled(
-            switches::kSyncEnableBookmarksInTransportMode)) {
-      signin_prefs.SetBookmarksExplicitBrowserSignin(account.gaia, false);
-    }
-  }
-
-  // It is important to only load credentials after starting to observe the
-  // token service.
-  token_service_observation_.Observe(token_service_);
-  token_service_->LoadCredentials(
-      GetPrimaryAccountId(signin::ConsentLevel::kSignin));
 }
 
 PrimaryAccountManager::~PrimaryAccountManager() = default;
 
 // static
 void PrimaryAccountManager::RegisterProfilePrefs(PrefRegistrySimple* registry) {
-  registry->RegisterStringPref(prefs::kGoogleServicesLastSyncingGaiaId,
-                               std::string());
-  registry->RegisterStringPref(prefs::kGoogleServicesLastSyncingUsername,
-                               std::string());
-  registry->RegisterStringPref(prefs::kGoogleServicesLastSignedInUsername,
-                               std::string());
-  registry->RegisterStringPref(prefs::kGoogleServicesAccountId, std::string());
-  registry->RegisterBooleanPref(prefs::kGoogleServicesConsentedToSync, false);
-  registry->RegisterStringPref(
-      prefs::kGoogleServicesSyncingGaiaIdMigratedToSignedIn, std::string());
-  registry->RegisterStringPref(
-      prefs::kGoogleServicesSyncingUsernameMigratedToSignedIn, std::string());
-  registry->RegisterBooleanPref(prefs::kSigninAllowed, true);
-  registry->RegisterBooleanPref(prefs::kSignedInWithCredentialProvider, false);
-  registry->RegisterBooleanPref(kExplicitBrowserSigninWithoutFeatureEnabled,
-                                false);
-  registry->RegisterBooleanPref(prefs::kExplicitBrowserSignin, false);
-  registry->RegisterBooleanPref(
-      prefs::kPrefsThemesSearchEnginesAccountStorageEnabled, false);
 }
 
 // static
 void PrimaryAccountManager::RegisterPrefs(PrefRegistrySimple* registry) {
-  registry->RegisterStringPref(prefs::kGoogleServicesUsernamePattern,
-                               std::string());
 }
 
 void PrimaryAccountManager::PrepareToLoadPrefs() {
-  // Check this method is only called before loading the primary account.
-  CHECK(!primary_account_.has_value());
-
-  PrefService* prefs = client_->GetPrefs();
-
-  // If the user is clearing the token service from the command line, then
-  // clear their login info also (not valid to be logged in without any
-  // tokens).
-  base::CommandLine* cmd_line = base::CommandLine::ForCurrentProcess();
-  if (cmd_line->HasSwitch(switches::kClearTokenService)) {
-    prefs->SetString(prefs::kGoogleServicesAccountId, "");
-    prefs->SetBoolean(prefs::kGoogleServicesConsentedToSync, false);
-  }
-
-#if BUILDFLAG(IS_CHROMEOS)
-  // Migrate primary account ID from email to Gaia ID if needed.
-  std::string pref_account_id =
-      prefs->GetString(prefs::kGoogleServicesAccountId);
-  if (!pref_account_id.empty()) {
-    if (account_tracker_service_->GetMigrationState() ==
-        AccountTrackerService::MIGRATION_IN_PROGRESS) {
-      CoreAccountInfo account_info =
-          account_tracker_service_->FindAccountInfoByEmail(pref_account_id);
-      // |account_info.gaia| could be empty if |account_id| is already gaia id.
-      if (!account_info.gaia.empty()) {
-        pref_account_id = account_info.gaia.ToString();
-        prefs->SetString(prefs::kGoogleServicesAccountId, pref_account_id);
-      }
-    }
-  }
-#endif
 }
 
 std::pair<CoreAccountInfo, PrimaryAccountManager::InitializeAccountInfoState>
@@ -405,46 +253,10 @@ PrimaryAccountManager::GetOrRestorePrima
                               kEmptyAccountInfo_RestoreFailedNotSyncConsented);
   }
 
-  PrefService* prefs = client_->GetPrefs();
-  const GaiaId last_syncing_gaia_id =
-      GaiaId(prefs->GetString(prefs::kGoogleServicesLastSyncingGaiaId));
-  if (last_syncing_gaia_id.empty()) {
-    return std::make_pair(CoreAccountInfo(),
-                          InitializeAccountInfoState::
-                              kEmptyAccountInfo_RestoreFailedNoLastSyncGaiaId);
-  }
-  std::string last_syncing_email =
-      prefs->GetString(prefs::kGoogleServicesLastSyncingUsername);
-  if (last_syncing_email.empty()) {
-    return std::make_pair(CoreAccountInfo(),
-                          InitializeAccountInfoState::
-                              kEmptyAccountInfo_RestoreFailedNoLastSyncEmail);
-  }
-
-  if (account_id != account_tracker_service_->PickAccountIdForAccount(
-                        last_syncing_gaia_id, last_syncing_email)) {
-    return std::make_pair(
-        CoreAccountInfo(),
-        InitializeAccountInfoState::
-            kEmptyAccountInfo_RestoreFailedAccountIdDontMatch);
-  }
-
-  if (base::FeatureList::IsEnabled(kRestorePrimaryAccountInfo)) {
-    CHECK_EQ(
-        account_id,
-        account_tracker_service_->SeedAccountInfo(
-            last_syncing_gaia_id, last_syncing_email,
-            signin_metrics::AccessPoint::kRestorePrimaryAccountOnProfileLoad));
-
-    return std::make_pair(account_tracker_service_->GetAccountInfo(account_id),
-                          InitializeAccountInfoState::
-                              kEmptyAccountInfo_RestoreSuccessFromLastSyncInfo);
-  } else {
     return std::make_pair(
         CoreAccountInfo(),
         InitializeAccountInfoState::
             kEmptyAccountInfo_RestoreFailedAsRestoreFeatureIsDisabled);
-  }
 }
 
 const PrimaryAccountManager::PrimaryAccount&
@@ -535,10 +347,9 @@ void PrimaryAccountManager::SetSyncPrima
 
 #if DCHECK_IS_ON()
   {
-    std::string pref_account_id =
-        client_->GetPrefs()->GetString(prefs::kGoogleServicesAccountId);
+    std::string pref_account_id;
     bool consented_to_sync =
-        client_->GetPrefs()->GetBoolean(prefs::kGoogleServicesConsentedToSync);
+        false;
 
     DCHECK(pref_account_id.empty() || !consented_to_sync ||
            pref_account_id == account_info.account_id.ToString())
@@ -549,14 +360,6 @@ void PrimaryAccountManager::SetSyncPrima
 
   SetPrimaryAccountInternal(account_info, /*consented_to_sync=*/true,
                             scoped_pref_commit);
-
-  // Go ahead and update the last signed in account info here as well. Once a
-  // user is signed in the corresponding preferences should match. Doing it here
-  // as opposed to on signin allows us to catch the upgrade scenario.
-  scoped_pref_commit.SetString(prefs::kGoogleServicesLastSyncingGaiaId,
-                               account_info.gaia.ToString());
-  scoped_pref_commit.SetString(prefs::kGoogleServicesLastSyncingUsername,
-                               account_info.email);
 }
 
 void PrimaryAccountManager::SetPrimaryAccountInternal(
@@ -568,22 +371,6 @@ void PrimaryAccountManager::SetPrimaryAc
   // 'account_info' might be a reference to the contents of `primary_account_`.
   // Create a PrimaryAccount object before calling emplace to avoid crashes.
   primary_account_.emplace(PrimaryAccount(account_info, consented_to_sync));
-  std::string account_id =
-      GetPrimaryAccount().account_info.account_id.ToString();
-  scoped_pref_commit.SetString(prefs::kGoogleServicesAccountId, account_id);
-  scoped_pref_commit.SetBoolean(prefs::kGoogleServicesConsentedToSync,
-                                GetPrimaryAccount().consented_to_sync);
-  // If this was a sign-out (account ID is empty), also clear the "account was
-  // migrated" prefs.
-  if (account_id.empty()) {
-    scoped_pref_commit.ClearPref(
-        prefs::kGoogleServicesSyncingGaiaIdMigratedToSignedIn);
-    scoped_pref_commit.ClearPref(
-        prefs::kGoogleServicesSyncingUsernameMigratedToSignedIn);
-  } else {
-    scoped_pref_commit.SetString(prefs::kGoogleServicesLastSignedInUsername,
-                                 account_info.email);
-  }
 }
 
 void PrimaryAccountManager::UpdatePrimaryAccountInfo() {
@@ -721,108 +508,6 @@ PrimaryAccountChangeEvent::State Primary
 void PrimaryAccountManager::ComputeExplicitBrowserSignin(
     const PrimaryAccountChangeEvent& event_details,
     ScopedPrefCommit& scoped_pref_commit) {
-  switch (event_details.GetEventTypeFor(signin::ConsentLevel::kSignin)) {
-    case PrimaryAccountChangeEvent::Type::kNone:
-      break;
-    case PrimaryAccountChangeEvent::Type::kCleared:
-      scoped_pref_commit.ClearPref(kExplicitBrowserSigninWithoutFeatureEnabled);
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-      scoped_pref_commit.ClearPref(prefs::kExplicitBrowserSignin);
-#endif
-      return;
-    case PrimaryAccountChangeEvent::Type::kSet:
-      CHECK(event_details.GetSetPrimaryAccountAccessPoint().has_value());
-      signin_metrics::AccessPoint access_point =
-          event_details.GetSetPrimaryAccountAccessPoint().value();
-      GaiaId current_gaia_id =
-          event_details.GetCurrentState().primary_account.gaia;
-
-      bool is_implicit_signin =
-          access_point == signin_metrics::AccessPoint::kUnknown ||
-          access_point == signin_metrics::AccessPoint::kWebSignin;
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-      if (base::FeatureList::IsEnabled(
-              switches::kWebSigninLeadsToImplicitlySignedInState)) {
-        // To allow easier testing, consider the following access points as
-        // implicit sign-in.
-        is_implicit_signin =
-            is_implicit_signin ||
-            access_point ==
-                signin_metrics::AccessPoint::kChromeSigninInterceptBubble ||
-            access_point ==
-                signin_metrics::AccessPoint::kSigninChoiceRemembered;
-      }
-#endif  // BUILDFLAG(ENABLE_DICE_SUPPORT)
-      if (is_implicit_signin) {
-        scoped_pref_commit.ClearPref(
-            kExplicitBrowserSigninWithoutFeatureEnabled);
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-        scoped_pref_commit.ClearPref(prefs::kExplicitBrowserSignin);
-#endif
-        // Reset explicit sign-in prefs for the relevant data types.
-        scoped_pref_commit.SetBoolean(
-            prefs::kPrefsThemesSearchEnginesAccountStorageEnabled, false);
-        SigninPrefs(*client_->GetPrefs())
-            .SetExtensionsExplicitBrowserSignin(current_gaia_id, false);
-        SigninPrefs(*client_->GetPrefs())
-            .SetBookmarksExplicitBrowserSignin(current_gaia_id, false);
-        break;
-      }
-      // All others access points are explicit sign ins except the Web
-      // Signin event.
-      scoped_pref_commit.SetBoolean(kExplicitBrowserSigninWithoutFeatureEnabled,
-                                    true);
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-      scoped_pref_commit.SetBoolean(prefs::kExplicitBrowserSignin, true);
-#endif
-      if (base::FeatureList::IsEnabled(
-              switches::kEnablePreferencesAccountStorage)) {
-        scoped_pref_commit.SetBoolean(
-            prefs::kPrefsThemesSearchEnginesAccountStorageEnabled, true);
-      }
-      if (access_point ==
-              signin_metrics::AccessPoint::kExtensionInstallBubble &&
-          switches::IsExtensionsExplicitBrowserSigninEnabled()) {
-        // Record an opt in for the extensions explicit signin feature and use
-        // the existing pref to determine if it's a new or existing opt in.
-        bool is_new_opt_in =
-            !SigninPrefs(*client_->GetPrefs())
-                 .GetExtensionsExplicitBrowserSignin(current_gaia_id);
-        base::UmaHistogramBoolean(
-            "Signin.Extensions.ExplicitSigninFromExtensionInstallBubble",
-            is_new_opt_in);
-
-        SigninPrefs(*client_->GetPrefs())
-            .SetExtensionsExplicitBrowserSignin(current_gaia_id, true);
-      }
-      if (access_point == signin_metrics::AccessPoint::kBookmarkBubble &&
-          base::FeatureList::IsEnabled(
-              switches::kSyncEnableBookmarksInTransportMode)) {
-        // Record an explicit signin for bookmarks for this account only.
-        SigninPrefs(*client_->GetPrefs())
-            .SetBookmarksExplicitBrowserSignin(current_gaia_id, true);
-      }
-  }
-
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-  // If the user turns on sync, disable account storage for bookmarks. This
-  // way the user does not get duplicate data if they turn off sync (and
-  // choose to preserve their data locally) and then sign in again.
-  // This is safe to remove with the deprecation of
-  // `signin::ConsentLevel::kSync`.
-  if (event_details.GetEventTypeFor(signin::ConsentLevel::kSync) ==
-      signin::PrimaryAccountChangeEvent::Type::kSet) {
-    auto current_gaia_id = event_details.GetCurrentState().primary_account.gaia;
-    auto prefs = SigninPrefs(*client_->GetPrefs());
-
-    if (prefs.GetBookmarksExplicitBrowserSignin(current_gaia_id)) {
-      base::UmaHistogramBoolean(
-          "Signin.Bookmarks.SyncTurnedOnWithAccountStorageEnabled", true);
-    }
-
-    prefs.SetBookmarksExplicitBrowserSignin(current_gaia_id, false);
-  }
-#endif
 }
 
 void PrimaryAccountManager::FirePrimaryAccountChanged(
--- a/components/signin/internal/identity_manager/primary_account_manager_unittest.cc
+++ b/components/signin/internal/identity_manager/primary_account_manager_unittest.cc
@@ -30,7 +30,6 @@
 #include "components/signin/public/base/gaia_id_hash.h"
 #include "components/signin/public/base/signin_client.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/base/test_signin_client.h"
--- a/components/signin/internal/identity_manager/primary_account_mutator_impl.cc
+++ b/components/signin/internal/identity_manager/primary_account_mutator_impl.cc
@@ -16,7 +16,6 @@
 #include "components/signin/public/base/signin_buildflags.h"
 #include "components/signin/public/base/signin_client.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "google_apis/gaia/core_account_id.h"
 
@@ -56,7 +55,7 @@ PrimaryAccountMutatorImpl::SetPrimaryAcc
   DCHECK(!account_info.gaia.empty());
 
 #if !BUILDFLAG(IS_CHROMEOS)
-  bool is_signin_allowed = pref_service_->GetBoolean(prefs::kSigninAllowed);
+  bool is_signin_allowed = false;
   if (!is_signin_allowed) {
     return PrimaryAccountError::kSigninNotAllowed;
   }
--- a/components/signin/internal/identity_manager/profile_oauth2_token_service.cc
+++ b/components/signin/internal/identity_manager/profile_oauth2_token_service.cc
@@ -15,7 +15,6 @@
 #include "components/signin/internal/identity_manager/profile_oauth2_token_service_delegate.h"
 #include "components/signin/public/base/device_id_helper.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "google_apis/gaia/gaia_constants.h"
 #include "google_apis/gaia/google_service_auth_error.h"
 #include "google_apis/gaia/oauth2_access_token_consumer.h"
@@ -107,8 +106,6 @@ bool ProfileOAuth2TokenService::HasRefre
 // static
 void ProfileOAuth2TokenService::RegisterProfilePrefs(
     PrefRegistrySimple* registry) {
-  registry->RegisterStringPref(prefs::kGoogleServicesSigninScopedDeviceId,
-                               std::string());
 }
 
 ProfileOAuth2TokenServiceDelegate* ProfileOAuth2TokenService::GetDelegate() {
--- a/components/signin/internal/identity_manager/profile_oauth2_token_service_delegate_chromeos_unittest.cc
+++ b/components/signin/internal/identity_manager/profile_oauth2_token_service_delegate_chromeos_unittest.cc
@@ -29,7 +29,6 @@
 #include "components/signin/internal/identity_manager/account_tracker_service.h"
 #include "components/signin/internal/identity_manager/mock_profile_oauth2_token_service_observer.h"
 #include "components/signin/internal/identity_manager/profile_oauth2_token_service_observer.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/test_signin_client.h"
 #include "components/signin/public/identity_manager/account_capabilities_test_mutator.h"
 #include "components/signin/public/identity_manager/account_info.h"
--- a/components/signin/public/base/BUILD.gn
+++ b/components/signin/public/base/BUILD.gn
@@ -153,8 +153,6 @@ component("signin_switches") {
   defines = [ "IS_SIGNIN_SWITCHES_IMPL" ]
 
   sources = [
-    "signin_pref_names.cc",
-    "signin_pref_names.h",
     "signin_switches.cc",
     "signin_switches.h",
   ]
--- a/components/signin/public/base/device_id_helper.cc
+++ b/components/signin/public/base/device_id_helper.cc
@@ -8,7 +8,6 @@
 #include "base/command_line.h"
 #include "base/uuid.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 
 namespace signin {
@@ -16,20 +15,12 @@ namespace signin {
 #if !BUILDFLAG(IS_CHROMEOS)
 
 std::string GetSigninScopedDeviceId(PrefService* prefs) {
-  std::string signin_scoped_device_id =
-      prefs->GetString(prefs::kGoogleServicesSigninScopedDeviceId);
-  if (signin_scoped_device_id.empty()) {
-    // If device_id doesn't exist then generate new and save in prefs.
-    signin_scoped_device_id = RecreateSigninScopedDeviceId(prefs);
-  }
-  return signin_scoped_device_id;
+  return RecreateSigninScopedDeviceId(prefs);
 }
 
 std::string RecreateSigninScopedDeviceId(PrefService* prefs) {
   std::string signin_scoped_device_id = GenerateSigninScopedDeviceId();
   DCHECK(!signin_scoped_device_id.empty());
-  prefs->SetString(prefs::kGoogleServicesSigninScopedDeviceId,
-                   signin_scoped_device_id);
   return signin_scoped_device_id;
 }
 
--- a/components/signin/public/base/device_id_helper_unittest.cc
+++ b/components/signin/public/base/device_id_helper_unittest.cc
@@ -7,7 +7,6 @@
 #include <string>
 
 #include "build/build_config.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/sync_preferences/testing_pref_service_syncable.h"
 #include "testing/gtest/include/gtest/gtest.h"
 
@@ -23,40 +22,23 @@ TEST(DeviceIdHelper, GenerateSigninScope
 
 TEST(DeviceIdHelper, RecreateSigninScopedDeviceId) {
   sync_preferences::TestingPrefServiceSyncable prefs;
-  prefs.registry()->RegisterStringPref(
-      prefs::kGoogleServicesSigninScopedDeviceId, std::string());
-  ASSERT_TRUE(
-      prefs.GetString(prefs::kGoogleServicesSigninScopedDeviceId).empty());
 
   std::string device_id_1 = RecreateSigninScopedDeviceId(&prefs);
   EXPECT_FALSE(device_id_1.empty());
-  EXPECT_EQ(device_id_1,
-            prefs.GetString(prefs::kGoogleServicesSigninScopedDeviceId));
 
   std::string device_id_2 = RecreateSigninScopedDeviceId(&prefs);
   EXPECT_FALSE(device_id_2.empty());
   EXPECT_NE(device_id_1, device_id_2);
-  EXPECT_EQ(device_id_2,
-            prefs.GetString(prefs::kGoogleServicesSigninScopedDeviceId));
 }
 
 TEST(DeviceIdHelper, GetSigninScopedDeviceId) {
   sync_preferences::TestingPrefServiceSyncable prefs;
-  prefs.registry()->RegisterStringPref(
-      prefs::kGoogleServicesSigninScopedDeviceId, std::string());
-
-  ASSERT_TRUE(
-      prefs.GetString(prefs::kGoogleServicesSigninScopedDeviceId).empty());
 
   std::string device_id_1 = GetSigninScopedDeviceId(&prefs);
   EXPECT_FALSE(device_id_1.empty());
-  EXPECT_EQ(device_id_1,
-            prefs.GetString(prefs::kGoogleServicesSigninScopedDeviceId));
 
   std::string device_id_2 = GetSigninScopedDeviceId(&prefs);
   EXPECT_EQ(device_id_1, device_id_2);
-  EXPECT_EQ(device_id_2,
-            prefs.GetString(prefs::kGoogleServicesSigninScopedDeviceId));
 }
 
 #endif
--- a/components/signin/public/base/signin_prefs.cc
+++ b/components/signin/public/base/signin_prefs.cc
@@ -13,7 +13,6 @@
 #include "components/prefs/pref_registry_simple.h"
 #include "components/prefs/pref_service.h"
 #include "components/prefs/scoped_user_pref_update.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "google_apis/gaia/gaia_id.h"
 
@@ -120,8 +119,6 @@ SigninPrefs::~SigninPrefs() = default;
 
 void SigninPrefs::RegisterProfilePrefs(PrefRegistrySimple* registry) {
   registry->RegisterDictionaryPref(kSigninAccountPrefs);
-  registry->RegisterIntegerPref(prefs::kHistorySyncSuccessiveDeclineCount, 0);
-  registry->RegisterInt64Pref(prefs::kHistorySyncLastDeclinedTimestamp, 0);
 }
 
 bool SigninPrefs::HasAccountPrefs(const GaiaId& gaia_id) const {
--- a/components/signin/public/base/signin_switches.cc
+++ b/components/signin/public/base/signin_switches.cc
@@ -7,7 +7,6 @@
 #include "base/feature_list.h"
 #include "base/time/time.h"
 #include "components/prefs/pref_service.h"
-#include "components/signin/public/base/signin_pref_names.h"
 
 #if BUILDFLAG(IS_WIN)
 #include "base/win/windows_version.h"
@@ -153,11 +152,6 @@ BASE_FEATURE(kEnableBoundSessionCredenti
 );
 
 bool IsBoundSessionCredentialsEnabled(const PrefService* profile_prefs) {
-  // Enterprise policy takes precedence over the feature value.
-  if (profile_prefs->HasPrefPath(prefs::kBoundSessionCredentialsEnabled)) {
-    return profile_prefs->GetBoolean(prefs::kBoundSessionCredentialsEnabled);
-  }
-
   return base::FeatureList::IsEnabled(kEnableBoundSessionCredentials);
 }
 
@@ -191,11 +185,6 @@ BASE_FEATURE(kEnableChromeRefreshTokenBi
              base::FEATURE_DISABLED_BY_DEFAULT);
 
 bool IsChromeRefreshTokenBindingEnabled(const PrefService* profile_prefs) {
-  // Enterprise policy takes precedence over the feature value.
-  if (profile_prefs->HasPrefPath(prefs::kBoundSessionCredentialsEnabled)) {
-    return profile_prefs->GetBoolean(prefs::kBoundSessionCredentialsEnabled);
-  }
-
   return base::FeatureList::IsEnabled(kEnableChromeRefreshTokenBinding);
 }
 
--- a/components/signin/public/identity_manager/identity_utils.cc
+++ b/components/signin/public/identity_manager/identity_utils.cc
@@ -14,7 +14,6 @@
 #include "base/strings/utf_string_conversions.h"
 #include "components/prefs/pref_service.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
@@ -66,21 +65,13 @@ bool IsUsernameAllowedByPattern(std::str
 
 bool IsUsernameAllowedByPatternFromPrefs(const PrefService* prefs,
                                          const std::string& username) {
-  return IsUsernameAllowedByPattern(
-      username, prefs->GetString(prefs::kGoogleServicesUsernamePattern));
+  return true;
 }
 
 bool IsImplicitBrowserSigninOrExplicitDisabled(
     const IdentityManager* identity_manager,
     const PrefService* prefs) {
-#if BUILDFLAG(ENABLE_DICE_SUPPORT)
-  // Check if the user is implicitly signed in.
-  // Signed out users or signed in explicitly should return false.
-  return identity_manager->HasPrimaryAccount(signin::ConsentLevel::kSignin) &&
-         !prefs->GetBoolean(prefs::kExplicitBrowserSignin);
-#else
-  return true;
-#endif
+  return false;
 }
 
 bool AreGoogleCookiesRebuiltAfterClearingWhenSignedIn(
--- a/components/signin/public/identity_manager/identity_utils_unittest.cc
+++ b/components/signin/public/identity_manager/identity_utils_unittest.cc
@@ -12,7 +12,6 @@
 #include "components/prefs/pref_service.h"
 #include "components/prefs/testing_pref_service.h"
 #include "components/signin/public/base/consent_level.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/accounts_in_cookie_jar_info.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
--- a/components/signin/public/identity_manager/primary_account_mutator_unittest.cc
+++ b/components/signin/public/identity_manager/primary_account_mutator_unittest.cc
@@ -15,7 +15,6 @@
 #include "components/signin/public/base/consent_level.h"
 #include "components/signin/public/base/signin_buildflags.h"
 #include "components/signin/public/base/signin_metrics.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/signin/public/identity_manager/identity_manager.h"
 #include "components/signin/public/identity_manager/identity_test_environment.h"
--- a/components/supervised_user/core/browser/supervised_user_pref_store.cc
+++ b/components/supervised_user/core/browser/supervised_user_pref_store.cc
@@ -20,7 +20,6 @@
 #include "components/policy/core/common/policy_pref_names.h"
 #include "components/prefs/pref_value_map.h"
 #include "components/safe_search_api/safe_search_util.h"
-#include "components/signin/public/base/signin_pref_names.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/supervised_user/core/browser/supervised_user_content_filters_service.h"
 #include "components/supervised_user/core/browser/supervised_user_settings_service.h"
@@ -59,14 +58,6 @@ SupervisedUserSettingsPrefMappingEntry k
         prefs::kSupervisedUserSafeSites,
     },
     {
-        supervised_user::kSigninAllowed,
-        prefs::kSigninAllowed,
-    },
-    {
-        supervised_user::kSigninAllowedOnNextStartup,
-        prefs::kSigninAllowedOnNextStartup,
-    },
-    {
         supervised_user::kSkipParentApprovalToInstallExtensions,
         prefs::kSkipParentApprovalToInstallExtensions,
     },
--- a/components/sync/service/sync_prefs.cc
+++ b/components/sync/service/sync_prefs.cc
@@ -25,8 +25,6 @@
 #include "components/saved_tab_groups/public/pref_names.h"
 #include "components/signin/public/base/gaia_id_hash.h"
 #include "components/signin/public/base/signin_buildflags.h"
-#include "components/signin/public/base/signin_pref_names.h"
-#include "components/signin/public/base/signin_prefs.h"
 #include "components/signin/public/base/signin_switches.h"
 #include "components/sync/base/account_pref_utils.h"
 #include "components/sync/base/features.h"
@@ -129,16 +127,6 @@ SyncPrefs::SyncPrefs(PrefService* pref_s
       base::BindRepeating(&SyncPrefs::OnSelectedTypesPrefChanged,
                           base::Unretained(this)));
 
-  if (base::FeatureList::IsEnabled(switches::kOfferMigrationToDiceUsers) ||
-      base::FeatureList::IsEnabled(switches::kRollbackDiceMigration)) {
-    // The explicit browser signin pref is used for determining whether some
-    // data types are selected by default. Therefore, upon a change, the
-    // selected types may change.
-    pref_change_registrar_.Add(
-        ::prefs::kExplicitBrowserSignin,
-        base::BindRepeating(&SyncPrefs::OnSelectedTypesPrefChanged,
-                            base::Unretained(this)));
-  }
 }
 
 SyncPrefs::~SyncPrefs() {
@@ -243,7 +231,7 @@ bool SyncPrefs::IsExplicitBrowserSignin(
 #else
   // On desktop `prefs::kExplicitBrowserSignin` determines whether the sign-in
   // is explicit or implicit.
-  return pref_service_->GetBoolean(::prefs::kExplicitBrowserSignin);
+  return false;
 #endif
 }
 
@@ -1043,30 +1031,6 @@ void SyncPrefs::MaybeMigrateAutofillToPe
     return;
   }
   pref_service->SetBoolean(kAutofillPerAccountPrefMigrationDone, true);
-
-  const GaiaId last_syncing_gaia_id(
-      pref_service->GetString(::prefs::kGoogleServicesLastSyncingGaiaId));
-  if (last_syncing_gaia_id.empty()) {
-    return;
-  }
-
-  if (pref_service->GetBoolean(prefs::internal::kSyncKeepEverythingSynced)) {
-    return;
-  }
-
-  for (auto user_selectable_type :
-       {UserSelectableType::kPasswords, UserSelectableType::kAutofill}) {
-    const char* const pref_name_for_type =
-        GetPrefNameForType(user_selectable_type);
-    if (pref_service->GetBoolean(pref_name_for_type)) {
-      continue;
-    }
-
-    SetAccountKeyedPrefDictEntry(
-        pref_service, prefs::internal::kSelectedTypesPerAccount,
-        signin::GaiaIdHash::FromGaiaId(last_syncing_gaia_id),
-        pref_name_for_type, base::Value(false));
-  }
 }
 #endif  // BUILDFLAG(ENABLE_DICE_SUPPORT)
 
@@ -1107,21 +1071,15 @@ bool SyncPrefs::IsTypeSelectedByDefaultI
       // Before kReplaceSyncPromosWithSignInPromos, Bookmarks and Reading List
       // require a specific explicit sign in (relevant for desktop only).
       return base::FeatureList::IsEnabled(kReplaceSyncPromosWithSignInPromos) ||
-             SigninPrefs(*pref_service_)
-                 .GetBookmarksExplicitBrowserSignin(gaia_id) ||
              base::FeatureList::IsEnabled(
                  kEnableBookmarksSelectedTypeOnSigninForTesting);
     case UserSelectableType::kPreferences:
     case UserSelectableType::kThemes:
-      return base::FeatureList::IsEnabled(kReplaceSyncPromosWithSignInPromos) ||
-             pref_service_->GetBoolean(
-                 ::prefs::kPrefsThemesSearchEnginesAccountStorageEnabled);
+      return false;
     case UserSelectableType::kExtensions:
       // Before kReplaceSyncPromosWithSignInPromos, Extensions require a
       // specific explicit sign in.
-      return base::FeatureList::IsEnabled(kReplaceSyncPromosWithSignInPromos) ||
-             SigninPrefs(*pref_service_)
-                 .GetExtensionsExplicitBrowserSignin(gaia_id);
+      return false;
     case UserSelectableType::kApps:
     case UserSelectableType::kProductComparison:
     case UserSelectableType::kCookies:
--- a/components/sync_preferences/common_syncable_prefs_database.cc
+++ b/components/sync_preferences/common_syncable_prefs_database.cc
@@ -24,7 +24,6 @@
 #include "components/payments/core/payment_prefs.h"
 #include "components/plus_addresses/plus_address_prefs.h"
 #include "components/privacy_sandbox/tracking_protection_prefs.h"
-#include "components/safe_browsing/core/common/safe_browsing_prefs.h"
 #include "components/saved_tab_groups/public/pref_names.h"
 #include "components/sharing_message/pref_names.h"
 #include "components/sync/base/data_type.h"
@@ -92,9 +91,7 @@ enum {
   // kSyncedLastTimePasswordCheckCompleted = 43, (deprecated)
   kWasAutoSignInFirstRunExperienceShown = 44,
   kCanMakePaymentEnabled = 45,
-  kAccountTailoredSecurityUpdateTimestamp = 46,
   kCookieControlsMode = 47,
-  kSafeBrowsingEnabled = 48,
   // kSyncedDefaultSearchProviderGUID = 49, (deprecated)
   kPrefForceTriggerTranslateCount = 50,
   // kPrefNeverPromptSitesDeprecated = 51, (deprecated)
@@ -230,16 +227,9 @@ constexpr auto kCommonSyncablePrefsAllow
         {payments::kCanMakePaymentEnabled,
          {syncable_prefs_ids::kCanMakePaymentEnabled, syncer::PREFERENCES,
           PrefSensitivity::kNone, MergeBehavior::kNone}},
-        {prefs::kAccountTailoredSecurityUpdateTimestamp,
-         {syncable_prefs_ids::kAccountTailoredSecurityUpdateTimestamp,
-          syncer::PRIORITY_PREFERENCES, PrefSensitivity::kNone,
-          MergeBehavior::kNone}},
         {prefs::kCookieControlsMode,
          {syncable_prefs_ids::kCookieControlsMode, syncer::PREFERENCES,
           PrefSensitivity::kNone, MergeBehavior::kNone}},
-        {prefs::kSafeBrowsingEnabled,
-         {syncable_prefs_ids::kSafeBrowsingEnabled, syncer::PREFERENCES,
-          PrefSensitivity::kNone, MergeBehavior::kNone}},
         {tab_groups::prefs::kAutoPinNewTabGroups,
          {syncable_prefs_ids::kAutoPinNewTabGroups, syncer::PREFERENCES,
           PrefSensitivity::kNone, MergeBehavior::kNone}},
--- a/content/browser/webid/federated_auth_request_impl.cc
+++ b/content/browser/webid/federated_auth_request_impl.cc
@@ -193,31 +193,6 @@ FederatedAuthRequestImpl::~FederatedAuth
 void FederatedAuthRequestImpl::Create(
     RenderFrameHost* host,
     mojo::PendingReceiver<blink::mojom::FederatedAuthRequest> receiver) {
-  CHECK(host);
-
-  BrowserContext* browser_context = host->GetBrowserContext();
-  raw_ptr<FederatedIdentityApiPermissionContextDelegate>
-      api_permission_context =
-          browser_context->GetFederatedIdentityApiPermissionContext();
-  raw_ptr<FederatedIdentityAutoReauthnPermissionContextDelegate>
-      auto_reauthn_permission_context =
-          browser_context->GetFederatedIdentityAutoReauthnPermissionContext();
-  raw_ptr<FederatedIdentityPermissionContextDelegate> permission_context =
-      browser_context->GetFederatedIdentityPermissionContext();
-  raw_ptr<IdentityRegistry> identity_registry =
-      IdentityRegistry::FromWebContents(WebContents::FromRenderFrameHost(host));
-
-  if (!api_permission_context || !auto_reauthn_permission_context ||
-      !permission_context) {
-    return;
-  }
-
-  // FederatedAuthRequestImpl owns itself. It will self-destruct when a mojo
-  // interface error occurs, the RenderFrameHost is deleted, or the
-  // RenderFrameHost navigates to a new document.
-  new FederatedAuthRequestImpl(
-      *host, api_permission_context, auto_reauthn_permission_context,
-      permission_context, identity_registry, std::move(receiver));
 }
 
 FederatedAuthRequestImpl& FederatedAuthRequestImpl::CreateForTesting(
