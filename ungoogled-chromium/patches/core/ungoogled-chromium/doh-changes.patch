--- a/chrome/browser/net/default_dns_over_https_config_source.cc
+++ b/chrome/browser/net/default_dns_over_https_config_source.cc
@@ -25,7 +25,7 @@ DefaultDnsOverHttpsConfigSource::Default
   if (set_up_pref_defaults) {
     local_state->SetDefaultPrefValue(prefs::kDnsOverHttpsMode,
                                      base::Value(SecureDnsConfig::ModeToString(
-                                         net::SecureDnsMode::kAutomatic)));
+                                         net::SecureDnsMode::kOff)));
   }
 }
 
--- a/chrome/browser/net/dns_probe_runner.cc
+++ b/chrome/browser/net/dns_probe_runner.cc
@@ -18,7 +18,7 @@
 
 namespace chrome_browser_net {
 
-const char DnsProbeRunner::kKnownGoodHostname[] = "google.com";
+const char DnsProbeRunner::kKnownGoodHostname[] = "example.com";
 
 namespace {
 
--- a/net/dns/public/doh_provider_entry.cc
+++ b/net/dns/public/doh_provider_entry.cc
@@ -171,30 +171,6 @@ const DohProviderEntry::List& DohProvide
         /*display_globally=*/false,
         /*display_countries=*/{"EE", "DE"},
         LoggingLevel::kNormal},
-       {"Google",
-        MAKE_BASE_FEATURE_WITH_STATIC_STORAGE(DohProviderGoogle,
-                                              base::FEATURE_ENABLED_BY_DEFAULT),
-        {"*******", "*******", "2001:4860:4860::8888", "2001:4860:4860::8844"},
-        /*dns_over_tls_hostnames=*/
-        {"dns.google", "dns.google.com", "8888.google"},
-        "https://dns.google/dns-query{?dns}",
-        /*ui_name=*/"Google (Public DNS)",
-        "https://developers.google.com/speed/public-dns/"
-        /*privacy_policy=*/"privacy",
-        /*display_globally=*/true,
-        /*display_countries=*/{},
-        LoggingLevel::kExtra},
-       {"GoogleDns64",
-        MAKE_BASE_FEATURE_WITH_STATIC_STORAGE(DohProviderGoogleDns64,
-                                              base::FEATURE_ENABLED_BY_DEFAULT),
-        {"2001:4860:4860::64", "2001:4860:4860::6464"},
-        /*dns_over_tls_hostnames=*/{"dns64.dns.google"},
-        "https://dns64.dns.google/dns-query{?dns}",
-        /*ui_name=*/"",
-        /*privacy_policy=*/"",
-        /*display_globally=*/false,
-        /*display_countries=*/{},
-        LoggingLevel::kNormal},
        {"Iij",
         MAKE_BASE_FEATURE_WITH_STATIC_STORAGE(DohProviderIij,
                                               base::FEATURE_ENABLED_BY_DEFAULT),
--- a/services/network/public/cpp/features.cc
+++ b/services/network/public/cpp/features.cc
@@ -108,12 +108,7 @@ BASE_FEATURE(kSplitAuthCacheByNetworkIso
 // Enable usage of hardcoded DoH upgrade mapping for use in automatic mode.
 BASE_FEATURE(kDnsOverHttpsUpgrade,
              "DnsOverHttpsUpgrade",
-#if BUILDFLAG(IS_WIN) || BUILDFLAG(IS_CHROMEOS) || BUILDFLAG(IS_MAC) || \
-    BUILDFLAG(IS_ANDROID) || BUILDFLAG(IS_LINUX)
-             base::FEATURE_ENABLED_BY_DEFAULT
-#else
              base::FEATURE_DISABLED_BY_DEFAULT
-#endif
 );
 
 // When enabled, the requests in a third party context to domains included in
