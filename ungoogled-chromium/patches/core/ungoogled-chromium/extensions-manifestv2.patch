--- a/chrome/browser/extensions/api/developer_private/extension_info_generator.cc
+++ b/chrome/browser/extensions/api/developer_private/extension_info_generator.cc
@@ -864,19 +864,6 @@ void ExtensionInfoGenerator::FillExtensi
         toolbar_actions_model->IsActionPinned(extension.id());
   }
 
-  // MV2 deprecation.
-  ManifestV2ExperimentManager* mv2_experiment_manager =
-      ManifestV2ExperimentManager::Get(profile);
-  CHECK(mv2_experiment_manager);
-  info.is_affected_by_mv2_deprecation =
-      mv2_experiment_manager->IsExtensionAffected(extension);
-  info.did_acknowledge_mv2_deprecation_notice =
-      mv2_experiment_manager->DidUserAcknowledgeNotice(extension.id());
-  if (info.web_store_url.length() > 0) {
-    info.recommendations_url =
-        extension_urls::GetNewWebstoreItemRecommendationsUrl(extension.id())
-            .spec();
-  }
 
   // Whether the extension can be uploaded as an account extension.
   // `CanUploadAsAccountExtension` should already check for the feature flag
--- a/chrome/browser/extensions/extension_management.cc
+++ b/chrome/browser/extensions/extension_management.cc
@@ -355,31 +355,7 @@ bool ExtensionManagement::IsAllowedManif
     int manifest_version,
     const std::string& extension_id,
     Manifest::Type manifest_type) {
-  bool enabled_by_default =
-      !base::FeatureList::IsEnabled(
-          extensions_features::kExtensionsManifestV3Only) ||
-      manifest_version >= 3;
-
-  // Manifest version policy only supports normal extensions and Chrome OS login
-  // screen extension.
-  if (manifest_type != Manifest::Type::TYPE_EXTENSION &&
-      manifest_type != Manifest::Type::TYPE_LOGIN_SCREEN_EXTENSION) {
-    return enabled_by_default;
-  }
-  switch (global_settings_->manifest_v2_setting) {
-    case internal::GlobalSettings::ManifestV2Setting::kDefault:
-      return enabled_by_default;
-    case internal::GlobalSettings::ManifestV2Setting::kDisabled:
-      return manifest_version >= 3;
-    case internal::GlobalSettings::ManifestV2Setting::kEnabled:
       return true;
-    case internal::GlobalSettings::ManifestV2Setting::kEnabledForForceInstalled:
-      auto installation_mode =
-          GetInstallationMode(extension_id, /*update_url=*/std::string());
-      return manifest_version >= 3 ||
-             installation_mode == ManagedInstallationMode::kForced ||
-             installation_mode == ManagedInstallationMode::kRecommended;
-  }
 }
 
 bool ExtensionManagement::IsAllowedManifestVersion(const Extension* extension) {
@@ -400,26 +376,8 @@ bool ExtensionManagement::IsExemptFromMV
     return false;
   }
 
-  switch (global_settings_->manifest_v2_setting) {
-    case internal::GlobalSettings::ManifestV2Setting::kDefault:
-      // Default browser behavior. Not exempt.
-      return false;
-    case internal::GlobalSettings::ManifestV2Setting::kDisabled:
-      // All MV2 extensions are disallowed. Not exempt.
-      return false;
-    case internal::GlobalSettings::ManifestV2Setting::kEnabled:
       // All MV2 extensions are allowed. Exempt.
       return true;
-    case internal::GlobalSettings::ManifestV2Setting::kEnabledForForceInstalled:
-      // Force-installed MV2 extensions are allowed. Exempt if it's a force-
-      // installed extension only.
-      auto installation_mode =
-          GetInstallationMode(extension_id, /*update_url=*/std::string());
-      return installation_mode == ManagedInstallationMode::kForced ||
-             installation_mode == ManagedInstallationMode::kRecommended;
-  }
-
-  return false;
 }
 
 bool ExtensionManagement::IsAllowedByUnpublishedAvailabilityPolicy(
--- a/chrome/browser/extensions/manifest_v2_experiment_manager.cc
+++ b/chrome/browser/extensions/manifest_v2_experiment_manager.cc
@@ -145,22 +145,6 @@ bool ManifestV2ExperimentManagerFactory:
 
 // Determines the current stage of the MV2 deprecation experiments.
 MV2ExperimentStage CalculateCurrentExperimentStage() {
-  // Return the "highest" stage that is currently active for the user.
-  if (base::FeatureList::IsEnabled(
-          extensions_features::kExtensionManifestV2Unsupported)) {
-    return MV2ExperimentStage::kUnsupported;
-  }
-
-  if (base::FeatureList::IsEnabled(
-          extensions_features::kExtensionManifestV2Disabled)) {
-    return MV2ExperimentStage::kDisableWithReEnable;
-  }
-
-  if (base::FeatureList::IsEnabled(
-          extensions_features::kExtensionManifestV2DeprecationWarning)) {
-    return MV2ExperimentStage::kWarning;
-  }
-
   return MV2ExperimentStage::kNone;
 }
 
@@ -200,19 +184,7 @@ PrefMap GetGlobalNoticeAcknowledgedPrefF
 // Returns true if legacy extensions should be disabled, looking at both
 // experiment stage and global state.
 bool ShouldDisableLegacyExtensions(MV2ExperimentStage stage) {
-  if (g_allow_mv2_for_testing) {
-    // We allow legacy MV2 extensions for testing purposes.
-    return false;
-  }
-
-  switch (stage) {
-    case MV2ExperimentStage::kNone:
-    case MV2ExperimentStage::kWarning:
       return false;
-    case MV2ExperimentStage::kDisableWithReEnable:
-    case MV2ExperimentStage::kUnsupported:
-      return true;
-  }
 }
 
 // Returns true if the given `stage` is one in which extension enablement should
--- a/chrome/browser/ui/webui/extensions/extensions_ui.cc
+++ b/chrome/browser/ui/webui/extensions/extensions_ui.cc
@@ -482,7 +482,7 @@ content::WebUIDataSource* CreateAndAddEx
           extensions_features::kExtensionsMenuAccessControlWithPermittedSites));
   source->AddBoolean(
       "safetyHubThreeDotDetails",
-      base::FeatureList::IsEnabled(features::kSafetyHubThreeDotDetails));
+      false);
 
   // MV2 deprecation.
   auto* mv2_experiment_manager = ManifestV2ExperimentManager::Get(profile);
--- a/extensions/common/extension.cc
+++ b/extensions/common/extension.cc
@@ -103,13 +103,6 @@ bool IsManifestSupported(int manifest_ve
   // Supported versions are always safe.
   if (manifest_version >= kMinimumSupportedManifestVersion &&
       manifest_version <= kMaximumSupportedManifestVersion) {
-    // Emit a warning for unpacked extensions on Manifest V2 warning that
-    // MV2 is deprecated.
-    if (type == Manifest::TYPE_EXTENSION && manifest_version == 2 &&
-        Manifest::IsUnpackedLocation(location) &&
-        !g_silence_deprecated_manifest_version_warnings) {
-      *warning = errors::kManifestV2IsDeprecatedWarning;
-    }
     return true;
   }
 
