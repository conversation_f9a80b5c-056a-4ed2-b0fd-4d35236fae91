# Disable some background communication with clients2.google.com

--- a/chrome/browser/chrome_content_browser_client.cc
+++ b/chrome/browser/chrome_content_browser_client.cc
@@ -2711,24 +2711,6 @@ void ChromeContentBrowserClient::AppendE
     command_line->AppendSwitchASCII(switches::kMetricsClientID,
                                     client_info->client_id);
   }
-#elif BUILDFLAG(IS_POSIX)
-#if !BUILDFLAG(IS_ANDROID)
-  pid_t pid;
-  if (crash_reporter::GetHandlerSocket(nullptr, &pid)) {
-    command_line->AppendSwitchASCII(
-        crash_reporter::switches::kCrashpadHandlerPid,
-        base::NumberToString(pid));
-  }
-#endif
-  std::string switch_value;
-  std::unique_ptr<metrics::ClientInfo> client_info =
-      GoogleUpdateSettings::LoadMetricsClientInfo();
-  if (client_info) {
-    switch_value = client_info->client_id;
-  }
-  switch_value.push_back(',');
-  switch_value.append(chrome::GetChannelName(chrome::WithExtendedStable(true)));
-  command_line->AppendSwitchASCII(switches::kEnableCrashReporter, switch_value);
 #endif
 
   if (logging::DialogsAreSuppressed()) {
--- a/components/crash/core/app/crashpad.cc
+++ b/components/crash/core/app/crashpad.cc
@@ -72,6 +72,12 @@ void InitializeDatabasePath(const base::
 }
 
 bool InitializeCrashpadImpl(bool initial_client,
+    const std::string& a, const std::string& b, const base::FilePath& c,
+    const std::vector<std::string>& d, bool e) {
+  return false;
+}
+[[maybe_unused]]
+bool DeadInitializeCrashpadImpl(bool initial_client,
                             const std::string& process_type,
                             const std::string& user_data_dir,
                             const base::FilePath& exe_path,
--- a/components/gwp_asan/client/gwp_asan.cc
+++ b/components/gwp_asan/client/gwp_asan.cc
@@ -345,6 +345,11 @@ GWP_ASAN_EXPORT std::optional<AllocatorS
 
 // Exported for testing.
 GWP_ASAN_EXPORT std::optional<AllocatorSettings> GetAllocatorSettings(
+    const base::Feature& a, bool b, std::string_view c) {
+  return std::nullopt;
+}
+[[maybe_unused]]
+std::optional<AllocatorSettings> DeadGetAllocatorSettings(
     const base::Feature& feature,
     bool boost_sampling,
     std::string_view process_type) {
--- a/third_party/crashpad/crashpad/client/crashpad_client_linux.cc
+++ b/third_party/crashpad/crashpad/client/crashpad_client_linux.cc
@@ -747,8 +747,6 @@ void CrashpadClient::CrashWithoutDump(co
 // static
 void CrashpadClient::SetFirstChanceExceptionHandler(
     FirstChanceHandler handler) {
-  DCHECK(SignalHandler::Get());
-  SignalHandler::Get()->SetFirstChanceHandler(handler);
 }
 
 // static
