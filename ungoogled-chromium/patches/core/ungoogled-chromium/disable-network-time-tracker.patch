# Disable Network Time Tracker
# This connects to Google to check if the system time is correct when a website certificate
# date seems incorrect, according to https://bugs.chromium.org/p/chromium/issues/detail?id=725232,
# Fixes https://github.com/ungoogled-software/ungoogled-chromium/issues/302

--- a/components/network_time/network_time_tracker.cc
+++ b/components/network_time/network_time_tracker.cc
@@ -279,7 +279,7 @@ void NetworkTimeTracker::UpdateNetworkTi
 }
 
 bool NetworkTimeTracker::AreTimeFetchesEnabled() const {
-  return base::FeatureList::IsEnabled(kNetworkTimeServiceQuerying);
+  return false;
 }
 
 NetworkTimeTracker::FetchBehavior NetworkTimeTracker::GetFetchBehavior() const {
