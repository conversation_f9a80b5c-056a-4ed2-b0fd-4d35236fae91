# Disables use of a binary for preloading the Media Engagement index
# Said binary is: chrome/browser/resources/media/mei_preload/preloaded_data.pb
# According to media/base/media_switches (for PreloadMediaEngagementData), it
# "enables a list of origins to be considered as having a high MEI until there
# is enough local data to determine the user's preferred behavior." This feature
# does not seem to outweigh the benefit of removing the binary, thus this patch.

--- a/chrome/BUILD.gn
+++ b/chrome/BUILD.gn
@@ -367,7 +367,6 @@ if (!is_android && !is_mac) {
     }
 
     data_deps += [
-      "//chrome/browser/resources/media/mei_preload:component",
       "//components/webapps/isolated_web_apps/preload:component",
       "//third_party/widevine/cdm",
     ]
@@ -1181,7 +1180,6 @@ if (is_win) {
       ":optimization_guide_library",
       ":swiftshader_binaries",
       ":widevine_cdm_library",
-      "//chrome/browser/resources/media/mei_preload:component_bundle",
       "//components/webapps/isolated_web_apps/preload:component_bundle",
     ]
 
--- a/chrome/browser/resources/BUILD.gn
+++ b/chrome/browser/resources/BUILD.gn
@@ -104,11 +104,6 @@ group("resources") {
     ]
   }
 
-  if (is_mac) {
-    public_deps += [
-      "//chrome/browser/resources/media/mei_preload:component",
-    ]
-  }
 
   if (is_win || is_mac || is_linux || is_chromeos || is_desktop_android) {
     public_deps += [ "discards:resources" ]
