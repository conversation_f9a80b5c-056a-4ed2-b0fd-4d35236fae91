# Disables WebRTC log uploading to Google

--- a/chrome/browser/media/webrtc/webrtc_log_uploader.cc
+++ b/chrome/browser/media/webrtc/webrtc_log_uploader.cc
@@ -179,31 +179,13 @@ void WebRtcLogUploader::OnLoggingStopped
   DCHECK(meta_data.get());
   DCHECK(!upload_done_data.paths.directory.empty());
 
-  std::string compressed_log = CompressLog(log_buffer.get());
-
-  std::string local_log_id;
-
   if (base::PathExists(upload_done_data.paths.directory)) {
     webrtc_logging::DeleteOldWebRtcLogFiles(upload_done_data.paths.directory);
-
-    local_log_id =
-        base::NumberToString(base::Time::Now().InSecondsFSinceUnixEpoch());
-    base::FilePath log_file_path =
-        upload_done_data.paths.directory.AppendASCII(local_log_id)
-            .AddExtension(FILE_PATH_LITERAL(".gz"));
-    WriteCompressedLogToFile(compressed_log, log_file_path);
-
-    base::FilePath log_list_path =
-        webrtc_logging::TextLogList::GetWebRtcLogListFileForDirectory(
-            upload_done_data.paths.directory);
-    AddLocallyStoredLogInfoToUploadListFile(log_list_path, local_log_id);
   }
 
-  upload_done_data.local_log_id = local_log_id;
 
   if (is_text_log_upload_allowed) {
-    PrepareMultipartPostData(compressed_log, std::move(meta_data),
-                             std::move(upload_done_data));
+    NotifyUploadDoneAndLogStats(net::HTTP_OK, net::OK, "", std::move(upload_done_data));
   } else {
     main_task_runner_->PostTask(
         FROM_HERE,
@@ -220,28 +202,7 @@ void WebRtcLogUploader::PrepareMultipart
   DCHECK(!compressed_log.empty());
   DCHECK(meta_data.get());
 
-  std::unique_ptr<std::string> post_data(new std::string());
-  SetupMultipart(post_data.get(), compressed_log,
-                 upload_done_data.paths.incoming_rtp_dump,
-                 upload_done_data.paths.outgoing_rtp_dump, *meta_data.get());
-
-  // If a test has set the test string pointer, write to it and skip uploading.
-  // Still fire the upload callback so that we can run an extension API test
-  // using the test framework for that without hanging.
-  // TODO(grunell): Remove this when the api test for this feature is fully
-  // implemented according to the test plan. http://crbug.com/257329.
-  if (post_data_) {
-    *post_data_ = *post_data;
-    NotifyUploadDoneAndLogStats(net::HTTP_OK, net::OK, "",
-                                std::move(upload_done_data));
-    return;
-  }
-
-  main_task_runner_->PostTask(
-      FROM_HERE,
-      base::BindOnce(&WebRtcLogUploader::UploadCompressedLog,
-                     base::Unretained(this), std::move(upload_done_data),
-                     std::move(post_data)));
+  NotifyUploadDoneAndLogStats(net::HTTP_OK, net::OK, "", std::move(upload_done_data));
 }
 
 void WebRtcLogUploader::UploadStoredLog(
@@ -308,48 +269,6 @@ void WebRtcLogUploader::LoggingStoppedDo
   DCHECK(log_buffer.get());
   DCHECK(!log_paths.directory.empty());
 
-  webrtc_logging::DeleteOldWebRtcLogFiles(log_paths.directory);
-
-  base::FilePath log_list_path =
-      webrtc_logging::TextLogList::GetWebRtcLogListFileForDirectory(
-          log_paths.directory);
-
-  // Store the native log with a ".gz" extension.
-  std::string compressed_log = CompressLog(log_buffer.get());
-  base::FilePath native_log_path =
-      log_paths.directory.AppendASCII(log_id).AddExtension(
-          FILE_PATH_LITERAL(".gz"));
-  WriteCompressedLogToFile(compressed_log, native_log_path);
-  AddLocallyStoredLogInfoToUploadListFile(log_list_path, log_id);
-
-  // Move the rtp dump files to the log directory with a name of
-  // <log id>.rtp_[in|out].
-  if (!log_paths.incoming_rtp_dump.empty()) {
-    base::FilePath rtp_path =
-        log_paths.directory.AppendASCII(log_id).AddExtension(
-            FILE_PATH_LITERAL(".rtp_in"));
-    base::Move(log_paths.incoming_rtp_dump, rtp_path);
-  }
-
-  if (!log_paths.outgoing_rtp_dump.empty()) {
-    base::FilePath rtp_path =
-        log_paths.directory.AppendASCII(log_id).AddExtension(
-            FILE_PATH_LITERAL(".rtp_out"));
-    base::Move(log_paths.outgoing_rtp_dump, rtp_path);
-  }
-
-  if (meta_data.get() && !meta_data->empty()) {
-    base::Pickle pickle;
-    for (const auto& it : *meta_data.get()) {
-      pickle.WriteString(it.first);
-      pickle.WriteString(it.second);
-    }
-    base::FilePath meta_path =
-        log_paths.directory.AppendASCII(log_id).AddExtension(
-            FILE_PATH_LITERAL(".meta"));
-    base::WriteFile(meta_path, pickle);
-  }
-
   main_task_runner_->PostTask(
       FROM_HERE, base::BindOnce(std::move(done_callback), true, ""));
 
--- a/chrome/browser/ui/browser_ui_prefs.cc
+++ b/chrome/browser/ui/browser_ui_prefs.cc
@@ -135,7 +135,7 @@ void RegisterBrowserUserPrefs(user_prefs
   registry->RegisterStringPref(prefs::kWebRTCUDPPortRange, std::string());
   registry->RegisterBooleanPref(prefs::kWebRtcEventLogCollectionAllowed, false);
   registry->RegisterListPref(prefs::kWebRtcLocalIpsAllowedUrls);
-  registry->RegisterBooleanPref(prefs::kWebRtcTextLogCollectionAllowed, true);
+  registry->RegisterBooleanPref(prefs::kWebRtcTextLogCollectionAllowed, false);
 
   // We need to register the type of these preferences in order to query
   // them even though they're only typically controlled via policy.
