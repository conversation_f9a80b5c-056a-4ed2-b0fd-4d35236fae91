# Disable Google Cloud Messaging (GCM) client

--- a/components/gcm_driver/gcm_client_impl.cc
+++ b/components/gcm_driver/gcm_client_impl.cc
@@ -296,38 +296,6 @@ void GCMClientImpl::Initialize(
 }
 
 void GCMClientImpl::Start(StartMode start_mode) {
-  DCHECK_NE(UNINITIALIZED, state_);
-  DCHECK(io_task_runner_->RunsTasksInCurrentSequence());
-
-  if (state_ == LOADED) {
-    // Start the GCM if not yet.
-    if (start_mode == IMMEDIATE_START) {
-      // Give up the scheduling to wipe out the store since now some one starts
-      // to use GCM.
-      destroying_gcm_store_ptr_factory_.InvalidateWeakPtrs();
-
-      StartGCM();
-    }
-    return;
-  }
-
-  // The delay start behavior will be abandoned when Start has been called
-  // once with IMMEDIATE_START behavior.
-  if (start_mode == IMMEDIATE_START)
-    start_mode_ = IMMEDIATE_START;
-
-  // Bail out if the loading is not started or completed.
-  if (state_ != INITIALIZED)
-    return;
-
-  // Once the loading is completed, the check-in will be initiated.
-  // If we're in lazy start mode, don't create a new store since none is really
-  // using GCM functionality yet.
-  gcm_store_->Load((start_mode == IMMEDIATE_START) ? GCMStore::CREATE_IF_MISSING
-                                                   : GCMStore::DO_NOT_CREATE,
-                   base::BindOnce(&GCMClientImpl::OnLoadCompleted,
-                                  weak_ptr_factory_.GetWeakPtr()));
-  state_ = LOADING;
 }
 
 void GCMClientImpl::OnLoadCompleted(
@@ -431,6 +399,7 @@ void GCMClientImpl::StartGCM() {
 
 void GCMClientImpl::InitializeMCSClient() {
   DCHECK(network_connection_tracker_);
+  return;
   std::vector<GURL> endpoints;
   endpoints.push_back(gservices_settings_.GetMCSMainEndpoint());
   GURL fallback_endpoint = gservices_settings_.GetMCSFallbackEndpoint();
@@ -482,8 +451,6 @@ void GCMClientImpl::OnReady(const std::v
 void GCMClientImpl::StartMCSLogin() {
   DCHECK_EQ(READY, state_);
   DCHECK(device_checkin_info_.IsValid());
-  mcs_client_->Login(device_checkin_info_.android_id,
-                     device_checkin_info_.secret);
 }
 
 void GCMClientImpl::DestroyStoreWhenNotNeeded() {
@@ -554,8 +521,6 @@ void GCMClientImpl::SetLastTokenFetchTim
 void GCMClientImpl::UpdateHeartbeatTimer(
     std::unique_ptr<base::RetainingOneShotTimer> timer) {
   DCHECK(io_task_runner_->RunsTasksInCurrentSequence());
-  DCHECK(mcs_client_);
-  mcs_client_->UpdateHeartbeatTimer(std::move(timer));
 }
 
 void GCMClientImpl::AddInstanceIDData(const std::string& app_id,
@@ -598,35 +563,14 @@ void GCMClientImpl::GetInstanceIDData(co
 void GCMClientImpl::AddHeartbeatInterval(const std::string& scope,
                                          int interval_ms) {
   DCHECK(io_task_runner_->RunsTasksInCurrentSequence());
-  DCHECK(mcs_client_);
-  mcs_client_->AddHeartbeatInterval(scope, interval_ms);
 }
 
 void GCMClientImpl::RemoveHeartbeatInterval(const std::string& scope) {
   DCHECK(io_task_runner_->RunsTasksInCurrentSequence());
-  DCHECK(mcs_client_);
-  mcs_client_->RemoveHeartbeatInterval(scope);
 }
 
 void GCMClientImpl::StartCheckin() {
   DCHECK(io_task_runner_->RunsTasksInCurrentSequence());
-
-  // Make sure no checkin is in progress.
-  if (checkin_request_)
-    return;
-
-  checkin_proto::ChromeBuildProto chrome_build_proto;
-  ToCheckinProtoVersion(chrome_build_info_, &chrome_build_proto);
-
-  CheckinRequest::RequestInfo request_info(
-      device_checkin_info_.android_id, device_checkin_info_.secret,
-      gservices_settings_.digest(), chrome_build_proto);
-  checkin_request_ = std::make_unique<CheckinRequest>(
-      gservices_settings_.GetCheckinURL(), request_info, GetGCMBackoffPolicy(),
-      base::BindOnce(&GCMClientImpl::OnCheckinCompleted,
-                     weak_ptr_factory_.GetWeakPtr()),
-      url_loader_factory_, io_task_runner_, &recorder_);
-  checkin_request_->Start();
 }
 
 void GCMClientImpl::OnCheckinCompleted(
@@ -683,24 +627,6 @@ void GCMClientImpl::SetGServicesSettings
 
 void GCMClientImpl::SchedulePeriodicCheckin() {
   DCHECK(io_task_runner_->RunsTasksInCurrentSequence());
-
-  // Make sure no checkin is in progress.
-  if (checkin_request_.get() || !device_checkin_info_.accounts_set)
-    return;
-
-  // There should be only one periodic checkin pending at a time. Removing
-  // pending periodic checkin to schedule a new one.
-  periodic_checkin_ptr_factory_.InvalidateWeakPtrs();
-
-  base::TimeDelta time_to_next_checkin = GetTimeToNextCheckin();
-  if (time_to_next_checkin.is_negative())
-    time_to_next_checkin = base::TimeDelta();
-
-  io_task_runner_->PostDelayedTask(
-      FROM_HERE,
-      base::BindOnce(&GCMClientImpl::StartCheckin,
-                     periodic_checkin_ptr_factory_.GetWeakPtr()),
-      time_to_next_checkin);
 }
 
 base::TimeDelta GCMClientImpl::GetTimeToNextCheckin() const {
@@ -1124,25 +1050,6 @@ void GCMClientImpl::Send(const std::stri
                          const OutgoingMessage& message) {
   DCHECK_EQ(state_, READY);
   DCHECK(io_task_runner_->RunsTasksInCurrentSequence());
-
-  mcs_proto::DataMessageStanza stanza;
-  stanza.set_ttl(message.time_to_live);
-  stanza.set_sent(clock_->Now().ToInternalValue() /
-                  base::Time::kMicrosecondsPerSecond);
-  stanza.set_id(message.id);
-  stanza.set_from(kSendMessageFromValue);
-  stanza.set_to(receiver_id);
-  stanza.set_category(app_id);
-
-  for (auto iter = message.data.begin(); iter != message.data.end(); ++iter) {
-    mcs_proto::AppData* app_data = stanza.add_app_data();
-    app_data->set_key(iter->first);
-    app_data->set_value(iter->second);
-  }
-
-  MCSMessage mcs_message(stanza);
-  DVLOG(1) << "MCS message size: " << mcs_message.size();
-  mcs_client_->SendMessage(mcs_message);
 }
 
 std::string GCMClientImpl::GetStateString() const {
