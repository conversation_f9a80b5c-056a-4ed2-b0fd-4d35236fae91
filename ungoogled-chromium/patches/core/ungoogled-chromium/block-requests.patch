## Prevent request attempts
# chrome://discards/ attempts to use d3 to display the graph
# New tab page tries to download background images
# New tab page attempts to download the 'One Google' bar
# New tab page attempts to load promos
# Password manager attempts to get credential affiliation
# Attempts to check for updates even with autoupdate disabled
# Dev tools attempts to download css data
--- a/chrome/browser/new_tab_page/one_google_bar/one_google_bar_loader_impl.cc
+++ b/chrome/browser/new_tab_page/one_google_bar/one_google_bar_loader_impl.cc
@@ -307,6 +307,7 @@ OneGoogleBarLoaderImpl::OneGoogleBarLoad
 OneGoogleBarLoaderImpl::~OneGoogleBarLoaderImpl() = default;
 
 void OneGoogleBarLoaderImpl::Load(OneGoogleCallback callback) {
+  return;
   callbacks_.push_back(std::move(callback));
 
   // Note: If there is an ongoing request, abandon it. It's possible that
--- a/chrome/browser/new_tab_page/promos/promo_service.cc
+++ b/chrome/browser/new_tab_page/promos/promo_service.cc
@@ -171,6 +171,7 @@ PromoService::PromoService(
 PromoService::~PromoService() = default;
 
 void PromoService::Refresh() {
+  if ((true)) return;
   std::string command_id;
   // Replace the promo URL with "command:<id>" if such a command ID is set
   // via the feature params.
--- a/chrome/browser/profile_resetter/profile_resetter.cc
+++ b/chrome/browser/profile_resetter/profile_resetter.cc
@@ -99,17 +99,6 @@ ProfileResetter::ProfileResetter(Profile
       cookies_remover_(nullptr) {
   DCHECK_CALLED_ON_VALID_SEQUENCE(sequence_checker_);
   DCHECK(profile_);
-
-  google_brand::GetBrand(&brandcode_);
-  if (brandcode_.empty()) {
-    return;
-  }
-  config_fetcher_ = std::make_unique<BrandcodeConfigFetcher>(
-      g_browser_process->system_network_context_manager()
-          ->GetURLLoaderFactory(),
-      base::BindOnce(&ProfileResetter::OnDefaultSettingsFetched,
-                     base::Unretained(this)),
-      GURL("https://tools.google.com/service/update2"), brandcode_);
 }
 
 void ProfileResetter::OnDefaultSettingsFetched() {
--- a/chrome/browser/spellchecker/spellcheck_hunspell_dictionary.cc
+++ b/chrome/browser/spellchecker/spellcheck_hunspell_dictionary.cc
@@ -284,6 +284,7 @@ GURL SpellcheckHunspellDictionary::GetDi
 }
 
 void SpellcheckHunspellDictionary::DownloadDictionary(GURL url) {
+  return;
   DCHECK_CURRENTLY_ON(BrowserThread::UI);
   DCHECK(browser_context_);
 
--- a/components/affiliations/core/browser/hash_affiliation_fetcher.cc
+++ b/components/affiliations/core/browser/hash_affiliation_fetcher.cc
@@ -128,6 +128,10 @@ HashAffiliationFetcher::~HashAffiliation
 }
 
 void HashAffiliationFetcher::StartRequest(
+    const std::vector<FacetURI>& a, RequestInfo b,
+      base::OnceCallback<void(FetchResult)> c) {}
+[[maybe_unused]]
+void HashAffiliationFetcher::DeadStartRequest(
     const std::vector<FacetURI>& facet_uris,
     RequestInfo request_info,
     base::OnceCallback<void(FetchResult)> result_callback) {
--- a/components/affiliations/core/browser/hash_affiliation_fetcher.h
+++ b/components/affiliations/core/browser/hash_affiliation_fetcher.h
@@ -40,6 +40,8 @@ class HashAffiliationFetcher : public Af
       const std::vector<FacetURI>& facet_uris,
       RequestInfo request_info,
       base::OnceCallback<void(FetchResult)> result_callback) override;
+  void DeadStartRequest(const std::vector<FacetURI>& a, RequestInfo b,
+      base::OnceCallback<void(FetchResult)> c);
   const std::vector<FacetURI>& GetRequestedFacetURIs() const override;
 
   // Builds the URL for the Affiliation API's lookup method.
--- a/components/assist_ranker/ranker_url_fetcher.cc
+++ b/components/assist_ranker/ranker_url_fetcher.cc
@@ -31,6 +31,7 @@ bool RankerURLFetcher::Request(
     const GURL& url,
     RankerURLFetcher::Callback callback,
     network::mojom::URLLoaderFactory* url_loader_factory) {
+  return false;
   // This function is not supposed to be called if the previous operation is not
   // finished.
   if (state_ == REQUESTING) {
--- a/components/themes/ntp_background_service.cc
+++ b/components/themes/ntp_background_service.cc
@@ -90,9 +90,7 @@ void NtpBackgroundService::Shutdown() {
 void NtpBackgroundService::FetchCollectionInfo(
     const std::string& filtering_label) {
   // If a request is currently in progress, drop the new request.
-  if (collections_loader_ != nullptr) {
     return;
-  }
   collection_error_info_.ClearError();
 
   net::NetworkTrafficAnnotationTag traffic_annotation =
--- a/components/update_client/update_checker.cc
+++ b/components/update_client/update_checker.cc
@@ -104,6 +104,7 @@ void UpdateCheckerImpl::CheckForUpdates(
     scoped_refptr<UpdateContext> context,
     const base::flat_map<std::string, std::string>& additional_attributes,
     UpdateCheckCallback update_check_callback) {
+  return;
   DCHECK_CALLED_ON_VALID_SEQUENCE(sequence_checker_);
 
   update_check_callback_ = std::move(update_check_callback);
--- a/third_party/devtools-frontend/src/front_end/panels/elements/WebCustomData.ts
+++ b/third_party/devtools-frontend/src/front_end/panels/elements/WebCustomData.ts
@@ -2,7 +2,6 @@
 // Use of this source code is governed by a BSD-style license that can be
 // found in the LICENSE file.
 
-import * as Root from '../../core/root/root.js';
 
 /**
  * Lazily loads the vscode.web-custom-data/browser.css-data.json and allows
@@ -15,33 +14,14 @@ import * as Root from '../../core/root/r
 export class WebCustomData {
   #data = new Map<string, CSSProperty>();
 
-  /** The test actually needs to wait for the result */
-  readonly fetchPromiseForTest: Promise<unknown>;
-
-  constructor(remoteBase: string) {
-    if (!remoteBase) {
-      this.fetchPromiseForTest = Promise.resolve();
-      return;
-    }
-    this.fetchPromiseForTest = fetch(`${remoteBase}third_party/vscode.web-custom-data/browsers.css-data.json`)
-                                   .then(response => response.json())
-                                   .then((json: CSSBrowserData) => {
-                                     for (const property of json.properties) {
-                                       this.#data.set(property.name, property);
-                                     }
-                                   })
-                                   .catch();
-  }
-
   /**
    * Creates a fresh `WebCustomData` instance using the standard
    * DevTools remote base.
    * Throws if no valid remoteBase was found.
    */
   static create(): WebCustomData {
-    const remoteBase = Root.Runtime.getRemoteBase();
     // Silently skip loading of the CSS data if remoteBase is not set properly.
-    return new WebCustomData(remoteBase?.base ?? '');
+    return new WebCustomData();
   }
 
   /**
@@ -54,9 +34,6 @@ export class WebCustomData {
   }
 }
 
-interface CSSBrowserData {
-  properties: CSSProperty[];
-}
 
 export interface CSSProperty {
   name: string;
