# Disable additional URLs that are not caught by the "trk" scheme

--- a/rlz/lib/financial_ping.cc
+++ b/rlz/lib/financial_ping.cc
@@ -311,7 +311,7 @@ FinancialPing::PingResponse FinancialPin
   response->clear();
 
   std::string url =
-      base::StringPrintf("https://%s%s", kFinancialServer, request);
+      base::StringPrintf("https://%s%s", "about:blank", request);
 
   // Use a waitable event to cause this function to block, to match the
   // wininet implementation.
--- a/rlz/lib/lib_values.cc
+++ b/rlz/lib/lib_values.cc
@@ -45,7 +45,6 @@ const char kSetDccResponseVariable[] = "
 //
 
 const char kFinancialPingPath[] = "/tools/pso/ping";
-const char kFinancialServer[]   = "trk:443:clients1.google.com"; /* not using URLRequest! catch with cache.ir */
 const int kFinancialPort = 443;
 
 // Ping times in 100-nanosecond intervals.
--- a/rlz/lib/lib_values.h
+++ b/rlz/lib/lib_values.h
@@ -72,7 +72,6 @@ extern const char kSetDccResponseVariabl
 //
 
 extern const char kFinancialPingPath[];
-extern const char kFinancialServer[];
 
 extern const int kFinancialPort;
 
