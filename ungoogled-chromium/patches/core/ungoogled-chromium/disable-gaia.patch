# Disables Gaia code
# Somehow it is still activated even without being signed-in: https://github.com/ungoogled-software/ungoogled-chromium/issues/104

--- a/google_apis/gaia/gaia_auth_fetcher.cc
+++ b/google_apis/gaia/gaia_auth_fetcher.cc
@@ -232,61 +232,6 @@ void GaiaAuthFetcher::CreateAndStartGaia
     network::mojom::CredentialsMode credentials_mode,
     const net::NetworkTrafficAnnotationTag& traffic_annotation) {
   DCHECK(!fetch_pending_) << "Tried to fetch two things at once!";
-
-  auto resource_request = std::make_unique<network::ResourceRequest>();
-  resource_request->url = gaia_gurl;
-  original_url_ = gaia_gurl;
-
-  if (credentials_mode != network::mojom::CredentialsMode::kOmit &&
-      credentials_mode !=
-          network::mojom::CredentialsMode::kOmitBug_775438_Workaround) {
-    CHECK(gaia::HasGaiaSchemeHostPort(gaia_gurl)) << gaia_gurl;
-
-    url::Origin origin = GaiaUrls::GetInstance()->gaia_origin();
-    resource_request->site_for_cookies =
-        net::SiteForCookies::FromOrigin(origin);
-    resource_request->trusted_params =
-        network::ResourceRequest::TrustedParams();
-    resource_request->trusted_params->isolation_info =
-        net::IsolationInfo::CreateForInternalRequest(origin);
-  }
-
-  if (!body.empty())
-    resource_request->method = "POST";
-
-  resource_request->headers = request_headers;
-
-  resource_request->credentials_mode = credentials_mode;
-
-  url_loader_ = network::SimpleURLLoader::Create(std::move(resource_request),
-                                                 traffic_annotation);
-  if (!body.empty()) {
-    DCHECK(!body_content_type.empty());
-    url_loader_->AttachStringForUpload(body, body_content_type);
-  }
-
-  url_loader_->SetAllowHttpErrorResults(true);
-
-  VLOG(2) << "Gaia fetcher URL: " << gaia_gurl.spec();
-  VLOG(2) << "Gaia fetcher headers: " << request_headers.ToString();
-  VLOG(2) << "Gaia fetcher body: " << body;
-
-  // Fetchers are sometimes cancelled because a network change was detected,
-  // especially at startup and after sign-in on ChromeOS. Retrying once should
-  // be enough in those cases; let the fetcher retry up to 3 times just in case.
-  // http://crbug.com/163710
-  url_loader_->SetRetryOptions(
-      3, network::SimpleURLLoader::RETRY_ON_NETWORK_CHANGE);
-
-  fetch_pending_ = true;
-
-  // Unretained is OK below as |url_loader_| is owned by this.
-  url_loader_->DownloadToString(
-      url_loader_factory_.get(),
-      base::BindOnce(&GaiaAuthFetcher::OnURLLoadComplete,
-                     base::Unretained(this)),
-      // Limit to 1 MiB.
-      1024 * 1024);
 }
 
 // static
