# Split up the learn.doubleclick.net string literal to prevent domain substitution breaking compilation due to the use of the excluded HSTS list json file in this code.

--- a/net/tools/transport_security_state_generator/transport_security_state_generator.cc
+++ b/net/tools/transport_security_state_generator/transport_security_state_generator.cc
@@ -129,7 +129,7 @@ bool CheckDuplicateEntries(const Transpo
 bool CheckNoopEntries(const TransportSecurityStateEntries& entries) {
   for (const auto& entry : entries) {
     if (!entry->force_https && entry->pinset.empty()) {
-      if (entry->hostname == "learn.doubleclick.net") {
+      if (entry->hostname == "learn.double" "click." "net") {
         // This entry is deliberately used as an exclusion.
         continue;
       }
