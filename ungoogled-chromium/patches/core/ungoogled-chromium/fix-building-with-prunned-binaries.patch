# Contains tests and features that are unneeded and would otherwise
# require binaries to be whitelisted in the pruning list

--- a/chrome/BUILD.gn
+++ b/chrome/BUILD.gn
@@ -368,7 +368,6 @@ if (!is_android && !is_mac) {
 
     data_deps += [
       "//chrome/browser/resources/media/mei_preload:component",
-      "//components/privacy_sandbox/privacy_sandbox_attestations/preload:component",
       "//components/webapps/isolated_web_apps/preload:component",
       "//third_party/widevine/cdm",
     ]
@@ -937,8 +936,6 @@ if (is_win) {
 
   bundle_data("chrome_framework_resources") {
     sources = [
-      "//ui/gl/resources/angle-metal/gpu_shader_cache.bin",
-
       # This image is used to badge the lock icon in the
       # authentication dialogs, such as those used for installation
       # from disk image and Keystone promotion (if so enabled).  It
@@ -1185,7 +1182,6 @@ if (is_win) {
       ":swiftshader_binaries",
       ":widevine_cdm_library",
       "//chrome/browser/resources/media/mei_preload:component_bundle",
-      "//components/privacy_sandbox/privacy_sandbox_attestations/preload:component_bundle",
       "//components/webapps/isolated_web_apps/preload:component_bundle",
     ]
 
--- a/chrome/browser/BUILD.gn
+++ b/chrome/browser/BUILD.gn
@@ -797,10 +797,6 @@ static_library("browser") {
     "navigation_predictor/navigation_predictor_metrics_document_data.h",
     "navigation_predictor/navigation_predictor_preconnect_client.cc",
     "navigation_predictor/navigation_predictor_preconnect_client.h",
-    "navigation_predictor/preloading_model_keyed_service.cc",
-    "navigation_predictor/preloading_model_keyed_service.h",
-    "navigation_predictor/preloading_model_keyed_service_factory.cc",
-    "navigation_predictor/preloading_model_keyed_service_factory.h",
     "navigation_predictor/search_engine_preconnector_keyed_service_factory.cc",
     "navigation_predictor/search_engine_preconnector_keyed_service_factory.h",
     "net/cert_verifier_service_time_updater.cc",
@@ -3678,10 +3674,6 @@ static_library("browser") {
   } else {
     #!is_android
     sources += [
-      "accessibility/ax_main_node_annotator_controller.cc",
-      "accessibility/ax_main_node_annotator_controller.h",
-      "accessibility/ax_main_node_annotator_controller_factory.cc",
-      "accessibility/ax_main_node_annotator_controller_factory.h",
       "accessibility/caption_bubble_context_browser.h",
       "accessibility/embedded_a11y_extension_loader.cc",
       "accessibility/embedded_a11y_extension_loader.h",
@@ -4341,8 +4333,6 @@ static_library("browser") {
       "//chrome/app:command_ids",
       "//chrome/app/theme:chrome_unscaled_resources_grit",
       "//chrome/app/vector_icons",
-      "//chrome/browser/accessibility/tree_fixing:prefs",
-      "//chrome/browser/accessibility/tree_fixing:service",
       "//chrome/browser/actor",
       "//chrome/browser/actor:impl",
       "//chrome/browser/actor/ui",
@@ -4369,10 +4359,6 @@ static_library("browser") {
       "//chrome/browser/performance_manager/user_tuning",
       "//chrome/browser/policy:path_parser",
       "//chrome/browser/resource_coordinator",
-      "//chrome/browser/screen_ai:prefs",
-      "//chrome/browser/screen_ai:screen_ai_install_state",
-      "//chrome/browser/screen_ai:screen_ai_service_router_factory",
-      "//chrome/browser/screen_ai/public:optical_character_recognizer",
       "//chrome/browser/search/background",
       "//chrome/browser/sharing_hub",
       "//chrome/browser/smart_card",
@@ -4416,7 +4402,6 @@ static_library("browser") {
       "//chrome/browser/ui/webui/access_code_cast",
       "//chrome/browser/ui/webui/actor_internals",
       "//chrome/browser/ui/webui/app_service_internals",
-      "//chrome/browser/ui/webui/autofill_ml_internals",
       "//chrome/browser/ui/webui/color_pipeline_internals",
       "//chrome/browser/ui/webui/infobar_internals",
       "//chrome/browser/ui/webui/infobar_internals:impl",
@@ -4536,9 +4521,6 @@ static_library("browser") {
       "//components/webauthn/core/browser",
       "//components/webauthn/core/browser:passkey_model",
       "//services/device/public/cpp/hid",
-      "//services/screen_ai",
-      "//services/screen_ai/public/cpp:utilities",
-      "//services/screen_ai/public/mojom",
       "//third_party/crashpad/crashpad/client:common",
       "//third_party/zxcvbn-cpp",
       "//ui/views",
@@ -4644,8 +4626,6 @@ static_library("browser") {
       sources += [
         "accessibility/soda_installer_impl.cc",
         "accessibility/soda_installer_impl.h",
-        "component_updater/screen_ai_component_installer.cc",
-        "component_updater/screen_ai_component_installer.h",
         "device_identity/device_oauth2_token_store_desktop.cc",
         "device_identity/device_oauth2_token_store_desktop.h",
         "device_notifications/device_status_icon_renderer.cc",
@@ -4701,8 +4681,6 @@ static_library("browser") {
         "profiles/profile_activity_metrics_recorder.h",
         "profiles/profile_list_desktop.cc",
         "profiles/profile_list_desktop.h",
-        "screen_ai/screen_ai_downloader_non_chromeos.cc",
-        "screen_ai/screen_ai_downloader_non_chromeos.h",
         "signin/force_signin_verifier.cc",
         "signin/force_signin_verifier.h",
         "signin/signin_manager.cc",
@@ -5511,7 +5489,6 @@ static_library("browser") {
       "//chrome/browser/policy:system_features_disable_list",
       "//chrome/browser/push_notification",
       "//chrome/browser/resources:app_icon_resources",
-      "//chrome/browser/screen_ai:screen_ai_dlc_installer",
       "//chrome/browser/sharesheet",
       "//chrome/browser/support_tool/ash",
       "//chrome/browser/ui/ash/accelerator",
@@ -5775,7 +5752,6 @@ static_library("browser") {
       "//remoting/host/chromeos:browser_interop",
       "//remoting/host/chromeos:features",
       "//services/device/public/cpp/geolocation",
-      "//services/screen_ai/public/cpp:metrics",
       "//third_party/nearby:rpc_resources_proto",
       "//third_party/nearby:wire_format_proto",
       "//ui/chromeos",
--- a/chrome/browser/accessibility/tree_fixing/BUILD.gn
+++ b/chrome/browser/accessibility/tree_fixing/BUILD.gn
@@ -63,11 +63,9 @@ source_set("internal") {
   deps = [
     "//base",
     "//chrome/browser/profiles:profile",
-    "//chrome/browser/screen_ai:screen_ai_service_router_factory",
     "//components/paint_preview/browser",
     "//components/paint_preview/common",
     "//components/paint_preview/public",
     "//content/public/browser",
-    "//services/screen_ai/public/mojom",
   ]
 }
--- a/chrome/browser/browser_process_impl.cc
+++ b/chrome/browser/browser_process_impl.cc
@@ -247,12 +247,10 @@
 #if BUILDFLAG(IS_CHROMEOS)
 #include "chrome/browser/chromeos/extensions/telemetry/chromeos_telemetry_extensions_browser_api_provider.h"
 #include "chrome/browser/hid/hid_pinned_notification.h"
-#include "chrome/browser/screen_ai/screen_ai_downloader_chromeos.h"
 #include "chrome/browser/usb/usb_pinned_notification.h"
 #include "components/crash/core/app/crashpad.h"
 #elif !BUILDFLAG(IS_ANDROID)
 #include "chrome/browser/hid/hid_status_icon.h"
-#include "chrome/browser/screen_ai/screen_ai_downloader_non_chromeos.h"
 #include "chrome/browser/usb/usb_status_icon.h"
 #include "components/enterprise/browser/controller/chrome_browser_cloud_management_controller.h"
 #endif
@@ -1384,9 +1382,6 @@ void BrowserProcessImpl::PreMainMessageL
   soda_installer_impl_ = std::make_unique<speech::SodaInstallerImplChromeOS>();
 #endif  // BUILDFLAG(IS_CHROMEOS)
 
-#if !BUILDFLAG(IS_ANDROID)
-  screen_ai_download_ = screen_ai::ScreenAIInstallState::Create();
-#endif
 
   base::FilePath user_data_dir;
   bool result = base::PathService::Get(chrome::DIR_USER_DATA, &user_data_dir);
--- a/chrome/browser/browser_process_impl.h
+++ b/chrome/browser/browser_process_impl.h
@@ -90,9 +90,6 @@ namespace speech {
 class SodaInstaller;
 }  // namespace speech
 
-namespace screen_ai {
-class ScreenAIInstallState;
-}  // namespace screen_ai
 
 // Real implementation of BrowserProcess that creates and returns the services.
 class BrowserProcessImpl : public BrowserProcess,
@@ -437,9 +434,6 @@ class BrowserProcessImpl : public Browse
   // to ensure that SodaInstallerImpl gets destructed first.
   std::unique_ptr<speech::SodaInstaller> soda_installer_impl_;
 
-  // Used to download Screen AI on demand and keep track of the library
-  // availability.
-  std::unique_ptr<screen_ai::ScreenAIInstallState> screen_ai_download_;
 #endif
 
   std::unique_ptr<BrowserProcessPlatformPart> platform_part_;
--- a/chrome/browser/chrome_browser_interface_binders.cc
+++ b/chrome/browser/chrome_browser_interface_binders.cc
@@ -83,8 +83,6 @@
 
 #if BUILDFLAG(IS_WIN) || BUILDFLAG(IS_MAC) || BUILDFLAG(IS_LINUX) || \
     BUILDFLAG(IS_CHROMEOS)
-#include "chrome/browser/screen_ai/screen_ai_service_router.h"
-#include "chrome/browser/screen_ai/screen_ai_service_router_factory.h"
 #include "chrome/browser/ui/web_applications/sub_apps_service_impl.h"
 #endif
 
@@ -364,27 +362,6 @@ void BindMediaFoundationPreferences(
 }
 #endif  // BUILDFLAG(IS_WIN)
 
-#if BUILDFLAG(IS_CHROMEOS) || BUILDFLAG(IS_LINUX) || BUILDFLAG(IS_MAC) || \
-    BUILDFLAG(IS_WIN)
-void BindScreenAIAnnotator(
-    content::RenderFrameHost* frame_host,
-    mojo::PendingReceiver<screen_ai::mojom::ScreenAIAnnotator> receiver) {
-  content::BrowserContext* browser_context =
-      frame_host->GetProcess()->GetBrowserContext();
-
-  screen_ai::ScreenAIServiceRouterFactory::GetForBrowserContext(browser_context)
-      ->BindScreenAIAnnotator(std::move(receiver));
-}
-
-void BindScreen2xMainContentExtractor(
-    content::RenderFrameHost* frame_host,
-    mojo::PendingReceiver<screen_ai::mojom::Screen2xMainContentExtractor>
-        receiver) {
-  screen_ai::ScreenAIServiceRouterFactory::GetForBrowserContext(
-      frame_host->GetProcess()->GetBrowserContext())
-      ->BindMainContentExtractor(std::move(receiver));
-}
-#endif
 
 void BindModelBroker(
     content::RenderFrameHost* frame_host,
@@ -554,10 +531,6 @@ void PopulateChromeFrameBinders(
         &web_app::SubAppsServiceImpl::CreateIfAllowed);
   }
 
-  map->Add<screen_ai::mojom::ScreenAIAnnotator>(&BindScreenAIAnnotator);
-
-  map->Add<screen_ai::mojom::Screen2xMainContentExtractor>(
-      &BindScreen2xMainContentExtractor);
 #endif
 
 #if BUILDFLAG(IS_WIN)
--- a/chrome/browser/chrome_browser_interface_binders_webui.cc
+++ b/chrome/browser/chrome_browser_interface_binders_webui.cc
@@ -803,8 +803,6 @@ void PopulateChromeWebUIFrameBinders(
       ::mojom::app_service_internals::AppServiceInternalsPageHandler,
       AppServiceInternalsUI>(map);
 
-  RegisterWebUIControllerInterfaceBinder<
-      ::autofill_ml_internals::mojom::PageHandler, AutofillMlInternalsUI>(map);
 
   RegisterWebUIControllerInterfaceBinder<
       access_code_cast::mojom::PageHandlerFactory,
--- a/chrome/browser/component_updater/registration.cc
+++ b/chrome/browser/component_updater/registration.cc
@@ -64,8 +64,6 @@
 
 #if BUILDFLAG(IS_ANDROID)
 #include "chrome/browser/component_updater/real_time_url_checks_allowlist_component_installer.h"
-#else
-#include "chrome/browser/component_updater/screen_ai_component_installer.h"
 #endif  // BUILDFLAG(IS_ANDROID)
 
 #if !BUILDFLAG(IS_ANDROID)
@@ -221,9 +219,6 @@ void RegisterComponentsForUpdate() {
 
   RegisterAutofillStatesComponent(cus, g_browser_process->local_state());
 
-#if !BUILDFLAG(IS_ANDROID) && !BUILDFLAG(IS_CHROMEOS)
-  ManageScreenAIComponentRegistration(cus, g_browser_process->local_state());
-#endif  // !BUILDFLAG(IS_ANDROID) && !BUILDFLAG(IS_CHROMEOS)
 
   RegisterCommerceHeuristicsComponent(cus);
 
--- a/chrome/browser/navigation_predictor/navigation_predictor.cc
+++ b/chrome/browser/navigation_predictor/navigation_predictor.cc
@@ -17,8 +17,6 @@
 #include "base/time/default_tick_clock.h"
 #include "chrome/browser/navigation_predictor/navigation_predictor_keyed_service.h"
 #include "chrome/browser/navigation_predictor/navigation_predictor_keyed_service_factory.h"
-#include "chrome/browser/navigation_predictor/preloading_model_keyed_service.h"
-#include "chrome/browser/navigation_predictor/preloading_model_keyed_service_factory.h"
 #include "chrome/browser/preloading/preloading_prefs.h"
 #include "chrome/browser/profiles/profile.h"
 #include "components/no_state_prefetch/browser/no_state_prefetch_manager.h"
@@ -105,10 +103,6 @@ base::TimeDelta MLModelExecutionTimerSta
       blink::features::kPreloadingModelTimerStartDelay.Get());
 }
 
-base::TimeDelta MLModelExecutionTimerInterval() {
-  return base::Milliseconds(
-      blink::features::kPreloadingModelTimerInterval.Get());
-}
 
 base::TimeDelta MLModelMaxHoverTime() {
   return blink::features::kPreloadingModelMaxHoverTime.Get();
@@ -415,80 +409,6 @@ void NavigationPredictor::ProcessPointer
 }
 
 void NavigationPredictor::OnMLModelExecutionTimerFired() {
-  // Check whether preloading is enabled or not.
-  Profile* profile =
-      Profile::FromBrowserContext(render_frame_host().GetBrowserContext());
-  if (prefetch::IsSomePreloadingEnabled(*profile->GetPrefs()) !=
-      content::PreloadingEligibility::kEligible) {
-    return;
-  }
-
-  // Execute the model.
-  PreloadingModelKeyedService* model_service =
-      PreloadingModelKeyedServiceFactory::GetForProfile(profile);
-  if (!model_service) {
-    return;
-  }
-
-  if (!ml_model_candidate_.has_value()) {
-    return;
-  }
-  auto it = anchors_.find(ml_model_candidate_.value());
-  if (it == anchors_.end()) {
-    return;
-  }
-
-  AnchorElementData& anchor = it->second;
-
-  PreloadingModelKeyedService::Inputs inputs;
-  inputs.contains_image = anchor.contains_image;
-  inputs.font_size = anchor.font_size;
-  inputs.has_text_sibling = anchor.has_text_sibling;
-  inputs.is_bold = anchor.is_bold_font;
-  inputs.is_in_iframe = anchor.is_in_iframe;
-  inputs.is_url_incremented_by_one = anchor.is_url_incremented_by_one;
-  inputs.navigation_start_to_link_logged =
-      anchor.first_report_timestamp - navigation_start_;
-  auto path_info = GetUrlPathLengthDepthAndHash(anchor.target_url);
-  inputs.path_length = path_info.path_length;
-  inputs.path_depth = path_info.path_depth;
-  inputs.percent_clickable_area = anchor.ratio_area;
-  inputs.percent_vertical_distance =
-      static_cast<int>(anchor.ratio_distance_root_top * 100);
-
-  inputs.is_same_host = anchor.is_same_host;
-  auto to_timedelta = [this](std::optional<base::TimeTicks> ts) {
-    return ts.has_value() ? NowTicks() - ts.value() : base::TimeDelta();
-  };
-  // TODO(329691634): Using the real viewport entry time for
-  // `entered_viewport_to_left_viewport` produces low quality results.
-  // We could remove it from the model, if we can't get this to be useful.
-  inputs.entered_viewport_to_left_viewport = base::TimeDelta();
-  inputs.hover_dwell_time = to_timedelta(anchor.pointer_over_timestamp);
-  inputs.pointer_hovering_over_count = anchor.pointer_hovering_over_count;
-  if (model_score_callback_) {
-    std::move(model_score_callback_).Run(inputs);
-  }
-
-  content::PreloadingData* preloading_data =
-      content::PreloadingData::GetOrCreateForWebContents(
-          content::WebContents::FromRenderFrameHost(&render_frame_host()));
-  preloading_data->OnPreloadingHeuristicsModelInput(
-      anchor.target_url,
-      base::BindOnce(&RecordMetricsForModelTraining, inputs,
-                     render_frame_host().GetPageUkmSourceId()));
-  model_service->Score(
-      &scoring_model_task_tracker_, inputs,
-      base::BindOnce(&NavigationPredictor::OnPreloadingHeuristicsModelDone,
-                     weak_ptr_factory_.GetWeakPtr(), anchor.target_url));
-
-  if (inputs.hover_dwell_time < MLModelMaxHoverTime() &&
-      !ml_model_execution_timer_.IsRunning()) {
-    ml_model_execution_timer_.Start(
-        FROM_HERE, MLModelExecutionTimerInterval(),
-        base::BindOnce(&NavigationPredictor::OnMLModelExecutionTimerFired,
-                       base::Unretained(this)));
-  }
 }
 
 void NavigationPredictor::SetModelScoreCallbackForTesting(
--- a/chrome/browser/pdf/pdf_extension_util.cc
+++ b/chrome/browser/pdf/pdf_extension_util.cc
@@ -110,7 +110,6 @@ void AddPdfViewerStrings(base::Value::Di
       {"rotationStateLabel90", IDS_PDF_ROTATION_STATE_LABEL_90},
       {"rotationStateLabel180", IDS_PDF_ROTATION_STATE_LABEL_180},
       {"rotationStateLabel270", IDS_PDF_ROTATION_STATE_LABEL_270},
-      {"searchifyInProgress", IDS_PDF_SEARCHIFY_IN_PROGRESS},
       {"thumbnailPageAriaLabel", IDS_PDF_THUMBNAIL_PAGE_ARIA_LABEL},
       {"tooltipAttachments", IDS_PDF_TOOLTIP_ATTACHMENTS},
       {"tooltipDocumentOutline", IDS_PDF_TOOLTIP_DOCUMENT_OUTLINE},
--- a/chrome/browser/permissions/BUILD.gn
+++ b/chrome/browser/permissions/BUILD.gn
@@ -44,10 +44,6 @@ source_set("permissions") {
     "prediction_service/passage_embedder_delegate.h",
     "prediction_service/permissions_aiv1_handler.cc",
     "prediction_service/permissions_aiv1_handler.h",
-    "prediction_service/prediction_based_permission_ui_selector.cc",
-    "prediction_service/prediction_based_permission_ui_selector.h",
-    "prediction_service/prediction_model_handler_provider.cc",
-    "prediction_service/prediction_model_handler_provider.h",
     "prediction_service/prediction_service_factory.cc",
     "prediction_service/prediction_service_factory.h",
     "prediction_service/prediction_service_request.cc",
--- a/chrome/browser/permissions/chrome_permissions_client.cc
+++ b/chrome/browser/permissions/chrome_permissions_client.cc
@@ -475,8 +475,6 @@ ChromePermissionsClient::CreatePermissio
 #endif
   selectors.emplace_back(std::make_unique<PrefBasedQuietPermissionUiSelector>(
       Profile::FromBrowserContext(browser_context)));
-  selectors.emplace_back(std::make_unique<PredictionBasedPermissionUiSelector>(
-      Profile::FromBrowserContext(browser_context)));
   return selectors;
 }
 
--- a/chrome/browser/permissions/prediction_service/passage_embedder_delegate.cc
+++ b/chrome/browser/permissions/prediction_service/passage_embedder_delegate.cc
@@ -17,11 +17,6 @@ PassageEmbedderDelegate::PassageEmbedder
     : profile_(profile) {}
 
 Embedder* PassageEmbedderDelegate::get_passage_embedder() {
-  if (auto* prediction_model_handler_provider =
-          PredictionModelHandlerProviderFactory::GetForBrowserContext(
-              profile_)) {
-    return prediction_model_handler_provider->GetPassageEmbedder();
-  }
   return nullptr;
 }
 
--- a/chrome/browser/permissions/prediction_service/prediction_based_permission_ui_selector.cc
+++ b/chrome/browser/permissions/prediction_service/prediction_based_permission_ui_selector.cc
@@ -836,7 +836,7 @@ void PredictionBasedPermissionUiSelector
     ModelExecutionData model_data) {
   VLOG(1) << "[PermissionsAI] ExecuteOnDeviceAivXModel";
   PredictionModelHandlerProvider* prediction_model_handler_provider =
-      PredictionModelHandlerProviderFactory::GetForBrowserContext(profile_);
+      nullptr;
   if (prediction_model_handler_provider) {
     permissions::RequestType request_type =
         model_data.request_metadata.request_type;
--- a/chrome/browser/prefs/BUILD.gn
+++ b/chrome/browser/prefs/BUILD.gn
@@ -297,7 +297,6 @@ source_set("impl") {
       "//chrome/browser/actor/ui",
       "//chrome/browser/contextual_cueing",
       "//chrome/browser/promos:utils",
-      "//chrome/browser/screen_ai:prefs",
       "//chrome/browser/search/background",
       "//chrome/browser/search_engine_choice",
       "//chrome/browser/search_engines",
--- a/chrome/browser/prefs/browser_prefs.cc
+++ b/chrome/browser/prefs/browser_prefs.cc
@@ -1830,9 +1830,6 @@ void RegisterLocalState(PrefRegistrySimp
   DeviceOAuth2TokenStoreDesktop::RegisterPrefs(registry);
 #endif
 
-#if !BUILDFLAG(IS_ANDROID)
-  screen_ai::RegisterLocalStatePrefs(registry);
-#endif  // !BUILDFLAG(IS_ANDROID)
 
 #if BUILDFLAG(IS_WIN) || BUILDFLAG(IS_MAC)
   PlatformAuthPolicyObserver::RegisterPrefs(registry);
--- a/chrome/browser/profiles/BUILD.gn
+++ b/chrome/browser/profiles/BUILD.gn
@@ -320,7 +320,6 @@ source_set("profiles_extra_parts_impl")
     ]
   } else {
     deps += [
-      "//chrome/browser/accessibility/tree_fixing:service",
       "//chrome/browser/apps/app_service",
       "//chrome/browser/autofill",
       "//chrome/browser/contextual_cueing",
@@ -328,7 +327,6 @@ source_set("profiles_extra_parts_impl")
       "//chrome/browser/feedback",
       "//chrome/browser/hid",
       "//chrome/browser/media/router/discovery/access_code:access_code_sink_service",
-      "//chrome/browser/screen_ai:screen_ai_service_router_factory",
       "//chrome/browser/search",
       "//chrome/browser/search/background",
       "//chrome/browser/search_engine_choice",
--- a/chrome/browser/profiles/chrome_browser_main_extra_parts_profiles.cc
+++ b/chrome/browser/profiles/chrome_browser_main_extra_parts_profiles.cc
@@ -111,7 +111,6 @@
 #include "chrome/browser/media_galleries/media_galleries_preferences_factory.h"
 #include "chrome/browser/metrics/variations/google_groups_manager_factory.h"
 #include "chrome/browser/navigation_predictor/navigation_predictor_keyed_service_factory.h"
-#include "chrome/browser/navigation_predictor/preloading_model_keyed_service_factory.h"
 #include "chrome/browser/navigation_predictor/search_engine_preconnector.h"
 #include "chrome/browser/navigation_predictor/search_engine_preconnector_keyed_service_factory.h"
 #include "chrome/browser/net/dns_probe_service_factory.h"
@@ -339,7 +338,6 @@
 #include "chrome/browser/prefs/persistent_renderer_prefs_manager_factory.h"
 #include "chrome/browser/privacy_sandbox/privacy_sandbox_survey_desktop_controller_factory.h"
 #include "chrome/browser/profile_resetter/reset_report_uploader_factory.h"
-#include "chrome/browser/screen_ai/screen_ai_service_router_factory.h"
 #include "chrome/browser/search/background/ntp_background_service_factory.h"
 #include "chrome/browser/search/background/ntp_custom_background_service_factory.h"
 #include "chrome/browser/search/instant_service_factory.h"
@@ -1057,7 +1055,6 @@ void ChromeBrowserMainExtraPartsProfiles
 #if BUILDFLAG(CHROME_ROOT_STORE_CERT_MANAGEMENT_UI)
   net::ServerCertificateDatabaseServiceFactory::GetInstance();
 #endif
-  PreloadingModelKeyedServiceFactory::GetInstance();
 #if BUILDFLAG(ENABLE_DICE_SUPPORT)
   ProfileManagementDisclaimerServiceFactory::GetInstance();
 #endif
@@ -1112,9 +1109,6 @@ void ChromeBrowserMainExtraPartsProfiles
   OneTimePermissionsTrackerFactory::GetInstance();
 #endif
   OpenerHeuristicServiceFactory::GetInstance();
-  if (optimization_guide::ShouldStartModelValidator()) {
-    optimization_guide::ModelValidatorKeyedServiceFactory::GetInstance();
-  }
   OptimizationGuideKeyedServiceFactory::GetInstance();
   OriginKeyedPermissionActionServiceFactory::GetInstance();
   OriginTrialsFactory::GetInstance();
@@ -1186,13 +1180,6 @@ void ChromeBrowserMainExtraPartsProfiles
   policy::UserPolicySigninServiceFactory::GetInstance();
 #endif
   PolicyBlocklistFactory::GetInstance();
-  if (base::FeatureList::IsEnabled(
-          permissions::features::kPermissionOnDeviceNotificationPredictions) ||
-      base::FeatureList::IsEnabled(
-          permissions::features::kPermissionOnDeviceGeolocationPredictions) ||
-      base::FeatureList::IsEnabled(permissions::features::kPermissionsAIv1)) {
-    PredictionModelHandlerProviderFactory::GetInstance();
-  }
   PredictionServiceFactory::GetInstance();
   predictors::AutocompleteActionPredictorFactory::GetInstance();
   predictors::LoadingPredictorFactory::GetInstance();
@@ -1278,10 +1265,6 @@ void ChromeBrowserMainExtraPartsProfiles
 #else
   SafetyHubMenuNotificationServiceFactory::GetInstance();
   SafetyHubHatsServiceFactory::GetInstance();
-  if (features::IsMainNodeAnnotationsEnabled()) {
-    screen_ai::AXMainNodeAnnotatorControllerFactory::GetInstance();
-  }
-  screen_ai::ScreenAIServiceRouterFactory::EnsureFactoryBuilt();
 #endif
 #if BUILDFLAG(IS_CHROMEOS)
   if (ash::features::IsScannerEnabled()) {
@@ -1401,7 +1384,6 @@ void ChromeBrowserMainExtraPartsProfiles
   TrackingProtectionSettingsFactory::GetInstance();
   translate::TranslateRankerFactory::GetInstance();
 #if !BUILDFLAG(IS_ANDROID)
-  tree_fixing::AXTreeFixingServicesRouterFactory::GetInstance();
   TriggeredProfileResetterFactory::GetInstance();
 #endif
 #if !BUILDFLAG(IS_CHROMEOS) && !BUILDFLAG(IS_ANDROID)
--- a/chrome/browser/profiles/off_the_record_profile_impl.cc
+++ b/chrome/browser/profiles/off_the_record_profile_impl.cc
@@ -225,11 +225,6 @@ void OffTheRecordProfileImpl::Init() {
   // AccessibilityLabelsService has a default prefs behavior in incognito.
   AccessibilityLabelsService::InitOffTheRecordPrefs(this);
 
-#if !BUILDFLAG(IS_ANDROID)
-  // To avoid using any server-side tree fixing service, it is disabled in
-  // Incognito profiles.
-  tree_fixing::InitOffTheRecordPrefs(this);
-#endif  // !BUILDFLAG(IS_ANDROID)
 
   // The ad service might not be available for some irregular profiles, like the
   // System Profile.
--- a/chrome/browser/profiles/profile_impl.cc
+++ b/chrome/browser/profiles/profile_impl.cc
@@ -855,11 +855,6 @@ void ProfileImpl::DoFinalInit(CreateMode
   // The password settings service needs to start listening to settings
   // changes from Google Mobile Services, as early as possible.
   PasswordManagerSettingsServiceFactory::GetForProfile(this);
-#else
-
-  if (features::IsMainNodeAnnotationsEnabled()) {
-    screen_ai::AXMainNodeAnnotatorControllerFactory::GetForProfile(this);
-  }
 #endif  // BUILDFLAG(IS_ANDROID)
 
   // The announcement notification  service might not be available for some
--- a/chrome/browser/profiles/profile_manager.cc
+++ b/chrome/browser/profiles/profile_manager.cc
@@ -51,7 +51,6 @@
 #include "chrome/browser/extensions/chrome_content_browser_client_extensions_part.h"
 #include "chrome/browser/lifetime/application_lifetime.h"
 #include "chrome/browser/navigation_predictor/navigation_predictor_keyed_service_factory.h"
-#include "chrome/browser/navigation_predictor/preloading_model_keyed_service_factory.h"
 #include "chrome/browser/prefs/incognito_mode_prefs.h"
 #include "chrome/browser/profiles/delete_profile_helper.h"
 #include "chrome/browser/profiles/keep_alive/profile_keep_alive_types.h"
@@ -1493,9 +1492,6 @@ void ProfileManager::DoFinalInitForServi
   // Ensure NavigationPredictorKeyedService is started.
   NavigationPredictorKeyedServiceFactory::GetForProfile(profile);
 
-  // Ensure PreloadingModelKeyedService is started.
-  PreloadingModelKeyedServiceFactory::GetForProfile(profile);
-
   IdentityManagerFactory::GetForProfile(profile)->OnNetworkInitialized();
   AccountReconcilorFactory::GetForProfile(profile);
 #if BUILDFLAG(IS_ANDROID)
--- a/chrome/browser/resources/BUILD.gn
+++ b/chrome/browser/resources/BUILD.gn
@@ -107,7 +107,6 @@ group("resources") {
   if (is_mac) {
     public_deps += [
       "//chrome/browser/resources/media/mei_preload:component",
-      "//components/privacy_sandbox/privacy_sandbox_attestations/preload:component",
     ]
   }
 
--- a/chrome/browser/resources/pdf/pdf_viewer.html
+++ b/chrome/browser/resources/pdf/pdf_viewer.html
@@ -113,10 +113,6 @@ import {AnnotationMode} from './constant
       </cr-page-selector>
     ` : ''}
   </if>
-  <cr-toast id="searchifyProgress">
-    <div class="spinner"></div>
-    <span>$i18n{searchifyInProgress}</span>
-  </cr-toast>
 </div>
 
 ${this.showErrorDialog ? html`<viewer-error-dialog id="error-dialog">
--- a/chrome/browser/ui/BUILD.gn
+++ b/chrome/browser/ui/BUILD.gn
@@ -1524,8 +1524,6 @@ static_library("ui") {
       "//chrome/browser/regional_capabilities",
       "//chrome/browser/safe_browsing",
       "//chrome/browser/safe_browsing:advanced_protection",
-      "//chrome/browser/screen_ai:screen_ai_install_state",
-      "//chrome/browser/screen_ai:screen_ai_service_router_factory",
       "//chrome/browser/smart_card",
       "//chrome/browser/tab_group_sync:utils",
       "//chrome/browser/themes",
@@ -2278,7 +2276,6 @@ static_library("ui") {
       "//chrome/browser/policy:onc",
       "//chrome/browser/policy:system_features_disable_list",
       "//chrome/browser/push_notification",
-      "//chrome/browser/screen_ai/public:optical_character_recognizer",
       "//chrome/browser/ui/ash/accessibility",
       "//chrome/browser/ui/ash/app_access",
       "//chrome/browser/ui/ash/arc",
@@ -4787,7 +4784,6 @@ static_library("ui") {
       "//chrome/browser/ui/views/user_education",
       "//chrome/browser/ui/webauthn:impl",
       "//chrome/browser/ui/webui/app_service_internals",
-      "//chrome/browser/ui/webui/autofill_ml_internals",
       "//chrome/browser/ui/webui/side_panel/customize_chrome",
       "//chrome/browser/ui/window_name_prompt",
       "//components/collaboration/public",
--- a/chrome/browser/ui/pdf/BUILD.gn
+++ b/chrome/browser/ui/pdf/BUILD.gn
@@ -6,7 +6,6 @@ import("//pdf/features.gni")
 import("//services/screen_ai/buildflags/features.gni")
 
 assert(enable_pdf)
-assert(enable_screen_ai_service)
 
 source_set("pdf") {
   sources = [
@@ -16,7 +15,6 @@ source_set("pdf") {
   deps = [
     "//chrome/browser/download",
     "//chrome/browser/pdf",
-    "//chrome/browser/screen_ai:screen_ai_install_state",
     "//chrome/browser/ui/tab_contents",
     "//chrome/browser/ui/user_education",
     "//chrome/common",
--- a/chrome/browser/ui/pdf/chrome_pdf_document_helper_client.cc
+++ b/chrome/browser/ui/pdf/chrome_pdf_document_helper_client.cc
@@ -107,21 +107,3 @@ void ChromePDFDocumentHelperClient::SetP
   }
 }
 
-void ChromePDFDocumentHelperClient::OnSearchifyStarted(
-    content::RenderFrameHost* render_frame_host) {
-  // Show the promo only when ScreenAI component is available and OCR can be
-  // done.
-  if (!screen_ai::ScreenAIInstallState::GetInstance()->IsComponentAvailable()) {
-    return;
-  }
-  content::WebContents* web_contents = GetWebContentsToUse(render_frame_host);
-  if (!MaybeShowFeaturePromo(web_contents)) {
-    return;
-  }
-  auto* const tab = tabs::TabInterface::MaybeGetFromContents(web_contents);
-  if (!tab) {
-    return;
-  }
-  tab_subscriptions_.push_back(
-      tab->RegisterWillDeactivate(base::BindRepeating(&MaybeHideFeaturePromo)));
-}
--- a/chrome/browser/ui/pdf/chrome_pdf_document_helper_client.h
+++ b/chrome/browser/ui/pdf/chrome_pdf_document_helper_client.h
@@ -25,7 +25,6 @@ class ChromePDFDocumentHelperClient : pu
   void OnSaveURL(content::WebContents* contents) override;
   void SetPluginCanSave(content::RenderFrameHost* render_frame_host,
                         bool can_save) override;
-  void OnSearchifyStarted(content::RenderFrameHost* render_frame_host) override;
 
   // Holds subscriptions for TabInterface callbacks.
   std::vector<base::CallbackListSubscription> tab_subscriptions_;
--- a/chrome/browser/ui/views/user_education/browser_user_education_service.cc
+++ b/chrome/browser/ui/views/user_education/browser_user_education_service.cc
@@ -764,39 +764,6 @@ void MaybeRegisterChromeFeaturePromos(
           .SetBubbleIcon(kLightbulbOutlineIcon)
           .SetBubbleTitleText(IDS_PASSWORD_MANAGER_IPH_CREATE_SHORTCUT_TITLE)));
 
-  // kIPHPdfSearchifyFeature:
-  registry.RegisterFeature(std::move(
-      FeaturePromoSpecification::CreateForToastPromo(
-          feature_engagement::kIPHPdfSearchifyFeature, kTopContainerElementId,
-          IDS_PDF_SEARCHIFY_IPH_BODY, IDS_PDF_SEARCHIFY_IPH_BODY_SCREEN_READER,
-          FeaturePromoSpecification::AcceleratorInfo())
-          .SetBubbleArrow(HelpBubbleArrow::kNone)
-          .SetBubbleTitleText(IDS_PDF_SEARCHIFY_IPH_TITLE)
-          .SetMetadata(132, "<EMAIL>",
-                       "Triggered once when user opens a PDF which gets OCRed.")
-          .SetAnchorElementFilter(base::BindRepeating(
-              [](const ui::ElementTracker::ElementList& elements)
-                  -> ui::TrackedElement* {
-                if (elements.empty()) {
-                  return nullptr;
-                }
-                // Ensure a searchified PDF is visible before showing the IPH.
-                auto* const browser_view =
-                    views::ElementTrackerViews::GetInstance()
-                        ->GetFirstMatchingViewAs<BrowserView>(
-                            kBrowserViewElementId, elements[0]->context());
-                std::vector<ContentsWebView*> contents_web_views =
-                    browser_view->GetAllVisibleContentsWebViews();
-                for (auto* contents_web_view : contents_web_views) {
-                  auto* pdf_doc_helper =
-                      pdf::PDFDocumentHelper::MaybeGetForWebContents(
-                          contents_web_view->GetWebContents());
-                  if (pdf_doc_helper && pdf_doc_helper->SearchifyStarted()) {
-                    return elements[0];
-                  }
-                }
-                return nullptr;
-              }))));
 
   // kIPHLensOverlayFeature:
   registry.RegisterFeature(std::move(
--- a/chrome/browser/ui/webui/BUILD.gn
+++ b/chrome/browser/ui/webui/BUILD.gn
@@ -59,7 +59,6 @@ source_set("configs") {
       "//chrome/browser/ui/webui/access_code_cast",
       "//chrome/browser/ui/webui/actor_internals",
       "//chrome/browser/ui/webui/app_service_internals",
-      "//chrome/browser/ui/webui/autofill_ml_internals",
       "//chrome/browser/ui/webui/color_pipeline_internals",
       "//chrome/browser/ui/webui/infobar_internals",
       "//chrome/browser/ui/webui/new_tab_footer",
--- a/chrome/browser/ui/webui/chrome_web_ui_configs.cc
+++ b/chrome/browser/ui/webui/chrome_web_ui_configs.cc
@@ -295,7 +295,6 @@ void RegisterChromeWebUIConfigs() {
   map.AddWebUIConfig(std::make_unique<actor::ui::ActorOverlayUIConfig>());
   map.AddWebUIConfig(std::make_unique<ActorInternalsUIConfig>());
   map.AddWebUIConfig(std::make_unique<AppServiceInternalsUIConfig>());
-  map.AddWebUIConfig(std::make_unique<AutofillMlInternalsUIConfig>());
   map.AddWebUIConfig(std::make_unique<media_router::AccessCodeCastUIConfig>());
   map.AddWebUIConfig(std::make_unique<BookmarksSidePanelUIConfig>());
   map.AddWebUIConfig(std::make_unique<BookmarksUIConfig>());
--- a/chrome/browser/ui/webui/settings/accessibility_main_handler.cc
+++ b/chrome/browser/ui/webui/settings/accessibility_main_handler.cc
@@ -35,11 +35,6 @@ void AccessibilityMainHandler::RegisterM
       base::BindRepeating(
           &AccessibilityMainHandler::HandleCheckAccessibilityImageLabels,
           base::Unretained(this)));
-  web_ui()->RegisterMessageCallback(
-      "getScreenAiInstallState",
-      base::BindRepeating(
-          &AccessibilityMainHandler::HandleGetScreenAIInstallState,
-          base::Unretained(this)));
 }
 
 void AccessibilityMainHandler::OnJavascriptAllowed() {
@@ -50,11 +45,6 @@ void AccessibilityMainHandler::OnJavascr
           base::Unretained(this)));
 #endif  // BUILDFLAG(IS_CHROMEOS)
 
-  if (features::IsMainNodeAnnotationsEnabled()) {
-    CHECK(!component_ready_observer_.IsObserving());
-    component_ready_observer_.Observe(
-        screen_ai::ScreenAIInstallState::GetInstance());
-  }
 }
 
 void AccessibilityMainHandler::OnJavascriptDisallowed() {
@@ -62,35 +52,6 @@ void AccessibilityMainHandler::OnJavascr
   accessibility_subscription_ = {};
 #endif  // BUILDFLAG(IS_CHROMEOS)
 
-  if (features::IsMainNodeAnnotationsEnabled()) {
-    component_ready_observer_.Reset();
-  }
-}
-
-void AccessibilityMainHandler::DownloadProgressChanged(double progress) {
-  CHECK_GE(progress, 0.0);
-  CHECK_LE(progress, 1.0);
-  const int progress_num = progress * 100;
-  FireWebUIListener("screen-ai-downloading-progress-changed",
-                    base::Value(progress_num));
-}
-
-void AccessibilityMainHandler::StateChanged(
-    screen_ai::ScreenAIInstallState::State state) {
-  base::Value state_value = base::Value(static_cast<int>(state));
-  FireWebUIListener("screen-ai-state-changed", state_value);
-}
-
-void AccessibilityMainHandler::HandleGetScreenAIInstallState(
-    const base::Value::List& args) {
-  CHECK_EQ(1U, args.size());
-  const base::Value& callback_id = args[0];
-  AllowJavascript();
-  // Get the current install state and send it back to a UI callback.
-  screen_ai::ScreenAIInstallState::State current_install_state =
-      screen_ai::ScreenAIInstallState::GetInstance()->get_state();
-  ResolveJavascriptCallback(
-      callback_id, base::Value(static_cast<int>(current_install_state)));
 }
 
 void AccessibilityMainHandler::HandleGetScreenReaderState(
--- a/chrome/browser/ui/webui/settings/accessibility_main_handler.h
+++ b/chrome/browser/ui/webui/settings/accessibility_main_handler.h
@@ -19,8 +19,7 @@ namespace settings {
 // Settings handler for the main accessibility settings page,
 // chrome://settings/accessibility.
 class AccessibilityMainHandler
-    : public ::settings::SettingsPageUIHandler,
-      public screen_ai::ScreenAIInstallState::Observer {
+    : public ::settings::SettingsPageUIHandler {
  public:
   AccessibilityMainHandler();
   ~AccessibilityMainHandler() override;
@@ -32,9 +31,6 @@ class AccessibilityMainHandler
   void OnJavascriptAllowed() override;
   void OnJavascriptDisallowed() override;
 
-  // screen_ai::ScreenAIInstallState::Observer:
-  void DownloadProgressChanged(double progress) override;
-  void StateChanged(screen_ai::ScreenAIInstallState::State state) override;
 
  private:
   void HandleGetScreenReaderState(const base::Value::List& args);
@@ -51,9 +47,6 @@ class AccessibilityMainHandler
   base::CallbackListSubscription accessibility_subscription_;
 #endif  // BUILDFLAG(IS_CHROMEOS)
 
-  base::ScopedObservation<screen_ai::ScreenAIInstallState,
-                          screen_ai::ScreenAIInstallState::Observer>
-      component_ready_observer_{this};
 };
 
 }  // namespace settings
--- a/chrome/browser/ui/webui/side_panel/read_anything/read_anything_untrusted_page_handler.cc
+++ b/chrome/browser/ui/webui/side_panel/read_anything/read_anything_untrusted_page_handler.cc
@@ -21,8 +21,6 @@
 #include "chrome/browser/browser_features.h"
 #include "chrome/browser/language/language_model_manager_factory.h"
 #include "chrome/browser/profiles/profile.h"
-#include "chrome/browser/screen_ai/screen_ai_service_router.h"
-#include "chrome/browser/screen_ai/screen_ai_service_router_factory.h"
 #include "chrome/browser/speech/extension_api/tts_engine_extension_api.h"
 #include "chrome/browser/translate/chrome_translate_client.h"
 #include "chrome/browser/ui/browser.h"
@@ -376,14 +374,6 @@ ReadAnythingUntrustedPageHandler::ReadAn
   prefs_lang = language::ExtractBaseLanguage(prefs_lang);
   SetDefaultLanguageCode(prefs_lang);
 
-  if (use_screen_ai_service_) {
-    screen_ai::ScreenAIServiceRouterFactory::GetForBrowserContext(profile_)
-        ->GetServiceStateAsync(
-            screen_ai::ScreenAIServiceRouter::Service::kMainContentExtraction,
-            base::BindOnce(
-                &ReadAnythingUntrustedPageHandler::OnScreenAIServiceInitialized,
-                weak_factory_.GetWeakPtr()));
-  }
 
   // Enable accessibility for the top level render frame and all descendants.
   // This causes AXTreeSerializer to reset and send accessibility events of
--- a/chrome/renderer/BUILD.gn
+++ b/chrome/renderer/BUILD.gn
@@ -334,18 +334,6 @@ static_library("renderer") {
     ]
   } else {
     sources += [
-      "accessibility/ax_tree_distiller.cc",
-      "accessibility/ax_tree_distiller.h",
-      "accessibility/read_anything/read_aloud_app_model.cc",
-      "accessibility/read_anything/read_aloud_app_model.h",
-      "accessibility/read_anything/read_aloud_traversal_utils.cc",
-      "accessibility/read_anything/read_aloud_traversal_utils.h",
-      "accessibility/read_anything/read_anything_app_controller.cc",
-      "accessibility/read_anything/read_anything_app_controller.h",
-      "accessibility/read_anything/read_anything_app_model.cc",
-      "accessibility/read_anything/read_anything_app_model.h",
-      "accessibility/read_anything/read_anything_node_utils.cc",
-      "accessibility/read_anything/read_anything_node_utils.h",
       "media/chrome_speech_recognition_client.cc",
       "media/chrome_speech_recognition_client.h",
       "searchbox/searchbox.cc",
@@ -360,7 +348,6 @@ static_library("renderer") {
       "//components/crx_file",
       "//components/trusted_vault",
       "//services/screen_ai/buildflags",
-      "//services/screen_ai/public/mojom",
       "//services/strings",
       "//third_party/re2",
     ]
--- a/chrome/renderer/chrome_render_frame_observer.cc
+++ b/chrome/renderer/chrome_render_frame_observer.cc
@@ -71,7 +71,6 @@
 #include "url/gurl.h"
 
 #if !BUILDFLAG(IS_ANDROID)
-#include "chrome/renderer/accessibility/read_anything/read_anything_app_controller.h"
 #include "chrome/renderer/actor/journal.h"
 #include "chrome/renderer/actor/tool_executor.h"
 #include "chrome/renderer/searchbox/searchbox_extension.h"
@@ -324,21 +323,6 @@ void ChromeRenderFrameObserver::DidCommi
 }
 
 void ChromeRenderFrameObserver::DidClearWindowObject() {
-#if !BUILDFLAG(IS_ANDROID)
-  const base::CommandLine& command_line =
-      *base::CommandLine::ForCurrentProcess();
-  if (command_line.HasSwitch(switches::kInstantProcess))
-    SearchBoxExtension::Install(render_frame()->GetWebFrame());
-
-  // Install ReadAnythingAppController on render frames with the Read Anything
-  // url, which is chrome-untrusted. ReadAnythingAppController installs v8
-  // bindings in the chrome.readingMode namespace which are consumed by
-  // read_anything/app.ts, the resource of the Read Anything WebUI.
-  if (render_frame()->GetWebFrame()->GetDocument().Url() ==
-      chrome::kChromeUIUntrustedReadAnythingSidePanelURL) {
-    ReadAnythingAppController::Install(render_frame());
-  }
-#endif  // !BUILDFLAG(IS_ANDROID)
 }
 
 void ChromeRenderFrameObserver::DidMeaningfulLayout(
--- a/chrome/test/BUILD.gn
+++ b/chrome/test/BUILD.gn
@@ -2375,9 +2375,6 @@ if (!is_android) {
       "//chrome/browser/resource_coordinator:tab_manager_features",
       "//chrome/browser/safe_browsing:advanced_protection",
       "//chrome/browser/safe_browsing:verdict_cache_manager_factory",
-      "//chrome/browser/screen_ai:screen_ai_install_state",
-      "//chrome/browser/screen_ai:screen_ai_service_router_factory",
-      "//chrome/browser/screen_ai/public:test_support",
       "//chrome/browser/search",
       "//chrome/browser/search_engines",
       "//chrome/browser/segmentation_platform:test_utils",
@@ -2809,8 +2806,6 @@ if (!is_android) {
       "//services/preferences/public/cpp",
       "//services/preferences/public/cpp/tracked",
       "//services/preferences/tracked:features",
-      "//services/screen_ai/public/cpp:utilities",
-      "//services/screen_ai/public/mojom",
       "//services/service_manager/public/cpp",
       "//services/strings",
       "//services/test/echo/public/mojom",
@@ -8444,7 +8439,6 @@ test("unit_tests") {
     #
     # TODO(crbug.com/417513088): Maybe merge with the non-android `deps` declaration above?
     deps += [
-      "../browser/screen_ai:screen_ai_install_state",
       "//chrome:packed_resources_integrity_header",
       "//chrome/browser/apps:icon_standardizer",
       "//chrome/browser/apps/app_service",
@@ -8477,7 +8471,6 @@ test("unit_tests") {
       "//chrome/browser/profile_resetter:fake_profile_resetter",
       "//chrome/browser/resource_coordinator:tab_manager_features",
       "//chrome/browser/resources/new_tab_page_instant:resources_grit",
-      "//chrome/browser/screen_ai:unit_tests",
       "//chrome/browser/search/background",
       "//chrome/browser/search/background:constants",
       "//chrome/browser/search_engine_choice:unit_tests",
--- a/chrome/test/chromedriver/BUILD.gn
+++ b/chrome/test/chromedriver/BUILD.gn
@@ -416,7 +416,7 @@ source_set("lib") {
   configs += [ "//build/config/compiler:no_exit_time_destructors" ]
 }
 
-if (is_linux) {
+if (false) {
   # Linux
   executable("chromedriver_server.unstripped") {
     testonly = true
--- a/chrome/test/variations/BUILD.gn
+++ b/chrome/test/variations/BUILD.gn
@@ -18,7 +18,6 @@ python_library("test_utils") {
 
   data = [ "//chrome/test/variations/test_utils/" ]
 
-  data_deps = [ "//third_party/catapult/third_party/gsutil" ]
 }
 
 python_library("fixtures") {
--- a/chrome/utility/BUILD.gn
+++ b/chrome/utility/BUILD.gn
@@ -145,7 +145,6 @@ static_library("utility") {
       "//components/user_data_importer/content",
       "//components/user_data_importer/utility:bookmarks",
       "//services/proxy_resolver:lib",
-      "//services/screen_ai",
     ]
   }
 
--- a/chrome/utility/services.cc
+++ b/chrome/utility/services.cc
@@ -61,8 +61,6 @@
 #include "services/passage_embeddings/passage_embeddings_service.h"
 #include "services/proxy_resolver/proxy_resolver_factory_impl.h"  // nogncheck
 #include "services/proxy_resolver/public/mojom/proxy_resolver.mojom.h"
-#include "services/screen_ai/public/mojom/screen_ai_factory.mojom.h"  // nogncheck
-#include "services/screen_ai/screen_ai_service_impl.h"  // nogncheck
 #endif  // !BUILDFLAG(IS_ANDROID)
 
 #if BUILDFLAG(ENABLE_BROWSER_SPEECH_SERVICE)
@@ -268,12 +266,6 @@ auto RunSpeechRecognitionService(
 }
 #endif  // !BUILDFLAG(ENABLE_BROWSER_SPEECH_SERVICE)
 
-#if !BUILDFLAG(IS_ANDROID)
-auto RunScreenAIServiceFactory(
-    mojo::PendingReceiver<screen_ai::mojom::ScreenAIServiceFactory> receiver) {
-  return std::make_unique<screen_ai::ScreenAIService>(std::move(receiver));
-}
-#endif
 
 #if BUILDFLAG(IS_CHROMEOS)
 auto RunCupsIppParser(
@@ -476,7 +468,6 @@ void RegisterMainThreadServices(mojo::Se
   services.Add(RunProfileImporter);
   services.Add(RunMirroringService);
   services.Add(RunPassageEmbeddingsService);
-  services.Add(RunScreenAIServiceFactory);
 #endif  // !BUILDFLAG(IS_ANDROID)
 
 #if BUILDFLAG(ENABLE_BROWSER_SPEECH_SERVICE)
--- a/components/BUILD.gn
+++ b/components/BUILD.gn
@@ -613,7 +613,6 @@ test("components_unittests") {
       "//components/media_router/common/providers/cast/channel:unit_tests",
       "//components/page_info:unit_tests",
       "//components/permissions:unit_tests",
-      "//components/permissions/prediction_service:unit_tests",
       "//components/privacy_sandbox/privacy_sandbox_attestations:unit_tests",
       "//components/safety_check:unit_tests",
       "//components/security_interstitials/content:unit_tests",
--- a/components/autofill/core/browser/autofill_field.cc
+++ b/components/autofill/core/browser/autofill_field.cc
@@ -24,7 +24,6 @@
 #include "components/autofill/core/browser/field_type_utils.h"
 #include "components/autofill/core/browser/field_types.h"
 #include "components/autofill/core/browser/heuristic_source.h"
-#include "components/autofill/core/browser/ml_model/field_classification_model_handler.h"
 #include "components/autofill/core/browser/proto/server.pb.h"
 #include "components/autofill/core/common/autofill_constants.h"
 #include "components/autofill/core/common/autofill_features.h"
--- a/components/omnibox/browser/autocomplete_classifier.cc
+++ b/components/omnibox/browser/autocomplete_classifier.cc
@@ -131,8 +131,6 @@ void AutocompleteClassifier::Classify(
     metrics::OmniboxEventProto::PageClassification page_classification,
     AutocompleteMatch* match,
     GURL* alternate_nav_url) {
-  TRACE_EVENT1("omnibox", "AutocompleteClassifier::Classify", "text",
-               base::UTF16ToUTF8(text));
   DCHECK(!inside_classify_);
   base::AutoReset<bool> reset(&inside_classify_, true);
   AutocompleteInput input(text, page_classification, *scheme_classifier_);
--- a/components/pdf/renderer/pdf_view_web_plugin_client.h
+++ b/components/pdf/renderer/pdf_view_web_plugin_client.h
@@ -12,7 +12,6 @@
 #include "base/memory/weak_ptr.h"
 #include "mojo/public/cpp/bindings/remote.h"
 #include "pdf/pdf_view_web_plugin.h"
-#include "services/screen_ai/public/mojom/screen_ai_service.mojom.h"
 
 namespace blink {
 class WebLocalFrame;
@@ -110,8 +109,6 @@ class PdfViewWebPluginClient : public ch
 
   raw_ptr<blink::WebPluginContainer> plugin_container_;
 
-  mojo::Remote<screen_ai::mojom::ScreenAIAnnotator> screen_ai_annotator_;
-  base::RepeatingClosure ocr_disconnect_callback_;
 
   base::WeakPtrFactory<PdfViewWebPluginClient> weak_factory_{this};
 };
--- a/components/update_client/BUILD.gn
+++ b/components/update_client/BUILD.gn
@@ -361,8 +361,6 @@ source_set("unit_tests") {
     "//third_party/puffin:libpuffpatch",
     "//third_party/re2",
   ]
-
-  data_deps = [ "//components/test/data/update_client/puffin_patch_test:puffin_patch_test_files" ]
 }
 
 fuzzer_test("update_client_protocol_serializer_fuzzer") {
--- a/content/shell/BUILD.gn
+++ b/content/shell/BUILD.gn
@@ -836,10 +836,6 @@ if (is_apple) {
       deps = [ "//third_party/icu:icudata" ]
     }
 
-    if (is_mac) {
-      sources += [ "//ui/gl/resources/angle-metal/gpu_shader_cache.bin" ]
-    }
-
     if (v8_use_external_startup_data) {
       public_deps += [ "//v8" ]
       if (use_v8_context_snapshot) {
--- a/services/on_device_model/on_device_model.gni
+++ b/services/on_device_model/on_device_model.gni
@@ -15,7 +15,6 @@ declare_args() {
   # Constraint code is disabled on platforms where the on-device model does not
   # run due to binary size.
   enable_constraints = is_win || is_mac || is_linux || is_ios || is_cbx
-  enable_ml_internal = build_with_internal_optimization_guide &&
-                       (is_win || is_mac || is_linux || is_ios || is_cbx)
+  enable_ml_internal = false
   use_chromeos_model_service = is_chrome_branded && is_cbx
 }
--- a/services/passage_embeddings/passage_embeddings_service.cc
+++ b/services/passage_embeddings/passage_embeddings_service.cc
@@ -23,7 +23,6 @@ PassageEmbeddingsService::~PassageEmbedd
 
 #if BUILDFLAG(BUILD_WITH_TFLITE_LIB)
 void PassageEmbeddingsService::OnEmbedderDisconnect() {
-  embedder_.reset();
 }
 #endif
 
--- a/services/passage_embeddings/passage_embeddings_service.h
+++ b/services/passage_embeddings/passage_embeddings_service.h
@@ -11,7 +11,6 @@
 
 namespace passage_embeddings {
 
-class PassageEmbedder;
 
 // Class implementation of the passage embeddings service mojo interface.
 class PassageEmbeddingsService : public mojom::PassageEmbeddingsService {
--- a/services/screen_ai/buildflags/features.gni
+++ b/services/screen_ai/buildflags/features.gni
@@ -6,14 +6,13 @@ import("//build/config/sanitizers/saniti
 
 declare_args() {
   # Screen AI service is only supported on desktop platforms.
-  enable_screen_ai_service = is_linux || is_mac || is_chromeos || is_win
+  enable_screen_ai_service = false
 
   # Screen AI library is not available for browser tests on ChromeOS. Tast tests
   # cover library's OCR functionality on ChromeOS.
   enable_screen_ai_browsertests =
-      is_linux || (is_mac && (target_cpu == "arm64" || target_cpu == "x64")) ||
-      (is_win && (target_cpu == "x64" || target_cpu == "x86"))
+      false
 
   # Screen AI library is not available for MSAN and UBSAN.
-  use_fake_screen_ai = is_msan || is_ubsan || is_ubsan_vptr || is_ubsan_security
+  use_fake_screen_ai = false
 }
--- a/third_party/devtools-frontend/src/front_end/BUILD.gn
+++ b/third_party/devtools-frontend/src/front_end/BUILD.gn
@@ -209,7 +209,6 @@ group("unittests") {
     "panels/timeline:unittests",
     "panels/timeline/components:unittests",
     "panels/timeline/components/insights:unittests",
-    "panels/timeline/fixtures/traces",
     "panels/timeline/overlays:unittests",
     "panels/timeline/overlays/components:unittests",
     "panels/timeline/track_appenders:unittests",
--- a/third_party/devtools-frontend/src/front_end/models/trace/lantern/core/BUILD.gn
+++ b/third_party/devtools-frontend/src/front_end/models/trace/lantern/core/BUILD.gn
@@ -38,5 +38,4 @@ ts_library("unittests") {
     "../testing:bundle",
   ]
 
-  public_deps = [ "../../../../panels/timeline/fixtures/traces" ]
 }
--- a/third_party/devtools-frontend/src/front_end/models/trace/lantern/metrics/BUILD.gn
+++ b/third_party/devtools-frontend/src/front_end/models/trace/lantern/metrics/BUILD.gn
@@ -54,5 +54,4 @@ ts_library("unittests") {
     "../testing:bundle",
   ]
 
-  public_deps = [ "../../../../panels/timeline/fixtures/traces" ]
 }
--- a/third_party/devtools-frontend/src/front_end/models/trace/lantern/simulation/BUILD.gn
+++ b/third_party/devtools-frontend/src/front_end/models/trace/lantern/simulation/BUILD.gn
@@ -51,5 +51,4 @@ ts_library("unittests") {
     "../testing:bundle",
   ]
 
-  public_deps = [ "../../../../panels/timeline/fixtures/traces" ]
 }
--- a/third_party/puffin/BUILD.gn
+++ b/third_party/puffin/BUILD.gn
@@ -115,7 +115,6 @@ executable("puffin_unittest") {
     "src/unittest_common.cc",
     "src/utils_unittest.cc",
   ]
-  data_deps = [ "//components/test/data/update_client/puffin_patch_test:puffin_patch_test_files" ]
   deps = [
     ":libpuffdiff",
     ":libpuffpatch",
