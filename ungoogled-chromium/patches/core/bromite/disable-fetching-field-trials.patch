# NOTE: Modified to remove usage of compiler #if macros
From: csagan5 <<EMAIL>>
Date: Sun, 8 Jul 2018 18:16:34 +0200
Subject: Disable fetching of all field trials

---
 .../browser/flags/ChromeFeatureList.java      | 19 ++++---------------
 .../variations/service/variations_service.cc  | 12 +-----------
 2 <USER> <GROUP>, 5 insertions(+), 26 deletions(-)

--- a/chrome/browser/flags/android/java/src/org/chromium/chrome/browser/flags/ChromeFeatureList.java
+++ b/chrome/browser/flags/android/java/src/org/chromium/chrome/browser/flags/ChromeFeatureList.java
@@ -60,7 +60,7 @@ public abstract class ChromeFeatureList
      * |kFeaturesExposedToJava| in chrome/browser/flags/android/chrome_feature_list.cc
      */
     public static String getFieldTrialParamByFeature(String featureName, String paramName) {
-        return ChromeFeatureMap.getInstance().getFieldTrialParamByFeature(featureName, paramName);
+        return "";
     }
 
     /**
@@ -72,8 +72,7 @@ public abstract class ChromeFeatureList
      */
     public static boolean getFieldTrialParamByFeatureAsBoolean(
             String featureName, String paramName, boolean defaultValue) {
-        return ChromeFeatureMap.getInstance()
-                .getFieldTrialParamByFeatureAsBoolean(featureName, paramName, defaultValue);
+        return defaultValue;
     }
 
     /**
@@ -85,8 +84,7 @@ public abstract class ChromeFeatureList
      */
     public static int getFieldTrialParamByFeatureAsInt(
             String featureName, String paramName, int defaultValue) {
-        return ChromeFeatureMap.getInstance()
-                .getFieldTrialParamByFeatureAsInt(featureName, paramName, defaultValue);
+        return defaultValue;
     }
 
     /**
@@ -98,8 +96,7 @@ public abstract class ChromeFeatureList
      */
     public static double getFieldTrialParamByFeatureAsDouble(
             String featureName, String paramName, double defaultValue) {
-        return ChromeFeatureMap.getInstance()
-                .getFieldTrialParamByFeatureAsDouble(featureName, paramName, defaultValue);
+        return defaultValue;
     }
 
     /**
--- a/components/variations/service/variations_service.cc
+++ b/components/variations/service/variations_service.cc
@@ -223,22 +223,7 @@ bool GetInstanceManipulations(const net:
 // Variations seed fetching is only enabled in official Chrome builds, if a URL
 // is specified on the command line, and for testing.
 bool IsFetchingEnabled() {
-#if BUILDFLAG(GOOGLE_CHROME_BRANDING)
-  if (base::CommandLine::ForCurrentProcess()->HasSwitch(
-          switches::kDisableVariationsSeedFetch)) {
     return false;
-  }
-#else
-  if (!base::CommandLine::ForCurrentProcess()->HasSwitch(
-          switches::kVariationsServerURL) &&
-      !g_should_fetch_for_testing) {
-    DVLOG(1)
-        << "Not performing repeated fetching in unofficial build without --"
-        << switches::kVariationsServerURL << " specified.";
-    return false;
-  }
-#endif  // BUILDFLAG(GOOGLE_CHROME_BRANDING)
-  return true;
 }
 
 // Returns the already downloaded first run seed, and clear the seed from the
