# Disable rlz

--- a/BUILD.gn
+++ b/BUILD.gn
@@ -469,14 +469,6 @@ group("gn_all") {
       ]
     }
 
-    if (is_chromeos || is_mac || is_win) {
-      deps += [
-        "//rlz:rlz_id",
-        "//rlz:rlz_lib",
-        "//rlz:rlz_unittests",
-      ]
-    }
-
     if (is_linux || is_chromeos) {
       # The following are definitely linux-only.
       deps += [
--- a/rlz/buildflags/buildflags.gni
+++ b/rlz/buildflags/buildflags.gni
@@ -6,7 +6,7 @@ import("//build/config/chrome_build.gni"
 
 # Whether we are using the rlz library or not.  Platforms like Android send
 # rlz codes for searches but do not use the library.
-enable_rlz_support = is_win || is_apple || is_chromeos
+enable_rlz_support = false
 
 declare_args() {
   enable_rlz = is_chrome_branded && enable_rlz_support
