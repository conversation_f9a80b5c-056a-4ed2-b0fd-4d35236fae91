--- a/chrome/browser/extensions/component_extensions_allowlist/allowlist.cc
+++ b/chrome/browser/extensions/component_extensions_allowlist/allowlist.cc
@@ -29,7 +29,6 @@ namespace extensions {
 
 bool IsComponentExtensionAllowlisted(const std::string& extension_id) {
   constexpr auto kAllowed = base::MakeFixedFlatSet<std::string_view>({
-      extension_misc::kInAppPaymentsSupportAppId,
       extension_misc::kPdfExtensionId,
 #if BUILDFLAG(IS_CHROMEOS)
       extension_misc::kAssessmentAssistantExtensionId,
--- a/chrome/browser/extensions/component_loader.cc
+++ b/chrome/browser/extensions/component_loader.cc
@@ -415,11 +415,6 @@ void ComponentLoader::AddWebStoreApp() {
     return;
   }
 #endif
-
-  AddWithNameAndDescription(
-      IDR_WEBSTORE_MANIFEST, base::FilePath(FILE_PATH_LITERAL("web_store")),
-      l10n_util::GetStringUTF8(IDS_WEBSTORE_NAME_STORE),
-      l10n_util::GetStringUTF8(IDS_WEBSTORE_APP_DESCRIPTION));
 }
 
 #if BUILDFLAG(IS_CHROMEOS)
--- a/chrome/browser/extensions/external_component_loader.cc
+++ b/chrome/browser/extensions/external_component_loader.cc
@@ -38,9 +38,6 @@ ExternalComponentLoader::~ExternalCompon
 void ExternalComponentLoader::StartLoading() {
   auto prefs = base::Value::Dict();
   // Skip in-app payments app on Android. crbug.com/409396604
-#if BUILDFLAG(GOOGLE_CHROME_BRANDING) && !BUILDFLAG(IS_ANDROID)
-  AddExternalExtension(extension_misc::kInAppPaymentsSupportAppId, prefs);
-#endif  // BUILDFLAG(GOOGLE_CHROME_BRANDING) && !BUILDFLAG(IS_ANDROID)
 
 #if BUILDFLAG(IS_CHROMEOS)
   {
--- a/chrome/browser/extensions/webstore_installer.cc
+++ b/chrome/browser/extensions/webstore_installer.cc
@@ -485,20 +485,6 @@ void WebstoreInstaller::DownloadNextPend
 
 void WebstoreInstaller::DownloadCrx(const extensions::ExtensionId& extension_id,
                                     InstallSource source) {
-  download_url_ = GetWebstoreInstallURL(extension_id, source);
-  MaybeAppendAuthUserParameter(approval_->authuser, &download_url_);
-
-  base::FilePath user_data_dir;
-  base::PathService::Get(chrome::DIR_USER_DATA, &user_data_dir);
-  base::FilePath download_path = user_data_dir.Append(kWebstoreDownloadFolder);
-
-  base::FilePath download_directory(g_download_directory_for_tests ?
-      *g_download_directory_for_tests : download_path);
-
-  GetExtensionFileTaskRunner()->PostTaskAndReplyWithResult(
-      FROM_HERE,
-      base::BindOnce(&GetDownloadFilePath, download_directory, extension_id),
-      base::BindOnce(&WebstoreInstaller::StartDownload, this, extension_id));
 }
 
 // http://crbug.com/165634
@@ -653,21 +639,6 @@ void WebstoreInstaller::UpdateDownloadPr
 void WebstoreInstaller::StartCrxInstaller(const DownloadItem& download) {
   DCHECK_CURRENTLY_ON(BrowserThread::UI);
   DCHECK(!crx_installer_.get());
-
-  const InstallApproval* approval = GetAssociatedApproval(download);
-  DCHECK(approval);
-
-  crx_installer_ = download_crx_util::CreateCrxInstaller(profile_, download);
-
-  crx_installer_->set_expected_id(approval->extension_id);
-  crx_installer_->set_is_gallery_install(true);
-  crx_installer_->set_allow_silent_install(true);
-  crx_installer_->AddInstallerCallback(base::BindOnce(
-      &WebstoreInstaller::OnInstallerDone, weak_ptr_factory_.GetWeakPtr()));
-  if (approval->withhold_permissions)
-    crx_installer_->set_withhold_permissions();
-
-  crx_installer_->InstallCrx(download.GetFullPath());
 }
 
 void WebstoreInstaller::ReportFailure(const std::string& error,
