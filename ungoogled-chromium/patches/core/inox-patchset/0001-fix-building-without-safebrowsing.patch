--- a/chrome/browser/BUILD.gn
+++ b/chrome/browser/BUILD.gn
@@ -3794,8 +3794,6 @@ static_library("browser") {
       "download/download_auto_open_policy_handler.cc",
       "download/download_auto_open_policy_handler.h",
       "download/download_commands.cc",
-      "download/download_danger_prompt.cc",
-      "download/download_danger_prompt.h",
       "download/download_dir_policy_handler.cc",
       "download/download_dir_policy_handler.h",
       "download/download_dir_util.cc",
--- a/chrome/browser/chrome_content_browser_client_navigation_throttles.cc
+++ b/chrome/browser/chrome_content_browser_client_navigation_throttles.cc
@@ -390,10 +390,6 @@ void CreateAndAddChromeThrottlesForNavig
   registry.AddThrottle(std::make_unique<PolicyBlocklistNavigationThrottle>(
       registry, handle.GetWebContents()->GetBrowserContext()));
 
-  // Before setting up SSL error detection, configure SSLErrorHandler to invoke
-  // the relevant extension API whenever an SSL interstitial is shown.
-  SSLErrorHandler::SetClientCallbackOnInterstitialsShown(
-      base::BindRepeating(&MaybeTriggerSecurityInterstitialShownEvent));
   registry.AddThrottle(std::make_unique<SSLErrorNavigationThrottle>(
       registry, base::BindOnce(&HandleSSLErrorWrapper),
       base::BindOnce(&IsInHostedApp),
--- a/chrome/browser/component_updater/file_type_policies_component_installer.cc
+++ b/chrome/browser/component_updater/file_type_policies_component_installer.cc
@@ -36,21 +36,6 @@ const uint8_t kFileTypePoliciesPublicKey
 const char kFileTypePoliciesManifestName[] = "File Type Policies";
 
 void LoadFileTypesFromDisk(const base::FilePath& pb_path) {
-  if (pb_path.empty()) {
-    return;
-  }
-
-  VLOG(1) << "Reading Download File Types from file: " << pb_path.value();
-  std::string binary_pb;
-  if (!base::ReadFileToString(pb_path, &binary_pb)) {
-    // The file won't exist on new installations, so this is not always an
-    // error.
-    VLOG(1) << "Failed reading from " << pb_path.value();
-    return;
-  }
-
-  safe_browsing::FileTypePolicies::GetInstance()->PopulateFromDynamicUpdate(
-      binary_pb);
 }
 
 }  // namespace
--- a/chrome/browser/download/download_item_model.cc
+++ b/chrome/browser/download/download_item_model.cc
@@ -128,7 +128,7 @@ class DownloadItemModelData : public bas
 
   // Danger level of the file determined based on the file type and whether
   // there was a user action associated with the download.
-  DownloadFileType::DangerLevel danger_level_ = DownloadFileType::NOT_DANGEROUS;
+  safe_browsing::DownloadFileType::DangerLevel danger_level_ = safe_browsing::DownloadFileType::NOT_DANGEROUS;
 
   // Whether the download is currently being revived.
   bool is_being_revived_ = false;
@@ -463,13 +463,13 @@ void DownloadItemModel::SetShouldPreferO
   data->should_prefer_opening_in_browser_ = preference;
 }
 
-DownloadFileType::DangerLevel DownloadItemModel::GetDangerLevel() const {
+safe_browsing::DownloadFileType::DangerLevel DownloadItemModel::GetDangerLevel() const {
   const DownloadItemModelData* data = DownloadItemModelData::Get(download_);
-  return data ? data->danger_level_ : DownloadFileType::NOT_DANGEROUS;
+  return data ? data->danger_level_ : safe_browsing::DownloadFileType::NOT_DANGEROUS;
 }
 
 void DownloadItemModel::SetDangerLevel(
-    DownloadFileType::DangerLevel danger_level) {
+    safe_browsing::DownloadFileType::DangerLevel danger_level) {
   DownloadItemModelData* data = DownloadItemModelData::GetOrCreate(download_);
   data->danger_level_ = danger_level;
 }
--- a/chrome/browser/extensions/api/downloads/downloads_api.cc
+++ b/chrome/browser/extensions/api/downloads/downloads_api.cc
@@ -36,7 +36,6 @@
 #include "chrome/browser/browser_process.h"
 #include "chrome/browser/download/download_core_service.h"
 #include "chrome/browser/download/download_core_service_factory.h"
-#include "chrome/browser/download/download_danger_prompt.h"
 #include "chrome/browser/download/download_file_icon_extractor.h"
 #include "chrome/browser/download/download_open_prompt.h"
 #include "chrome/browser/download/download_prefs.h"
@@ -1379,9 +1378,6 @@ DownloadsAcceptDangerFunction::Downloads
 
 DownloadsAcceptDangerFunction::~DownloadsAcceptDangerFunction() = default;
 
-DownloadsAcceptDangerFunction::OnPromptCreatedCallback*
-    DownloadsAcceptDangerFunction::on_prompt_created_ = nullptr;
-
 ExtensionFunction::ResponseAction DownloadsAcceptDangerFunction::Run() {
   std::optional<downloads::AcceptDanger::Params> params =
       downloads::AcceptDanger::Params::Create(args());
@@ -1431,44 +1427,7 @@ void DownloadsAcceptDangerFunction::Prom
     return;
   }
   RecordApiFunctions(DownloadsFunctionName::kDownloadsFunctionAcceptDanger);
-  // DownloadDangerPrompt displays a modal dialog using native widgets that the
-  // user must either accept or cancel. It cannot be scripted.
-  DownloadDangerPrompt* prompt = DownloadDangerPrompt::Create(
-      download_item, web_contents,
-      base::BindOnce(&DownloadsAcceptDangerFunction::DangerPromptCallback, this,
-                     download_id));
-  // DownloadDangerPrompt deletes itself
-  if (on_prompt_created_ && !on_prompt_created_->is_null()) {
-    std::move(*on_prompt_created_).Run(prompt);
-    on_prompt_created_ = nullptr;
-  }
-  // Function finishes in DangerPromptCallback().
-}
-
-void DownloadsAcceptDangerFunction::DangerPromptCallback(
-    int download_id,
-    DownloadDangerPrompt::Action action) {
-  DCHECK_CURRENTLY_ON(BrowserThread::UI);
-  DownloadItem* download_item = GetDownload(
-      browser_context(), include_incognito_information(), download_id);
-  std::string error;
-  if (InvalidId(download_item, &error) ||
-      Fault(download_item->GetState() != DownloadItem::IN_PROGRESS,
-            download_extension_errors::kNotInProgress, &error)) {
-    Respond(Error(std::move(error)));
-    return;
-  }
-  switch (action) {
-    case DownloadDangerPrompt::ACCEPT:
-      download_item->ValidateDangerousDownload();
-      break;
-    case DownloadDangerPrompt::CANCEL:
-      download_item->Remove();
-      break;
-    case DownloadDangerPrompt::DISMISS:
-      break;
-  }
-  Respond(NoArguments());
+  download_item->ValidateDangerousDownload();
 }
 
 DownloadsShowFunction::DownloadsShowFunction() = default;
--- a/chrome/browser/extensions/api/downloads/downloads_api.h
+++ b/chrome/browser/extensions/api/downloads/downloads_api.h
@@ -13,7 +13,6 @@
 #include "base/memory/raw_ptr.h"
 #include "base/scoped_observation.h"
 #include "base/time/time.h"
-#include "chrome/browser/download/download_danger_prompt.h"
 #include "chrome/common/extensions/api/downloads.h"
 #include "components/download/content/public/all_download_item_notifier.h"
 #include "components/download/public/common/download_path_reservation_tracker.h"
@@ -201,13 +200,6 @@ class DownloadsRemoveFileFunction : publ
 
 class DownloadsAcceptDangerFunction : public ExtensionFunction {
  public:
-  using OnPromptCreatedCallback =
-      base::OnceCallback<void(DownloadDangerPrompt*)>;
-  static void OnPromptCreatedForTesting(
-      OnPromptCreatedCallback* callback) {
-    on_prompt_created_ = callback;
-  }
-
   DECLARE_EXTENSION_FUNCTION("downloads.acceptDanger", DOWNLOADS_ACCEPTDANGER)
   DownloadsAcceptDangerFunction();
 
@@ -219,13 +211,10 @@ class DownloadsAcceptDangerFunction : pu
 
  protected:
   ~DownloadsAcceptDangerFunction() override;
-  void DangerPromptCallback(int download_id,
-                            DownloadDangerPrompt::Action action);
 
  private:
   void PromptOrWait(int download_id, int retries);
 
-  static OnPromptCreatedCallback* on_prompt_created_;
 };
 
 class DownloadsShowFunction : public ExtensionFunction {
--- a/chrome/browser/extensions/api/webstore_private/webstore_private_api.cc
+++ b/chrome/browser/extensions/api/webstore_private/webstore_private_api.cc
@@ -703,18 +703,6 @@ void WebstorePrivateBeginInstallWithMani
 
 void WebstorePrivateBeginInstallWithManifest3Function::
     ReportFrictionAcceptedEvent() {
-  if (!profile_) {
-    return;
-  }
-  auto* metrics_collector =
-      safe_browsing::SafeBrowsingMetricsCollectorFactory::GetForProfile(
-          profile_);
-  // `metrics_collector` can be null in incognito.
-  if (metrics_collector) {
-    metrics_collector->AddSafeBrowsingEventToPref(
-        safe_browsing::SafeBrowsingMetricsCollector::EventType::
-            EXTENSION_ALLOWLIST_INSTALL_BYPASS);
-  }
 }
 
 void WebstorePrivateBeginInstallWithManifest3Function::OnInstallPromptDone(
--- a/chrome/browser/extensions/blocklist_state_fetcher.cc
+++ b/chrome/browser/extensions/blocklist_state_fetcher.cc
@@ -75,8 +75,7 @@ void BlocklistStateFetcher::SendRequest(
   std::string request_str;
   request.SerializeToString(&request_str);
 
-  GURL request_url = GURL(safe_browsing::GetReportUrl(
-      *safe_browsing_config_, "clientreport/crx-list-info"));
+  GURL request_url = GURL();
   net::NetworkTrafficAnnotationTag traffic_annotation =
       net::DefineNetworkTrafficAnnotation("extension_blacklist", R"(
         semantics {
@@ -131,12 +130,6 @@ void BlocklistStateFetcher::SendRequest(
                      base::Unretained(this), fetcher));
 }
 
-void BlocklistStateFetcher::SetSafeBrowsingConfig(
-    const safe_browsing::V4ProtocolConfig& config) {
-  safe_browsing_config_ =
-      std::make_unique<safe_browsing::V4ProtocolConfig>(config);
-}
-
 void BlocklistStateFetcher::OnURLLoaderComplete(
     network::SimpleURLLoader* url_loader,
     std::unique_ptr<std::string> response_body) {
--- a/chrome/browser/extensions/blocklist_state_fetcher.h
+++ b/chrome/browser/extensions/blocklist_state_fetcher.h
@@ -39,8 +39,6 @@ class BlocklistStateFetcher {
 
   virtual void Request(const std::string& id, RequestCallback callback);
 
-  void SetSafeBrowsingConfig(const safe_browsing::V4ProtocolConfig& config);
-
  protected:
   void OnURLLoaderComplete(network::SimpleURLLoader* url_loader,
                            std::unique_ptr<std::string> response_body);
--- a/chrome/browser/safe_browsing/BUILD.gn
+++ b/chrome/browser/safe_browsing/BUILD.gn
@@ -7,6 +7,7 @@ import("//components/safe_browsing/build
 import("//extensions/buildflags/buildflags.gni")
 
 static_library("safe_browsing") {
+  if (false) {
   sources = [
     "chrome_controller_client.cc",
     "chrome_controller_client.h",
@@ -77,6 +78,7 @@ static_library("safe_browsing") {
     "//services/metrics/public/cpp:ukm_builders",
     "//services/preferences/public/cpp",
   ]
+  }
 
   if (enable_extensions) {
     deps += [ "//chrome/browser/ui/web_applications" ]
@@ -601,6 +603,7 @@ static_library("advanced_protection") {
 }
 
 source_set("metrics_collector") {
+  if (false) {
   sources = [
     "safe_browsing_metrics_collector_factory.cc",
     "safe_browsing_metrics_collector_factory.h",
@@ -620,6 +623,7 @@ source_set("metrics_collector") {
     "//components/safe_browsing/core/common:safe_browsing_prefs",
     "//content/public/browser",
   ]
+  }
 }
 
 source_set("unit_tests") {
--- a/chrome/browser/ui/views/download/BUILD.gn
+++ b/chrome/browser/ui/views/download/BUILD.gn
@@ -6,7 +6,6 @@ assert(is_win || is_mac || is_linux || i
 
 source_set("download") {
   sources = [
-    "download_danger_prompt_views.cc",
     "download_in_progress_dialog_view.cc",
     "download_in_progress_dialog_view.h",
   ]
--- a/chrome/browser/ui/webui/downloads/downloads_dom_handler.cc
+++ b/chrome/browser/ui/webui/downloads/downloads_dom_handler.cc
@@ -24,7 +24,6 @@
 #include "base/threading/thread.h"
 #include "base/values.h"
 #include "chrome/browser/browser_process.h"
-#include "chrome/browser/download/download_danger_prompt.h"
 #include "chrome/browser/download/download_history.h"
 #include "chrome/browser/download/download_item_model.h"
 #include "chrome/browser/download/download_item_warning_data.h"
@@ -550,12 +549,6 @@ void DownloadsDOMHandler::RemoveDownload
   IdSet ids;
 
   for (download::DownloadItem* download : to_remove) {
-    if (download->IsDangerous() || download->IsInsecure()) {
-      // Don't allow users to revive dangerous downloads; just nuke 'em.
-      download->Remove();
-      continue;
-    }
-
     DownloadItemModel item_model(download);
     if (!item_model.ShouldShowInUi() ||
         download->GetState() == download::DownloadItem::IN_PROGRESS) {
--- a/chrome/browser/ui/webui/downloads/downloads_dom_handler.h
+++ b/chrome/browser/ui/webui/downloads/downloads_dom_handler.h
@@ -13,7 +13,6 @@
 #include "base/memory/raw_ptr.h"
 #include "base/memory/weak_ptr.h"
 #include "base/time/time.h"
-#include "chrome/browser/download/download_danger_prompt.h"
 #include "chrome/browser/download/download_warning_desktop_hats_utils.h"
 #include "chrome/browser/ui/webui/downloads/downloads.mojom-forward.h"
 #include "chrome/browser/ui/webui/downloads/downloads_list_tracker.h"
--- a/chrome/renderer/chrome_content_renderer_client.cc
+++ b/chrome/renderer/chrome_content_renderer_client.cc
@@ -110,7 +110,6 @@
 #include "components/pdf/common/pdf_util.h"
 #include "components/permissions/features.h"
 #include "components/safe_browsing/buildflags.h"
-#include "components/safe_browsing/content/renderer/threat_dom_details.h"
 #include "components/sampling_profiler/process_type.h"
 #include "components/sampling_profiler/thread_profiler.h"
 #include "components/security_interstitials/content/renderer/security_interstitial_page_controller_delegate_impl.h"
