--- a/components/autofill/core/browser/crowdsourcing/autofill_crowdsourcing_manager.cc
+++ b/components/autofill/core/browser/crowdsourcing/autofill_crowdsourcing_manager.cc
@@ -925,94 +925,6 @@ std::tuple<GURL, std::string> AutofillCr
 }
 
 bool AutofillCrowdsourcingManager::StartRequest(FormRequestData request_data) {
-  // kRequestUploads take no IsolationInfo because Password Manager uploads when
-  // RenderFrameHostImpl::DidCommitNavigation() is called, in which case
-  // AutofillDriver::IsolationInfo() may crash because there is no committing
-  // NavigationRequest. Not setting an IsolationInfo is safe because no
-  // information about the response is passed to the renderer, or is otherwise
-  // visible to a page. See crbug/1176635#c22.
-#if BUILDFLAG(IS_IOS)
-  DCHECK(!request_data.isolation_info);
-#else
-  DCHECK(
-      (request_data.request_type == CrowdsourcingRequestType::kRequestUpload) ==
-      !request_data.isolation_info);
-#endif
-  // Get the URL and method to use for this request.
-  auto [request_url, method] = GetRequestURLAndMethod(request_data);
-
-  // Track the URL length for GET queries because the URL length can be in the
-  // thousands when rich metadata is enabled.
-  if (request_data.request_type == CrowdsourcingRequestType::kRequestQuery &&
-      method == "GET") {
-    base::UmaHistogramCounts100000(kUmaGetUrlLength,
-                                   request_url.spec().length());
-  }
-
-  auto resource_request = std::make_unique<network::ResourceRequest>();
-  resource_request->url = request_url;
-  resource_request->credentials_mode = network::mojom::CredentialsMode::kOmit;
-  resource_request->method = method;
-
-  if (request_data.isolation_info) {
-    resource_request->trusted_params =
-        network::ResourceRequest::TrustedParams();
-    resource_request->trusted_params->isolation_info =
-        *request_data.isolation_info;
-  }
-
-  // Add Chrome experiment state to the request headers.
-  variations::AppendVariationsHeaderUnknownSignedIn(
-      request_url,
-      client_->IsOffTheRecord() ? variations::InIncognito::kYes
-                                : variations::InIncognito::kNo,
-      resource_request.get());
-
-  // Set headers specific to the API.
-  // Encode response serialized proto in base64 for safety.
-  resource_request->headers.SetHeader(kGoogEncodeResponseIfExecutable,
-                                      "base64");
-
-  // Add API key to the request if a key exists, and the endpoint is trusted by
-  // Google.
-  if (!api_key_.empty() && request_url.SchemeIs(url::kHttpsScheme) &&
-      google_util::IsGoogleAssociatedDomainUrl(request_url)) {
-    google_apis::AddAPIKeyToRequest(*resource_request, api_key_);
-  }
-
-  auto simple_loader = network::SimpleURLLoader::Create(
-      std::move(resource_request),
-      GetNetworkTrafficAnnotation(request_data.request_type));
-
-  // This allows reading the error message within the API response when status
-  // is not 200 (e.g., 400). Otherwise, URL loader will not give any content in
-  // the response when there is a failure, which makes debugging hard.
-  simple_loader->SetAllowHttpErrorResults(true);
-
-  if (method == "POST") {
-    static constexpr char content_type[] = "application/x-protobuf";
-    std::optional<std::string> payload =
-        GetAPIBodyPayload(request_data.payload, request_data.request_type);
-    if (!payload) {
-      return false;
-    }
-    // Attach payload data and add data format header.
-    simple_loader->AttachStringForUpload(std::move(payload).value(),
-                                         content_type);
-  }
-
-  // Transfer ownership of the loader into url_loaders_. Temporarily hang
-  // onto the raw pointer to use it as a key and to kick off the request;
-  // transferring ownership (std::move) invalidates the `simple_loader`
-  // variable.
-  auto* raw_simple_loader = simple_loader.get();
-  url_loaders_.push_back(std::move(simple_loader));
-  raw_simple_loader->SetTimeoutDuration(kFetchTimeout);
-  raw_simple_loader->DownloadToStringOfUnboundedSizeUntilCrashAndDie(
-      client_->GetURLLoaderFactory().get(),
-      base::BindOnce(&AutofillCrowdsourcingManager::OnSimpleLoaderComplete,
-                     base::Unretained(this), std::move(--url_loaders_.end()),
-                     std::move(request_data), base::TimeTicks::Now()));
   return true;
 }
 
