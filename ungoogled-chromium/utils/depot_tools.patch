# Changes to gclient that:
#   use system python on windows
#   move dotfiles into the staging directory
#   skip cipd binary downloads
#   skip gcs downloads unless its an allowed sysroot
#   replace 'src' in checkout paths with the output directory
#   add flag to specify an allowed sysroot
#   ensure shallow fetches
#   utilize a newer version of gsutil to support later versions of python
--- a/gclient.bat
+++ b/gclient.bat
@@ -20,4 +20,4 @@ IF %ERRORLEVEL% NEQ 0 (
 set PATH=%PATH%;%~dp0
 
 :: Defer control.
-call vpython3 "%~dp0gclient.py" %*
+call python3 "%~dp0gclient.py" %*
--- a/gclient.py
+++ b/gclient.py
@@ -126,8 +126,8 @@ DEPOT_TOOLS_DIR = os.path.dirname(os.pat
 # one, e.g. if a spec explicitly says `cache_dir = None`.)
 UNSET_CACHE_DIR = object()
 
-PREVIOUS_CUSTOM_VARS_FILE = '.gclient_previous_custom_vars'
-PREVIOUS_SYNC_COMMITS_FILE = '.gclient_previous_sync_commits'
+PREVIOUS_CUSTOM_VARS_FILE = r'UC_STAGING'+os.sep+'.gclient_previous_custom_vars'
+PREVIOUS_SYNC_COMMITS_FILE = r'UC_STAGING'+os.sep+'.gclient_previous_sync_commits'
 
 PREVIOUS_SYNC_COMMITS = 'GCLIENT_PREVIOUS_SYNC_COMMITS'
 
@@ -424,6 +424,7 @@ class Dependency(gclient_utils.WorkItem,
                  protocol='https',
                  git_dependencies_state=gclient_eval.DEPS,
                  print_outbuf=False):
+        if name and name[0:3] == "src": name = r"UC_OUT"+name[3:]
         gclient_utils.WorkItem.__init__(self, name)
         DependencySettings.__init__(self, parent, url, managed, custom_deps,
                                     custom_vars, custom_hooks, deps_file,
@@ -769,6 +770,7 @@ class Dependency(gclient_utils.WorkItem,
 
             condition = dep_value.get('condition')
             dep_type = dep_value.get('dep_type')
+            if dep_type == 'cipd': continue
 
             if not self._get_option('process_all_deps', False):
                 should_process = should_process and _should_process(condition)
@@ -820,6 +822,12 @@ class Dependency(gclient_utils.WorkItem,
                     should_process_object = should_process and _should_process(
                         merged_condition)
 
+                    if name != "src/third_party/node/node_modules" and \
+                    (not name.startswith("src/build/linux/") or \
+                    not f"{self._get_option('sysroot', 'None')}-sysroot" in name):
+                        continue
+                    should_process_object = True
+                    merged_condition = 'True'
                     gcs_deps.append(
                         GcsDependency(parent=self,
                                       name=name,
@@ -931,6 +939,8 @@ class Dependency(gclient_utils.WorkItem,
 
         self._gn_args_from = local_scope.get('gclient_gn_args_from')
         self._gn_args_file = local_scope.get('gclient_gn_args_file')
+        if self._gn_args_file and self._gn_args_file[0:3] == "src":
+            self._gn_args_file = r"UC_OUT"+self._gn_args_file[3:]
         self._gn_args = local_scope.get('gclient_gn_args', [])
         # It doesn't make sense to set all of these, since setting gn_args_from
         # to another DEPS will make gclient ignore any other local gn_args*
@@ -3983,6 +3993,7 @@ def CMDsync(parser, args):
                       default=[],
                       help='Specify to skip processing of a certain type of '
                       'dep.')
+    parser.add_option('--sysroot')
     (options, args) = parser.parse_args(args)
     client = GClient.LoadCurrentConfig(options)
 
--- a/gclient_scm.py
+++ b/gclient_scm.py
@@ -979,8 +979,7 @@ class GitWrapper(SCMWrapper):
         self._SetFetchConfig(options)
 
         # Fetch upstream if we don't already have |revision|.
-        if not scm.GIT.IsValidRevision(
-                self.checkout_path, revision, sha_only=True):
+        if False:
             self._Fetch(options, prune=options.force)
 
             if not scm.GIT.IsValidRevision(
@@ -996,7 +995,7 @@ class GitWrapper(SCMWrapper):
 
         # This is a big hammer, debatable if it should even be here...
         if options.force or options.reset:
-            target = 'HEAD'
+            target = 'FETCH_HEAD'
             if options.upstream and upstream_branch:
                 target = upstream_branch
             self._Scrub(target, options)
@@ -1011,7 +1010,6 @@ class GitWrapper(SCMWrapper):
             # to the checkout step.
             if not (options.force or options.reset):
                 self._CheckClean(revision)
-            self._CheckDetachedHead(revision, options)
 
             if not current_revision:
                 current_revision = self._Capture(
@@ -1702,8 +1700,7 @@ class GitWrapper(SCMWrapper):
             fetch_cmd.append('--no-tags')
         elif quiet:
             fetch_cmd.append('--quiet')
-        if depth:
-            fetch_cmd.append('--depth=' + str(depth))
+        fetch_cmd.append('--depth=1')
         self._Run(fetch_cmd, options, show_header=options.verbose, retry=True)
 
     def _SetFetchConfig(self, options):
--- a/gsutil.py
+++ b/gsutil.py
@@ -25,7 +25,7 @@ DEFAULT_BIN_DIR = os.path.join(THIS_DIR,
 
 IS_WINDOWS = os.name == 'nt'
 
-VERSION = '4.68'
+VERSION = 'GSUVER'
 
 # Google OAuth Context required by gsutil.
 LUCI_AUTH_SCOPES = [
