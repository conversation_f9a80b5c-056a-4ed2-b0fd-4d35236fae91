name: New version check

on:
  # enabling manual trigger
  workflow_dispatch:
  # running every hour
  schedule:
    - cron: '48 * * * *'

jobs:
  check:
    # do not run in forks
    if: github.repository == 'ungoogled-software/ungoogled-chromium'
    runs-on: ubuntu-latest
    steps:
      - name: Set maintainer groups
        id: maintainers
        run: |
          echo "all=@networkException" >> $GITHUB_OUTPUT
          echo "linux=@rany2 @clickot @emilylange" >> $GITHUB_OUTPUT
          echo "windows=" >> $GITHUB_OUTPUT
          echo "macos=" >> $GITHUB_OUTPUT
      - name: Get the latest Chromium version
        id: latest-version
        run: |
          set -eo pipefail
          BASE_URL="https://versionhistory.googleapis.com/v1/chrome/platforms"
          END_URL="channels/stable/versions/all/releases?filter=endtime%3Dnone%2Cfraction%3E%3D0.5&order_by=version%20desc"
          JQ_FILTER='if .releases | select(type=="array") | length > 0 then .releases | first | .version else "null" end'
          for platform in linux win mac; do
            printf %s "${platform}_version=" >> $GITHUB_OUTPUT
            curl -sf "${BASE_URL}/${platform}/${END_URL}" | jq -re "${JQ_FILTER}" >> $GITHUB_OUTPUT
          done
      - uses: actions/checkout@v3
      - name: Create Issue for all platforms
        if: |
          contains(steps.latest-version.outputs.win_version, steps.latest-version.outputs.mac_version) &&
          contains(steps.latest-version.outputs.mac_version, steps.latest-version.outputs.linux_version) &&
          !contains(steps.latest-version.outputs.linux_version, 'null')
        uses: dblock/create-a-github-issue@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VERSION: ${{ steps.latest-version.outputs.linux_version }}
          PLATFORM: all platforms
          NOTIFY_MAINTAINERS: "${{ steps.maintainers.outputs.all }} ${{ steps.maintainers.outputs.linux }} ${{ steps.maintainers.outputs.windows }} ${{ steps.maintainers.outputs.macos }}"
        with:
          update_existing: false
          search_existing: all
          filename:  .github/ISSUE_TEMPLATE/create-an--updating-to-chromium-x-x-x-x-.md
      - name: Create Issue for Linux
        if: |
          (
            !contains(steps.latest-version.outputs.win_version, steps.latest-version.outputs.mac_version) ||
            !contains(steps.latest-version.outputs.mac_version, steps.latest-version.outputs.linux_version)
          ) && !contains(steps.latest-version.outputs.linux_version, 'null')
        uses: dblock/create-a-github-issue@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VERSION: ${{ steps.latest-version.outputs.linux_version }}
          PLATFORM: Linux
          NOTIFY_MAINTAINERS: "${{ steps.maintainers.outputs.all }} ${{ steps.maintainers.outputs.linux }}"
        with:
          update_existing: false
          search_existing: all
          filename:  .github/ISSUE_TEMPLATE/create-an--updating-to-chromium-x-x-x-x-.md
      - name: Create Issue for macOS
        if: |
          (
            !contains(steps.latest-version.outputs.win_version, steps.latest-version.outputs.mac_version) ||
            !contains(steps.latest-version.outputs.mac_version, steps.latest-version.outputs.linux_version)
          ) && !contains(steps.latest-version.outputs.mac_version, 'null')
        uses: dblock/create-a-github-issue@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VERSION: ${{ steps.latest-version.outputs.mac_version }}
          PLATFORM: macOS
          NOTIFY_MAINTAINERS: "${{ steps.maintainers.outputs.all }} ${{ steps.maintainers.outputs.macos }}"
        with:
          update_existing: false
          search_existing: all
          filename:  .github/ISSUE_TEMPLATE/create-an--updating-to-chromium-x-x-x-x-.md
      - name: Create Issue for Windows
        if: |
          (
            !contains(steps.latest-version.outputs.win_version, steps.latest-version.outputs.mac_version) ||
            !contains(steps.latest-version.outputs.mac_version, steps.latest-version.outputs.linux_version)
          ) && !contains(steps.latest-version.outputs.win_version, 'null')
        uses: dblock/create-a-github-issue@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VERSION: ${{ steps.latest-version.outputs.win_version }}
          PLATFORM: Windows
          NOTIFY_MAINTAINERS: "${{ steps.maintainers.outputs.all }} ${{ steps.maintainers.outputs.windows }}"
        with:
          update_existing: false
          search_existing: all
          filename:  .github/ISSUE_TEMPLATE/create-an--updating-to-chromium-x-x-x-x-.md
