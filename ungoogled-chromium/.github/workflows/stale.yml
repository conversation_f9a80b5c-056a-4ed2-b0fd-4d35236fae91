name: 'Close stale issues and PRs'

on:
    schedule:
        - cron: '30 1 * * *'

permissions:
    issues: write
    pull-requests: write

jobs:
    stale:
        runs-on: ubuntu-latest
        steps:
          - uses: actions/stale@v6
            with:
                exempt-all-milestones: true
                any-of-labels: 'need info'
                days-before-stale: 30
                days-before-close: 7
                stale-issue-message: "This issue has been automatically marked as stale as there has been no recent activity in response to our request for more information. Please respond so that we can proceed with this issue."
                close-issue-message: "This issue has been automatically closed as sufficient information hasn't been provided on the issue for further actions to be taken. Feel free to add more information."
