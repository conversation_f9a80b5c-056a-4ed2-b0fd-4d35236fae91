name: Feature request
description: Suggest an idea
labels: ["enhancement", "help wanted"]
body:
  - type: markdown
    attributes:
      value: |
        Before submitting this feature request, please confirm if you should submit it to a [platform-specific issue tracker instead](https://github.com/ungoogled-software/ungoogled-chromium/blob/master/SUPPORT.md#platform-specific-support)

        Otherwise, your issue may not be noticed. In addition, please read through the [SUPPORT.md](https://github.com/ungoogled-software/ungoogled-chromium/blob/master/SUPPORT.md) first
  - type: input
    id: description
    attributes:
      label: Description
      description: A clear and concise description (in one line) of what your suggestion is
    validations:
      required: true
  - type: checkboxes
    attributes:
      label: Who's implementing?
      options:
        - label: I'm willing to implement this feature myself
  - type: textarea
    id: prob
    attributes:
      label: The problem
      description: Please describe the problem you are solving or new feature you're suggesting
      placeholder: I'm always frustrated when [...] happens
    validations:
      required: true
  - type: textarea
    id: sol
    attributes:
      label: Possible solutions
      description: Please describe possible solution(-s) to The Problem
    validations:
      required: true
  - type: textarea
    id: alt
    attributes:
      label: Alternatives
      description: Please describe alternatives you've considered, if any
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: Add any other context about the feature request here
