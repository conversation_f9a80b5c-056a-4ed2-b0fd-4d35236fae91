name: Bug Report
description: Report a bug building or running ungoogled-chromium
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        Before submitting this issue, please confirm if you should submit it to a [platform-specific issue tracker instead](https://github.com/ungoogled-software/ungoogled-chromium/blob/master/SUPPORT.md#platform-specific-support)

        Otherwise, your issue may not be noticed. In addition, please read through the [SUPPORT.md](https://github.com/ungoogled-software/ungoogled-chromium/blob/master/SUPPORT.md) first
  - type: dropdown
    id: os
    attributes:
      label: OS/Platform
      description: OS/Platform you are running ungoogled-chromium on
      options:
          - Android
          - Arch Linux
          - Debian, Ubuntu, and derivatives
          - Fedora and CentOS
          - Gentoo
          - GNU Guix
          - macOS
          - NixOS
          - Other Linux (please specify)
          - Portable Linux
          - Slackware
          - Windows
    validations:
        required: true
  - type: dropdown
    id: install
    attributes:
      label: Installed
      description: How was ungoogled-chromium installed on your OS/Platform
      options:
          - Compiled from source
          - OS/Platform's package manager
          - Flatpak
          - https://ungoogled-software.github.io/ungoogled-chromium-binaries/
          - Other (please use 'Additional context' field below to mention how exactly)
    validations:
        required: true
  - type: input
    id: version
    attributes:
      label: Version
      description: ungoogled-chromium version. Could be copied from chrome://settings/help
    validations:
      required: true
  - type: checkboxes
    attributes:
      label: Have you tested that this is not an upstream issue or an issue with your configuration?
      options:
        - label: I have tried reproducing this issue in Chrome and it could not be reproduced there
        - label: I have tried reproducing this issue in vanilla Chromium and it could not be reproduced there
        - label: I have tried reproducing this issue in ungoogled-chromium with a new and empty profile using `--user-data-dir` command line argument and it could not be reproduced there
  - type: input
    id: description
    attributes:
      label: Description
      description: A clear and concise description (in one line) of what the bug is
    validations:
      required: true
  - type: textarea
    id: repro
    attributes:
      label: How to Reproduce?
      description: Steps to reproduce the behaviour
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
  - type: textarea
    id: actual
    attributes:
      label: Actual behaviour
      description: A clear and concise description of what happens
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected behaviour
      description: A clear and concise description of what you expected to happen
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: sh
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: Add any other context about the problem here. If applicable, add screenshots to help explain your problem.
