# arm64 (Apple Silicon) specific dependencies not included in the main Chromium source archive

# Pre-built LLVM toolchain for convenience
[llvm]
version = bd809ffb4b5f277a661509fbbbf9ea893a545ab0
url = https://github.com/dumbmoron/llvm-macos-buildbot/releases/download/%(version)s-arm64/clang+llvm-%(version)s-arm64-apple-darwin21.0.tar.xz
download_filename = clang+llvm-%(version)s-arm64-apple-darwin21.0.tar.xz
strip_leading_dirs = clang+llvm-%(version)s-arm64-apple-darwin21.0
sha512 = 64ebdb038a8e0444e50032951775e75e40ae7a8cc8cdaa3d5148060666419ffee0d4579e9a400f5f945bd4e7e2540ff65e650df54eaada4f17eea75de3a4e0df
output_path = third_party/llvm-build/Release+Asserts

[nodejs]
version = 22.11.0
url = https://nodejs.org/dist/v%(version)s/node-v%(version)s-darwin-arm64.tar.xz
download_filename = node-v%(version)s-darwin-arm64.tar.xz
strip_leading_dirs = node-v%(version)s-darwin-arm64
sha512 = 1ba7ec05c1445c03d561cc9acc50d64446bf71ae54c657fecb2ee1cfaa3739906ee8e018d40affc0f8165a124f9181214ba19455a18495619afef0d659a8c53a
output_path = third_party/node/mac_arm64/node-darwin-arm64

[rust]
version = 2025-06-23
url = https://static.rust-lang.org/dist/%(version)s/rust-nightly-aarch64-apple-darwin.tar.xz
download_filename = rust-nightly-%(version)s-aarch64-apple-darwin.tar.xz
output_path = third_party/rust-toolchain
strip_leading_dirs = rust-nightly-aarch64-apple-darwin
