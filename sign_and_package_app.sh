_root_dir="$(dirname "$(greadlink -f "$0")")"

# For packaging
_chromium_version=$(cat "$_root_dir"/ungoogled-chromium/chromium_version.txt)
_ungoogled_revision=$(cat "$_root_dir"/ungoogled-chromium/revision.txt)
_package_revision=$(cat "$_root_dir"/revision.txt)

# Optional branding inputs
# Priority: env var MACOS_BRAND_NAME > branding/name.txt > default "Chromium"
_BRAND_NAME_DEFAULT="Chromium"
_BRAND_NAME="${MACOS_BRAND_NAME:-}"
if [[ -z "$_BRAND_NAME" && -f "$_root_dir/branding/name.txt" ]]; then
  _BRAND_NAME="$(cat "$_root_dir/branding/name.txt" | tr -d '\n' )"
fi
if [[ -z "$_BRAND_NAME" ]]; then
  _BRAND_NAME="$_BRAND_NAME_DEFAULT"
fi
# Safe DMG base name from brand (spaces → underscores)
_DMG_NAME_SAFE="${_BRAND_NAME// /_}"

# App bundle renaming and bundle identifier
_SRC_APP="out/Default/Chromium.app"
_APP_BUNDLE_NAME_DEFAULT="Iradium.app"
_APP_BUNDLE_NAME="${MACOS_APP_BUNDLE_NAME:-$_APP_BUNDLE_NAME_DEFAULT}"
_APP_PATH="out/Default/${_APP_BUNDLE_NAME}"

_BUNDLE_ID_DEFAULT="com.integrofy.iradium"
_BUNDLE_ID="${MACOS_BUNDLE_ID:-$_BUNDLE_ID_DEFAULT}"

# If built app exists, rename it to target bundle name
if [[ -d "$_SRC_APP" && "$_SRC_APP" != "$_APP_PATH" ]]; then
  rm -rf "$_APP_PATH" 2>/dev/null || true
  mv -f "$_SRC_APP" "$_APP_PATH"
fi


# Fix issue where macOS requests permission for incoming network connections
# See https://github.com/ungoogled-software/ungoogled-chromium-macos/issues/17
xattr -cs "$_APP_PATH" || true

# If the app exists, optionally replace the icon and update Info.plist before signing
if [[ -d "$_APP_PATH" ]]; then
  if [[ -f "$_root_dir/branding/app.icns" ]]; then
    echo "Using custom icon: $_root_dir/branding/app.icns"
    cp -f "$_root_dir/branding/app.icns" "$_APP_PATH/Contents/Resources/app.icns"
    touch "$_APP_PATH/Contents/Resources/app.icns"
  fi
  # Update bundle display name and identifier (non-fatal if keys differ)
  /usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName $_BRAND_NAME" -c "Set :CFBundleName $_BRAND_NAME" -c "Set :CFBundleIdentifier $_BUNDLE_ID" "$_APP_PATH/Contents/Info.plist" || true

  # Update helper bundle identifiers if present
  _HELPERS_DIR="$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Helpers"
  if [[ -d "$_HELPERS_DIR" ]]; then
    declare -A _HELPERS_IDS=(
      ["Chromium Helper.app"]="${_BUNDLE_ID}.helper"
      ["Chromium Helper (Renderer).app"]="${_BUNDLE_ID}.helper.renderer"
      ["Chromium Helper (GPU).app"]="${_BUNDLE_ID}.helper.gpu"
      ["Chromium Helper (Plugin).app"]="${_BUNDLE_ID}.helper.plugin"
      ["Chromium Helper (Alerts).app"]="${_BUNDLE_ID}.framework.AlertNotificationService"
    )
    for _h in "${!_HELPERS_IDS[@]}"; do
      if [[ -f "$_HELPERS_DIR/$_h/Contents/Info.plist" ]]; then
        /usr/libexec/PlistBuddy -c "Set :CFBundleIdentifier ${_HELPERS_IDS[$_h]}" "$_HELPERS_DIR/$_h/Contents/Info.plist" || true
      fi
    done
  fi

  # Update framework bundle identifier if present
  _FRAMEWORK_PLIST="$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Resources/Info.plist"
  if [[ -f "$_FRAMEWORK_PLIST" ]]; then
    /usr/libexec/PlistBuddy -c "Set :CFBundleIdentifier ${_BUNDLE_ID}.framework" "$_FRAMEWORK_PLIST" || true
  fi
fi

# Sign the binary
_HELPER_BASE_ID="${_BUNDLE_ID}.helper"
_HELPER_RENDERER_ID="${_BUNDLE_ID}.helper.renderer"
_HELPER_GPU_ID="${_BUNDLE_ID}.helper.gpu"
_HELPER_PLUGIN_ID="${_BUNDLE_ID}.helper.plugin"
_FRAMEWORK_ID="${_BUNDLE_ID}.framework"
_ALERTS_ID="${_BUNDLE_ID}.framework.AlertNotificationService"

# Select identity (ad-hoc when requested or when no certificate provided)
CODESIGN_ID="$MACOS_CERTIFICATE_NAME"
if [[ "$MACOS_ADHOC_SIGN" == "true" || -z "$MACOS_CERTIFICATE_NAME" ]]; then
  echo "Ad-hoc signing enabled: using '-' identity and skipping notarization."
  CODESIGN_ID="-"
  SKIP_NOTARIZE=1
fi

codesign --sign "$CODESIGN_ID" --force --timestamp --identifier chrome_crashpad_handler --options=restrict,library,runtime,kill "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Helpers/chrome_crashpad_handler"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier "$_HELPER_BASE_ID" --options restrict,library,runtime,kill "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Helpers/Chromium Helper.app"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier "$_HELPER_RENDERER_ID" --options restrict,kill,runtime --entitlements $_root_dir/entitlements/helper-renderer-entitlements.plist "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Helpers/Chromium Helper (Renderer).app"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier "$_HELPER_GPU_ID" --options restrict,kill,runtime --entitlements $_root_dir/entitlements/helper-gpu-entitlements.plist "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Helpers/Chromium Helper (GPU).app"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier "$_HELPER_PLUGIN_ID" --options restrict,kill,runtime --entitlements $_root_dir/entitlements/helper-plugin-entitlements.plist "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Helpers/Chromium Helper (Plugin).app"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier "$_ALERTS_ID" --options restrict,library,runtime,kill "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Helpers/Chromium Helper (Alerts).app"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier app_mode_loader --options restrict,library,runtime,kill "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Helpers/app_mode_loader"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier web_app_shortcut_copier --options restrict,library,runtime,kill "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Helpers/web_app_shortcut_copier"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier libEGL "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Libraries/libEGL.dylib"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier libGLESv2 "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Libraries/libGLESv2.dylib"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier libvk_swiftshader "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework/Libraries/libvk_swiftshader.dylib"
codesign --sign "$CODESIGN_ID" --force --timestamp --identifier "$_FRAMEWORK_ID" "$_APP_PATH/Contents/Frameworks/Chromium Framework.framework"

# Main app signing (different requirements for ad-hoc vs certificate)
if [[ "$CODESIGN_ID" == "-" ]]; then
  codesign --sign "$CODESIGN_ID" --force --timestamp --identifier "$_BUNDLE_ID" --options restrict,library,runtime,kill --entitlements $_root_dir/entitlements/app-entitlements.plist "$_APP_PATH"
else
  codesign --sign "$CODESIGN_ID" --force --timestamp --identifier "$_BUNDLE_ID" --options restrict,library,runtime,kill --entitlements $_root_dir/entitlements/app-entitlements.plist --requirements "=designated => identifier \"$_BUNDLE_ID\" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */" "$_APP_PATH"
fi

# Verify the binary signature
codesign --verify --deep --verbose=4 "$_APP_PATH"

# Notarization (skipped when ad-hoc)
if [[ -z "$SKIP_NOTARIZE" ]]; then
  # Prepare app notarization
  ditto -c -k --keepParent "$_APP_PATH" "notarize.zip"

  # Notarize the app
  xcrun notarytool store-credentials "notarytool-profile" --apple-id "$PROD_MACOS_NOTARIZATION_APPLE_ID" --team-id "$PROD_MACOS_NOTARIZATION_TEAM_ID" --password "$PROD_MACOS_NOTARIZATION_PWD"
  xcrun notarytool submit "notarize.zip" --keychain-profile "notarytool-profile" --wait
  xcrun stapler staple "$_APP_PATH"
fi

# Package the app
chrome/installer/mac/pkg-dmg \
  --sourcefile --source "$_APP_PATH" \
  --target "$_root_dir/build/${_DMG_NAME_SAFE}_${_chromium_version}-${_ungoogled_revision}.${_package_revision}_macos.dmg" \
  --volname "$_BRAND_NAME" --symlink /Applications:/Applications \
  --format UDBZ --verbosity 2
