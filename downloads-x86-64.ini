# x86-64 (Intel) specific dependencies not included in the main Chromium source archive

# Pre-built LLVM toolchain for convenience
[llvm]
version = bd809ffb4b5f277a661509fbbbf9ea893a545ab0
url = https://github.com/dumbmoron/llvm-macos-buildbot/releases/download/%(version)s-x86-64/clang+llvm-%(version)s-x86-64-apple-darwin21.0.tar.xz
download_filename = clang+llvm-%(version)s-x86-64-apple-darwin21.0.tar.xz
strip_leading_dirs = clang+llvm-%(version)s-x86-64-apple-darwin21.0
sha512 = 979e514bb1741e1871128ed4dcdf600d97e48aaa746e337f246b1dd9f30a0f88833c02f31cefe507784108eeee854b3e611ad94bf4a22212dbd5fb7fe2875b11
output_path = third_party/llvm-build/Release+Asserts

[nodejs]
version = 22.11.0
url = https://nodejs.org/dist/v%(version)s/node-v%(version)s-darwin-x64.tar.xz
download_filename = node-v%(version)s-darwin-x64.tar.xz
strip_leading_dirs = node-v%(version)s-darwin-x64
sha512 = 0fdd6978268f8f7f6d3dd2a4f965eb7dbf0a4e0d5560fa7f6da58a65b7b75ab51ac209ba17779ffb8506cc5d64887ae03b68fea68d78e796cd25ceac583c24c6
output_path = third_party/node/mac/node-darwin-x64

[rust]
version = 2025-06-23
url = https://static.rust-lang.org/dist/%(version)s/rust-nightly-x86_64-apple-darwin.tar.xz
download_filename = rust-nightly-%(version)s-x86_64-apple-darwin.tar.xz
output_path = third_party/rust-toolchain
strip_leading_dirs = rust-nightly-x86_64-apple-darwin
