name: Building macOS binaries of Ungoogled Chromium

defaults:
  run:
    shell: bash

on:
  workflow_call:
    inputs:
      arch:
        required: true
        type: string
      os:
        required: true
        type: string

env:
  MACOS_CERTIFICATE: ${{ secrets.PROD_MACOS_CERTIFICATE }}
  MACOS_CERTIFICATE_PWD: ${{ secrets.PROD_MACOS_CERTIFICATE_PWD }}
  MACOS_CERTIFICATE_NAME: ${{ secrets.PROD_MACOS_CERTIFICATE_NAME }}
  MACOS_CI_KEYCHAIN_PWD: ${{ secrets.PROD_MACOS_CI_KEYCHAIN_PWD }}
  PROD_MACOS_NOTARIZATION_APPLE_ID: ${{ secrets.PROD_MACOS_NOTARIZATION_APPLE_ID }}
  PROD_MACOS_NOTARIZATION_TEAM_ID: ${{ secrets.PROD_MACOS_NOTARIZATION_TEAM_ID }}
  PROD_MACOS_NOTARIZATION_PWD: ${{ secrets.PROD_MACOS_NOTARIZATION_PWD }}

jobs:
  retrieve-resources:
    name: Retrieve resources required for building
    runs-on: ${{ inputs.os }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./
      - name: Disable Spotlight
        run: sudo mdutil -a -i off
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Download and unpack required resources
        run: ./github_fetch_resources.sh ${{ inputs.arch }} | tee -a github_actions_retrieve_resources.log
      - name: List resources
        run: ls -la
      - name: Archive resources
        run: ./github_pack_resources.sh | tee -a github_actions_retrieve_resources.log
      - name: Upload resources
        uses: actions/upload-artifact@v4
        with:
          name: ungoogled_chromium_macos_resources_${{ inputs.arch }}
          path: upload_build_resources/

  build_job_01:
    name: Start Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: retrieve-resources
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable Spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Download resources
        uses: actions/download-artifact@v4
        with:
          name: ungoogled_chromium_macos_resources_${{ inputs.arch }}
      - name: Unpack resources
        run: ./github_unpack_resources.sh
      - name: List resources
        run: ls -la
      - name: Prepare for building
        run: ./github_before_build.sh ${{ inputs.arch }} | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_02:
    name: Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_01
    if: ${{needs.build_job_01.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_03:
    name: Further Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_02
    if: ${{needs.build_job_02.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download resumed build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_04:
    name: Further Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_03
    if: ${{needs.build_job_03.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download resumed build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_05:
    name: Further Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_04
    if: ${{needs.build_job_04.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download resumed build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_06:
    name: Further Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_05
    if: ${{needs.build_job_05.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download resumed build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_07:
    name: Further Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_06
    if: ${{needs.build_job_06.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download resumed build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_08:
    name: Further Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_07
    if: ${{needs.build_job_07.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download resumed build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_09:
    name: Further Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_08
    if: ${{needs.build_job_08.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download resumed build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_10:
    name: Further Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_09
    if: ${{needs.build_job_09.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download resumed build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_11:
    name: Further Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_10
    if: ${{needs.build_job_10.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download resumed build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md

  build_job_12:
    name: Further Resuming Building Ungoogled-Chromium for macOS
    runs-on: ${{ inputs.os }}
    needs: build_job_11
    if: ${{needs.build_job_11.outputs.status == 'running'}}
    outputs:
      status: ${{ steps.build.outputs.status }}
    steps:
      - name: Cleanup Xcode installations
        run: sudo mv /Applications/Xcode_16.4.app /Applications/tmp_Xcode_16.4.app ; sudo rm -rf /Applications/Xcode* ; sudo mv /Applications/tmp_Xcode_16.4.app /Applications/Xcode_16.4.app
      - name: Select Xcode version
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Cleanup Xcode Simulators
        run: sudo xcrun simctl delete all
      - name: Cleanup Android Related Stuff
        run: sudo rm -rf $ANDROID_HOME
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./ ; date +%s > ./epoch_job_start.txt
      - name: Disable spotlight
        run: sudo mdutil -a -i off
      - name: Run xcode-select
        run: sudo xcode-select --switch /Applications/Xcode_16.4.app
      - name: Get previous logs
        uses: actions/download-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
      - name: Download resumed build
        uses: actions/download-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
      - name: Setup Environment and Toolchain
        run: ./github_setup_env_toolchain.sh ${{ inputs.arch }} | tee -a github_actions_setup_env_toolchain.log
      - name: Unpack archive of build
        run: ./github_unpack_archive.sh
      - name: Resume Build
        id: build
        run: ./github_build.sh ${{ inputs.arch }} 2>&1 | tee -a github_actions_build_${{ inputs.arch }}.log
      - name: Prepare archive of build as artifact
        id: bake
        run: ./github_prepare_artifacts.sh ${{ inputs.arch }} | tee -a github_actions_upload_${{ inputs.arch }}.log
      - name: Upload part build artifact
        uses: actions/upload-artifact@v4
        with:
          name: github_build_artifact_${{ inputs.arch }}
          path: upload_part_build/
          overwrite: true
      - name: Upload logs
        uses: actions/upload-artifact@v4
        with:
          name: github_build_logs_${{ inputs.arch }}
          path: upload_logs/
          overwrite: true
      - name: Upload disk-image and hash file as artifact after build
        if: ${{ steps.build.outputs.status == 'finished' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.bake.outputs.file_name }}
          path: |
            ./release_asset/${{ steps.bake.outputs.file_name }}
            ./${{ steps.bake.outputs.file_name }}.hashes.md
