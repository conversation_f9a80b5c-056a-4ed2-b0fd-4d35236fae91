name: Build macOS binaries of Ungoogled Chromium
on:
  workflow_dispatch:
  push:
    paths:
      - 'patches/**'
      - 'downloads**.ini'
      - 'revision.txt'
      - 'flags.macos.gn'
      - '!build.sh'
      - '!README.md'
      - '!LICENSE'
      - '!devutils/**'
      - '.github/scripts/force_build.txt'

jobs:
  build:
    name: Build macOS binaries of Ungoogled Chromium
    strategy:
      matrix:
        arch: [arm64, x86_64]
      fail-fast: true
      max-parallel: 2
    uses: ./.github/workflows/building.yml
    secrets: inherit
    with:
      arch: ${{ matrix.arch }}
      os: macos-15

  release:
    needs: build
    if: github.event_name == 'push' && contains(github.ref, 'refs/tags/')
    name: Release macOS binaries of Ungoogled Chromium
    runs-on: macos-latest
    steps:
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Copy GitHub specific scripts to git-root folder
        run: cp -va ./.github/scripts/ ./
      - name: Disable Spotlight
        run: sudo mdutil -a -i off
      - name: Install coreutils
        run: brew install coreutils
      - name: Prepare release
        id: bake
        run: ./github_prepare_release.sh | tee -a github_actions_release.log
      - name: Get built x86-64 binary
        uses: actions/download-artifact@v4
        with:
          name: ${{ steps.bake.outputs.x64_file_name }}
      - name: Get built arm64 binary
        uses: actions/download-artifact@v4
        with:
          name: ${{ steps.bake.outputs.arm64_file_name }}
      - name: List files
        run: ls -la
      - name: Generate release note
        run: ./github_generate_release_note.sh | tee -a github_actions_release.log
      - name: Release Ungoogled-Chromium binaries
        uses: softprops/action-gh-release@4634c16e79c963813287e889244c50009e7f0981
        with:
          body_path: ./github_release_note.md
          draft: false
          prerelease: false
          files: |
            ./release_asset/${{ steps.bake.outputs.arm64_file_name }}
            ./release_asset/${{ steps.bake.outputs.x64_file_name }}
          name: ${{ steps.bake.outputs.release_name }}
          tag_name: ${{ steps.bake.outputs.release_tag_version }}
          token: ${{ secrets.GITHUB_TOKEN }}
